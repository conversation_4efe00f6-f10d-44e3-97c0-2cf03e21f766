{"extends": ["next/core-web-vitals", "@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/prefer-const": "error", "prefer-const": "off", "no-console": "warn", "no-debugger": "error"}, "ignorePatterns": ["node_modules/", ".next/", "dist/", "build/"]}