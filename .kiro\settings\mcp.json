{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "disabled": false, "autoApprove": ["resolve-library-id", "get-library-docs"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": ["sequentialthinking", "sequentialthinking"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "disabled": false, "autoApprove": []}}}