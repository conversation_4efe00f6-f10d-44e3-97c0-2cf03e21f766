# GuruFlow MVP - Design Document

## Overview

GuruFlow MVP is a modern school administration web platform built with Next.js 15 App Router, designed to digitize daily classroom operations for Indonesian schools. The system serves two primary user roles: Teachers (Guru) who manage classroom operations, and Students (Siswa) who access their academic information. The MVP focuses on core functionality including authentication, onboarding, dashboards, classroom management, and attendance tracking.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[Next.js 15 App Router]
        B[React Server Components]
        C[Client Components]
        D[Tailwind CSS + shadcn/ui]
    end
    
    subgraph "Server Layer"
        E[Next.js API Routes]
        F[Server Actions]
        G[Middleware]
    end
    
    subgraph "Data Layer"
        H[Drizzle ORM]
        I[Turso/libSQL Database]
    end
    
    subgraph "External Services"
        J[File Storage - R2/MinIO]
        K[PDF Generation]
    end
    
    A --> E
    B --> F
    C --> E
    E --> H
    F --> H
    H --> I
    E --> J
    F --> K
```

### Technology Stack

- **Frontend Framework**: Next.js 15 with App Router
- **UI Framework**: React 18 with Server Components
- **Styling**: Tailwind CSS v4 + shadcn/ui components
- **Database**: Turso (libSQL) with Drizzle ORM
- **Authentication**: Custom session-based auth with HTTP-only cookies
- **File Storage**: S3-compatible (Cloudflare R2 or MinIO)
- **PDF Generation**: react-pdf
- **Deployment**: Vercel (frontend), Turso Cloud (database)

## Components and Interfaces

### Core Components Structure

```
src/
├── app/                          # Next.js App Router
│   ├── (auth)/                   # Auth route group
│   │   ├── login/
│   │   └── onboarding/
│   ├── (dashboard)/              # Dashboard route group
│   │   ├── guru/                 # Teacher dashboard
│   │   └── siswa/                # Student dashboard
│   ├── api/                      # API routes
│   │   ├── auth/
│   │   ├── attendance/
│   │   └── classrooms/
│   └── globals.css
├── components/                   # Reusable UI components
│   ├── ui/                       # shadcn/ui components
│   ├── forms/                    # Form components
│   ├── dashboard/                # Dashboard-specific components
│   └── attendance/               # Attendance components
├── lib/                          # Utility functions
│   ├── auth.ts                   # Authentication utilities
│   ├── db.ts                     # Database connection
│   ├── session.ts                # Session management
│   └── utils.ts                  # General utilities
└── db/                           # Database schema and migrations
    ├── schema.ts                 # Drizzle schema definitions
    └── migrations/               # Migration files
```

### Key Component Interfaces

#### Authentication Components
- `LoginForm`: Handles email/password authentication
- `OnboardingWizard`: Multi-step school setup and admin creation
- `AuthGuard`: Route protection component

#### Dashboard Components
- `TeacherDashboard`: Teacher-specific dashboard layout
- `StudentDashboard`: Student-specific dashboard layout
- `ScheduleCard`: Display teaching schedule
- `QuickActions`: Shortcut buttons for common tasks
- `XPLeaderboard`: Gamification leaderboard display

#### Classroom Management Components
- `ClassroomList`: Display and manage classrooms
- `ClassroomForm`: Create/edit classroom details
- `StudentEnrollment`: Manage student enrollment
- `CSVImport`: Bulk student import functionality

#### Attendance Components
- `AttendanceGrid`: Daily attendance input interface
- `AttendanceHistory`: View historical attendance data
- `AttendanceExport`: Export attendance reports

## Data Models

### Database Schema Design

```typescript
// Core entities with relationships
export const schools = sqliteTable('schools', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  name: text('name').notNull(),
  logo: text('logo'),
  signature: text('signature'),
  level: text('level').notNull(), // SD, SMP, SMA
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`)
});

export const users = sqliteTable('users', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  email: text('email').notNull().unique(),
  passwordHash: text('password_hash').notNull(),
  role: text('role').notNull(), // 'teacher' | 'student'
  name: text('name').notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  isSuperAdmin: integer('is_super_admin', { mode: 'boolean' }).default(false),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`)
});

export const sessions = sqliteTable('sessions', {
  id: text('id').primaryKey(),
  userId: integer('user_id').notNull().references(() => users.id),
  expiresAt: text('expires_at').notNull(),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`)
});

export const classrooms = sqliteTable('classrooms', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  name: text('name').notNull(),
  grade: text('grade').notNull(),
  academicYear: text('academic_year').notNull(),
  homeroomTeacherId: integer('homeroom_teacher_id').references(() => users.id),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`)
});

export const subjects = sqliteTable('subjects', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  name: text('name').notNull(),
  code: text('code').notNull().unique(),
  description: text('description'),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`)
});

export const classroomSubjects = sqliteTable('classroom_subjects', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  classroomId: integer('classroom_id').notNull().references(() => classrooms.id),
  subjectId: integer('subject_id').notNull().references(() => subjects.id),
  teacherId: integer('teacher_id').notNull().references(() => users.id),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`)
});

export const students = sqliteTable('students', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  userId: integer('user_id').notNull().references(() => users.id),
  studentId: text('student_id').notNull().unique(),
  classroomId: integer('classroom_id').notNull().references(() => classrooms.id),
  enrollmentDate: text('enrollment_date').notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`)
});

export const attendanceRecords = sqliteTable('attendance_records', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  studentId: integer('student_id').notNull().references(() => students.id),
  classroomId: integer('classroom_id').notNull().references(() => classrooms.id),
  date: text('date').notNull(), // YYYY-MM-DD format
  status: text('status').notNull(), // 'H' | 'I' | 'S' | 'A'
  notes: text('notes'),
  markedBy: integer('marked_by').notNull().references(() => users.id),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`)
});

// XP and gamification tables for future use
export const studentXP = sqliteTable('student_xp', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  studentId: integer('student_id').notNull().references(() => students.id),
  totalXP: integer('total_xp').default(0),
  level: integer('level').default(1),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`)
});
```

### Data Relationships

```mermaid
erDiagram
    SCHOOLS ||--o{ USERS : "belongs_to"
    USERS ||--o{ SESSIONS : "has_many"
    USERS ||--o{ STUDENTS : "has_one"
    USERS ||--o{ CLASSROOM_SUBJECTS : "teaches"
    CLASSROOMS ||--o{ STUDENTS : "has_many"
    CLASSROOMS ||--o{ CLASSROOM_SUBJECTS : "has_many"
    CLASSROOMS ||--o{ ATTENDANCE_RECORDS : "has_many"
    SUBJECTS ||--o{ CLASSROOM_SUBJECTS : "has_many"
    STUDENTS ||--o{ ATTENDANCE_RECORDS : "has_many"
    STUDENTS ||--|| STUDENT_XP : "has_one"
```

## Error Handling

### Error Handling Strategy

1. **Client-Side Error Boundaries**
   - React Error Boundaries for component-level errors
   - Global error boundary for unhandled exceptions
   - User-friendly error messages in Indonesian

2. **Server-Side Error Handling**
   - Try-catch blocks in API routes and Server Actions
   - Structured error responses with appropriate HTTP status codes
   - Error logging for debugging and monitoring

3. **Database Error Handling**
   - Connection error recovery
   - Transaction rollback on failures
   - Constraint violation handling

4. **Authentication Error Handling**
   - Invalid credentials handling
   - Session expiration management
   - Unauthorized access prevention

### Error Response Format

```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
}

interface SuccessResponse<T> {
  success: true;
  data: T;
}
```

## Testing Strategy

### Testing Approach

1. **Unit Testing**
   - Utility functions testing
   - Database query testing
   - Authentication logic testing

2. **Integration Testing**
   - API route testing
   - Database integration testing
   - Authentication flow testing

3. **Component Testing**
   - React component testing with React Testing Library
   - Form validation testing
   - User interaction testing

4. **End-to-End Testing**
   - Critical user flows (login, attendance marking)
   - Cross-browser compatibility
   - Mobile responsiveness testing

### Testing Tools

- **Jest**: Unit and integration testing
- **React Testing Library**: Component testing
- **Playwright**: End-to-end testing
- **MSW (Mock Service Worker)**: API mocking

## Security Considerations

### Authentication & Authorization

1. **Session Management**
   - HTTP-only cookies for session storage
   - Secure session token generation
   - Session expiration and cleanup

2. **Password Security**
   - bcrypt for password hashing
   - Strong password requirements
   - Rate limiting for login attempts

3. **Role-Based Access Control**
   - Middleware for route protection
   - Component-level permission checks
   - API endpoint authorization

### Data Protection

1. **Input Validation**
   - Server-side validation for all inputs
   - SQL injection prevention through ORM
   - XSS protection with proper sanitization

2. **Data Privacy**
   - Student data access restrictions
   - Audit logging for sensitive operations
   - GDPR-compliant data handling

## Performance Optimization

### Frontend Optimization

1. **Next.js Optimizations**
   - Server Components for reduced client-side JavaScript
   - Static generation where possible
   - Image optimization with next/image

2. **Code Splitting**
   - Route-based code splitting
   - Component lazy loading
   - Dynamic imports for heavy components

3. **Caching Strategy**
   - Browser caching for static assets
   - API response caching
   - Database query caching

### Database Optimization

1. **Query Optimization**
   - Proper indexing strategy
   - Efficient query patterns with Drizzle
   - Connection pooling

2. **Data Structure**
   - Normalized database design
   - Appropriate data types
   - Efficient foreign key relationships

## Deployment Architecture

### Production Environment

```mermaid
graph LR
    A[Vercel Edge Network] --> B[Next.js Application]
    B --> C[Turso Database]
    B --> D[Cloudflare R2 Storage]
    E[Custom Domain] --> A
    F[SSL Certificate] --> A
```

### Environment Configuration

1. **Development Environment**
   - Local SQLite database
   - Local file storage
   - Hot reloading and debugging

2. **Production Environment**
   - Turso Cloud database
   - Cloudflare R2 storage
   - CDN for static assets
   - Environment variable management

### Monitoring and Logging

1. **Application Monitoring**
   - Error tracking and reporting
   - Performance monitoring
   - User analytics

2. **Database Monitoring**
   - Query performance tracking
   - Connection monitoring
   - Backup and recovery procedures

## Mobile Responsiveness

### Responsive Design Strategy

1. **Mobile-First Approach**
   - Tailwind CSS responsive utilities
   - Touch-friendly interface elements
   - Optimized form inputs for mobile

2. **Navigation Patterns**
   - Bottom navigation for mobile
   - Sidebar navigation for desktop
   - Hamburger menu for tablet

3. **Performance on Mobile**
   - Optimized bundle size
   - Efficient image loading
   - Minimal JavaScript for critical paths

This design provides a solid foundation for the GuruFlow MVP, focusing on scalability, security, and user experience while maintaining the Indonesian school context and requirements.