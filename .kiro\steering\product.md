# GuruFlow Product Overview

GuruFlow is a modern school administration web platform designed to simplify and digitize daily classroom operations for Indonesian schools. The platform serves two primary user roles: Teachers (Guru) who manage classroom operations, and Students (Siswa) who access their academic information and progress.

## Core Purpose
- Digitize traditional classroom management tasks
- Streamline attendance tracking using Indonesian standards (H/I/S/A codes)
- Enable efficient assignment and assessment workflows
- Provide gamification to motivate student engagement
- Generate reports and exports for administrative needs

## Key Features
- **Authentication & Onboarding**: Custom auth with school setup wizard
- **Role-Based Dashboards**: Separate interfaces for teachers and students
- **Classroom Management**: CRUD operations for classes, subjects, and students
- **Attendance System**: Daily tracking with Indonesian attendance codes
- **Assignment & Assessment**: Task creation, submissions, and grading
- **Gamification**: XP points, badges, and leaderboards
- **Reporting**: PDF and CSV exports for attendance and grades

## Target Users
- **Teachers (Guru)**: Both homeroom and subject teachers managing classroom tasks
- **Students (Siswa)**: Elementary to high school students accessing their academic dashboard
- **School Administrators**: Using teacher accounts with superadmin privileges

## Language & Localization
- Primary language: Bahasa Indonesia
- Indonesian education system standards (KKM, predikat grading)
- Local attendance codes and academic terminology