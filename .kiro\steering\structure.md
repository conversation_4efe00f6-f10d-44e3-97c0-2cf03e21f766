# GuruFlow Project Structure

## Root Directory Organization
```
├── .kiro/                        # Kiro IDE configuration
│   ├── steering/                 # AI assistant guidance files
│   └── specs/                    # Project specifications
├── src/                          # Main application source code
├── public/                       # Static assets
├── docs/                         # Project documentation
└── config files                  # Package.json, tsconfig, etc.
```

## Source Code Structure (`src/`)
```
src/
├── app/                          # Next.js App Router
│   ├── (auth)/                   # Authentication route group
│   │   ├── login/                # Login page
│   │   └── onboarding/           # School setup wizard
│   ├── (dashboard)/              # Protected dashboard routes
│   │   ├── guru/                 # Teacher dashboard and features
│   │   │   ├── page.tsx          # Teacher dashboard
│   │   │   ├── kelas/            # Classroom management
│   │   │   ├── absensi/          # Attendance management
│   │   │   └── laporan/          # Reports and exports
│   │   └── siswa/                # Student dashboard and features
│   │       ├── page.tsx          # Student dashboard
│   │       ├── tugas/            # Assignments view
│   │       └── nilai/            # Grades view
│   ├── api/                      # API routes
│   │   ├── auth/                 # Authentication endpoints
│   │   ├── classrooms/           # Classroom CRUD operations
│   │   ├── students/             # Student management
│   │   ├── attendance/           # Attendance operations
│   │   └── reports/              # Export and reporting
│   ├── globals.css               # Global styles
│   ├── layout.tsx                # Root layout
│   └── page.tsx                  # Landing page
├── components/                   # Reusable UI components
│   ├── ui/                       # shadcn/ui base components
│   ├── forms/                    # Form components
│   ├── dashboard/                # Dashboard-specific components
│   ├── attendance/               # Attendance components
│   ├── classroom/                # Classroom management components
│   └── layout/                   # Layout components (nav, sidebar)
├── lib/                          # Utility functions and configurations
│   ├── auth.ts                   # Authentication utilities
│   ├── db.ts                     # Database connection
│   ├── session.ts                # Session management
│   ├── utils.ts                  # General utilities
│   ├── validations.ts            # Form validation schemas
│   └── constants.ts              # Application constants
└── db/                           # Database schema and migrations
    ├── schema.ts                 # Drizzle schema definitions
    └── migrations/               # Migration files
```

## Component Organization Patterns

### UI Components (`components/ui/`)
- Base shadcn/ui components (button, input, card, etc.)
- Keep these as close to original shadcn/ui implementations
- Customize through Tailwind CSS classes, not component modifications

### Feature Components (`components/[feature]/`)
- Group components by feature area (attendance, classroom, etc.)
- Include both presentational and container components
- Follow naming convention: `FeatureComponent.tsx`

### Form Components (`components/forms/`)
- Reusable form components with validation
- Use React Hook Form with Zod validation
- Include error handling and loading states

## File Naming Conventions

### Pages and Routes
- Use lowercase with hyphens for route segments: `/guru/kelas-saya`
- Page components: `page.tsx`, `layout.tsx`, `loading.tsx`, `error.tsx`
- API routes: `route.ts`

### Components
- PascalCase for component files: `AttendanceGrid.tsx`
- Use descriptive names that indicate purpose
- Include component type in name when helpful: `LoginForm.tsx`

### Utilities and Libraries
- camelCase for utility files: `authUtils.ts`
- Descriptive names for purpose: `sessionManager.ts`
- Group related utilities in single files

## Route Organization

### Authentication Routes (`(auth)`)
- `/login` - User authentication
- `/onboarding` - First-time school setup
- Redirect authenticated users to appropriate dashboard

### Teacher Routes (`(dashboard)/guru`)
- `/guru` - Teacher dashboard
- `/guru/kelas` - Classroom management
- `/guru/siswa` - Student management
- `/guru/absensi` - Attendance tracking
- `/guru/tugas` - Assignment management
- `/guru/laporan` - Reports and exports

### Student Routes (`(dashboard)/siswa`)
- `/siswa` - Student dashboard
- `/siswa/tugas` - View assignments
- `/siswa/nilai` - View grades
- `/siswa/absensi` - View attendance history

## Database Schema Organization

### Core Tables
- `schools` - School configuration (singleton)
- `users` - All system users (teachers and students)
- `sessions` - Authentication sessions

### Academic Structure
- `classrooms` - Class definitions
- `subjects` - Subject/course definitions
- `classroom_subjects` - Many-to-many relationship with teacher assignment
- `students` - Student enrollment records

### Operational Data
- `attendance_records` - Daily attendance tracking
- `assignments` - Teacher-created tasks
- `submissions` - Student assignment submissions
- `grades` - Academic performance records

### Gamification
- `student_xp` - Experience points tracking
- `badges` - Achievement definitions
- `student_badges` - Earned achievements

## Import/Export Conventions

### Import Order
1. React and Next.js imports
2. Third-party library imports
3. Internal component imports
4. Utility and configuration imports
5. Type imports (with `type` keyword)

### Export Patterns
- Default exports for page components and main features
- Named exports for utilities and shared components
- Barrel exports in index files for clean imports

## Indonesian Language Integration

### Text Content
- All user-facing text in Bahasa Indonesia
- Use proper Indonesian educational terminology
- Maintain consistent terminology across the application

### Data Formats
- Indonesian date formats (DD/MM/YYYY)
- Indonesian number formatting
- Local academic year patterns (e.g., "2024/2025")

### Attendance Codes
- H (Hadir) - Present
- I (Izin) - Excused absence
- S (Sakit) - Sick
- A (Alpa) - Unexcused absence