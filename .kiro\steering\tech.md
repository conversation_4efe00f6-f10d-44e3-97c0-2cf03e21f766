# GuruFlow Technical Stack

## Core Technologies

- **Frontend Framework**: Next.js 15 with App Router
- **UI Framework**: React 18 with Server Components
- **Language**: TypeScript for type safety
- **Styling**: Tailwind CSS v4 + shadcn/ui components
- **Database**: Turso (libSQL) with Drizzle ORM
- **Authentication**: Custom session-based auth with HTTP-only cookies
- **File Storage**: S3-compatible (Cloudflare R2 or MinIO)
- **PDF Generation**: react-pdf
- **Deployment**: Vercel (frontend), Turso Cloud (database)

## Development Tools

- **Package Manager**: bun v1.0.23
- **Type Checking**: TypeScript v5.4.2
- **ORM**: Drizzle ORM with Drizzle Kit for migrations
- **Testing**: Jest, React Testing Library, Playwright
- **Code Quality**: ESLint, Prettier, TypeScript strict mode

## Architecture Patterns

- **App Router**: Use Next.js 15 App Router for file-based routing
- **Server Components**: Prefer React Server Components for data fetching
- **Server Actions**: Use Next.js Server Actions for form submissions and mutations
- **Route Groups**: Organize routes with (auth) and (dashboard) groups
- **Middleware**: Implement authentication and role-based access control

## Database Conventions

- **Schema**: Define all tables in `db/schema.ts` using Drizzle ORM
- **Migrations**: Use Drizzle Kit for database migrations
- **Naming**: Use snake_case for database columns, camelCase for TypeScript
- **Relationships**: Properly define foreign keys and relationships
- **Timestamps**: Include created_at and updated_at for audit trails

## Common Commands

### Development

```bash
# Start development server
bun run dev

# Run database migrations
bun run db:migrate

# Generate database migrations
bun run db:generate

# Push schema changes (development)
bun run db:push

# View database in Drizzle Studio
bun run db:studio
```

### Testing

```bash
# Run unit tests
bun run test

# Run tests in watch mode
bun run test:watch

# Run E2E tests
bun run test:e2e

# Run all tests with coverage
bun run test:coverage
```

### Build & Deploy

```bash
# Build for production
bun run build

# Start production server
bun run start

# Type checking
bun run type-check

# Lint code
bun run lint

# Format code
bun run format
```

## Environment Variables

- `DATABASE_URL`: Turso database connection string
- `DATABASE_AUTH_TOKEN`: Turso authentication token
- `NEXTAUTH_SECRET`: Session encryption secret
- `NEXTAUTH_URL`: Application base URL
- `STORAGE_ENDPOINT`: S3-compatible storage endpoint
- `STORAGE_ACCESS_KEY`: Storage access key
- `STORAGE_SECRET_KEY`: Storage secret key
