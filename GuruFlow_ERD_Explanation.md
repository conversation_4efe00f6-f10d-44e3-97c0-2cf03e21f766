# GuruFlow ERD Explanation

This document explains how the key entities in the GuruFlow platform are related, especially focusing on AI Questions, XP, and Badges.

---

## 🔗 Key Entity Relationships (Simplified Walkthrough)

### 🧑‍🏫 Teacher ↔ Subject ↔ Assignment
- A **Teacher** is linked to many **Subjects** they teach.
- Teachers can create **Assignments** under specific **Subjects**.
- Each Assignment can have multiple **Submissions** (one per Student).

---

### 👨‍🎓 Student ↔ Classroom ↔ Subject
- A **Student** belongs to a **Classroom**.
- Each **Classroom** has multiple **Subjects**, taught by Teachers.
- This structure drives assignment visibility, grading, and attendance.

---

### 📥 Assignment ↔ Submission ↔ Feedback
- A **Student** submits work via a **Submission**.
- That Submission links to:
  - The original **Assignment**
  - The **Student**
  - It also contains a grade and teacher **feedback**.

---

### 📅 Attendance Records
- Teachers take daily **Attendance**, linking:
  - `Student` + `Classroom` + `Date`
  - Status options: H (Hadir), I (<PERSON>zin), S (Sakit), A (Alpa)
- Records can be exported and analyzed.

---

### 🧠 AI Question Bank
- A **Question** is always linked to a **Subject** and optionally flagged as `ai_generated = true`.
- Created by Teachers, these can be reviewed, edited, and published for future use in assessments.

---

### 🎮 Gamification: XP and Badges
- **XPTransaction** tracks each XP event:
  - Earned by a **Student**, includes amount, reason, and timestamp.
- **Badge** defines reward criteria (e.g., “Submitted 10 assignments”).
- **StudentBadge** tracks which badges each student has earned and when.

---

### 🧾 Gradebook / Rapor
- Links a **Student** with a **Subject**
- Stores formative and summative scores
- Includes predikat values (A/B/C/D) based on KKM
