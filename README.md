# GuruFlow MVP

Platform administrasi sekolah digital modern untuk Indonesia yang membantu guru dan siswa mengelola kelas, a<PERSON><PERSON><PERSON>, tugas, dan penilaian dengan mudah.

## 🚀 Fitur Utama

- **Sistem Absensi Digital**: Menggunakan kode absensi Indonesia (H/I/S/A)
- **<PERSON><PERSON><PERSON><PERSON>**: <PERSON><PERSON><PERSON>, mata pela<PERSON>, dan siswa
- **Tugas & Penilaian**: <PERSON><PERSON><PERSON> tugas, terima pengumpulan, dan berikan nilai
- **Gamifikasi**: Sistem XP dan badge untuk motivasi siswa
- **Laporan & Export**: Export laporan PDF dan CSV
- **Mobile Responsive**: Akses dari perangkat apapun
- **Keamanan Data**: Enkripsi dan backup otomatis

## 🛠️ Tech Stack

- **Frontend**: Next.js 15 + React 18 + TypeScript
- **Styling**: Tailwind CSS v4 + shadcn/ui
- **Database**: <PERSON><PERSON><PERSON> (libSQL) + Drizzle ORM
- **Authentication**: Custom session-based auth
- **File Storage**: S3-compatible (Cloudflare R2)
- **PDF Generation**: react-pdf
- **Deployment**: Vercel + Turso Cloud

## 📋 Prerequisites

- Node.js 18+ atau Bun 1.0+
- Database Turso (atau SQLite untuk development)
- S3-compatible storage (opsional)

## 🚀 Quick Start

### 1. Clone Repository

```bash
git clone https://github.com/your-org/guruflow-mvp.git
cd guruflow-mvp
```

### 2. Install Dependencies

```bash
# Menggunakan bun (recommended)
bun install

# Atau menggunakan npm
npm install
```

### 3. Setup Environment

```bash
cp .env.example .env.local
```

Edit `.env.local` dengan konfigurasi Anda:

```env
DATABASE_URL="libsql://your-database.turso.io"
DATABASE_AUTH_TOKEN="your-auth-token"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

### 4. Setup Database

```bash
# Generate dan apply migrations
bun run db:generate
bun run db:migrate

# Atau push schema langsung (development)
bun run db:push

# Seed data awal
bun run db:seed
```

### 5. Run Development Server

```bash
bun run dev
```

Buka [http://localhost:3000](http://localhost:3000) di browser Anda.

## 📚 Available Scripts

### Development

```bash
bun run dev          # Start development server
bun run build        # Build for production
bun run start        # Start production server
bun run lint         # Run ESLint
bun run type-check   # Run TypeScript check
bun run format       # Format code with Prettier
```

### Database

```bash
bun run db:generate  # Generate migrations
bun run db:migrate   # Apply migrations
bun run db:push      # Push schema (dev only)
bun run db:studio    # Open Drizzle Studio
bun run db:seed      # Seed initial data
```

### Testing

```bash
bun run test         # Run unit tests
bun run test:watch   # Run tests in watch mode
bun run test:e2e     # Run E2E tests
bun run test:coverage # Run tests with coverage
```

## 🏗️ Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes
│   ├── (dashboard)/       # Protected dashboard routes
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Landing page
├── components/            # Reusable components
│   ├── ui/               # shadcn/ui components
│   ├── forms/            # Form components
│   └── dashboard/        # Dashboard components
├── lib/                  # Utilities and configurations
│   ├── auth.ts           # Authentication utilities
│   ├── db.ts             # Database connection
│   ├── utils.ts          # General utilities
│   ├── validations.ts    # Form validation schemas
│   └── constants.ts      # Application constants
└── db/                   # Database schema and migrations
    ├── schema.ts         # Drizzle schema
    └── migrations/       # Migration files
```

## 🔐 Authentication

GuruFlow menggunakan sistem autentikasi berbasis session dengan HTTP-only cookies untuk keamanan maksimal.

### User Roles

- **Teacher (Guru)**: Dapat mengelola kelas, siswa, absensi, dan tugas
- **Student (Siswa)**: Dapat melihat tugas, nilai, dan absensi mereka
- **Super Admin**: Guru pertama yang mendaftar dengan akses penuh

## 📊 Database Schema

### Core Tables

- `schools`: Konfigurasi sekolah
- `users`: Semua pengguna sistem
- `sessions`: Manajemen session autentikasi

### Academic Structure

- `classrooms`: Definisi kelas
- `subjects`: Mata pelajaran
- `classroom_subjects`: Relasi kelas-mata pelajaran dengan guru
- `students`: Data siswa dan enrollment

### Operational Data

- `attendance_records`: Catatan absensi harian
- `assignments`: Tugas dari guru
- `submissions`: Pengumpulan tugas siswa
- `grades`: Catatan nilai akademik

### Gamification

- `student_xp`: Tracking poin XP siswa
- `badges`: Definisi badge/penghargaan
- `student_badges`: Badge yang diraih siswa

## 🎯 Indonesian Education Standards

GuruFlow dirancang khusus untuk sistem pendidikan Indonesia:

### Kode Absensi
- **H** (Hadir): Siswa hadir
- **I** (Izin): Tidak hadir dengan izin
- **S** (Sakit): Tidak hadir karena sakit
- **A** (Alpa): Tidak hadir tanpa keterangan

### Jenjang Sekolah
- SD (Sekolah Dasar)
- SMP (Sekolah Menengah Pertama)
- SMA (Sekolah Menengah Atas)
- SMK (Sekolah Menengah Kejuruan)

### Tahun Ajaran
Format: 2024/2025 (Juli - Juni)

## 🚀 Deployment

### Vercel (Recommended)

1. Push code ke GitHub
2. Connect repository di Vercel
3. Set environment variables
4. Deploy

### Manual Deployment

```bash
# Build aplikasi
bun run build

# Start production server
bun run start
```

## 🧪 Testing

GuruFlow menggunakan Jest untuk unit testing dan Playwright untuk E2E testing.

```bash
# Run all tests
bun run test

# Run specific test file
bun run test src/lib/utils.test.ts

# Run E2E tests
bun run test:e2e
```

## 📝 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Support

Jika Anda membutuhkan bantuan:

- 📧 Email: <EMAIL>
- 📖 Documentation: [docs.guruflow.id](https://docs.guruflow.id)
- 🐛 Issues: [GitHub Issues](https://github.com/your-org/guruflow-mvp/issues)

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - React framework
- [Tailwind CSS](https://tailwindcss.com/) - CSS framework
- [shadcn/ui](https://ui.shadcn.com/) - UI components
- [Drizzle ORM](https://orm.drizzle.team/) - TypeScript ORM
- [Turso](https://turso.tech/) - Edge database

---

Made with ❤️ for Indonesian education