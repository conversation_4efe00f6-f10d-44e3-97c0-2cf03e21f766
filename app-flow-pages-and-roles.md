# GuruFlow - App Flow, Pages, and Roles

## 👥 User Roles
- **Teacher** (superadmin + regular): Can access all modules, input/edit data, generate reports
- **Student:** View-only access to dashboard, tasks, grades, XP, and submissions

## 🗺️ App Flow
1. **Landing Page** → Login → Redirect based on role (Teacher → Dashboard Guru, Student → Dashboard Siswa)
2. **Onboarding Wizard (First Use Only):** Set school info → Create first Teacher account
3. **Dashboard (Role-Based):** Shows personalized content
4. **Module Access via Nav:** Sidebar or bottom nav depending on device

## 📄 Core Pages and Functions

### General
- **Landing Page**: Branded, CTA login, responsive
- **Login Page**: Custom auth with email/password, optional magic link
- **404/Error Pages**: Friendly messages, redirect option

### Onboarding
- **School Setup Wizard**: Name, logo, signature, level
- **Create Teacher Superadmin**

### Teacher Pages
- **Dashboard Guru**: Schedule, shortcuts (absensi, tugas), XP leaderboard
- **<PERSON><PERSON> & Siswa Management**: CRUD kelas, mapel, siswa, enroll via CSV
- **Absensi**: Daily input (H/I/S/A), per kelas view, export PDF/CSV
- **Tugas & Penilaian**: Create assignments, review submissions, rubric scoring, feedback
- **Nilai & Rapor**: Track nilai formatif/sumatif, global KKM, generate report cards
- **Jurnal Guru**: Notes/log per pertemuan
- **Bank Soal AI**: Generate/review/publish soal
- **Laporan**: Rekap nilai, XP, absensi

### Student Pages
- **Dashboard Siswa**: Active tugas, nilai progres, XP/badge
- **Tugas View**: Submit tugas, see feedback
- **Rapor View**: See grades & predikat
- **Leaderboard**: View rankings

## 🔄 Navigation Flow Summary
- After login, users land on their role-specific dashboard
- Navigation allows access to all modules relevant to their role
- Students see simplified views and are limited to their own data
- Teachers can manage all data and access full analytics
