# Task 1 - Project Setup and Core Infrastructure

## Overview

Task ini mencakup setup awal proyek GuruFlow MVP dengan teknologi stack modern dan konfigurasi development environment yang optimal.

## Teknologi Stack

### Frontend Framework
- **Next.js 15** dengan App Router
- **React 18** dengan Server Components
- **TypeScript** untuk type safety

### Styling & UI
- **Tailwind CSS v4** untuk styling
- **shadcn/ui** untuk komponen UI
- **Lucide React** untuk icons
- **Radix UI** untuk primitives

### Database & ORM
- **Turso (libSQL)** sebagai database
- **Drizzle ORM** untuk database operations
- **Drizzle Kit** untuk migrations

### Development Tools
- **Bun** sebagai package manager dan runtime
- **ESLint** untuk code linting
- **TypeScript** strict mode

## File Struktur

```
├── src/
│   ├── app/                    # Next.js App Router
│   ├── components/             # React components
│   │   └── ui/                # shadcn/ui components
│   ├── lib/                   # Utility functions
│   ├── db/                    # Database schema
│   └── hooks/                 # Custom React hooks
├── docs/                      # Dokumentasi
├── scripts/                   # Utility scripts
├── public/                    # Static assets
└── config files               # Package.json, tsconfig, etc.
```

## Konfigurasi Utama

### 1. Next.js Configuration (`next.config.js`)
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    typedRoutes: true,
  },
};

module.exports = nextConfig;
```

### 2. TypeScript Configuration (`tsconfig.json`)
```json
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

### 3. Tailwind Configuration (`tailwind.config.ts`)
```typescript
import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        // ... more colors
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};

export default config;
```

### 4. Drizzle Configuration (`drizzle.config.ts`)
```typescript
import type { Config } from "drizzle-kit";

export default {
  schema: "./src/db/schema.ts",
  out: "./src/db/migrations",
  driver: "turso",
  dbCredentials: {
    url: process.env.DATABASE_URL!,
    authToken: process.env.DATABASE_AUTH_TOKEN!,
  },
} satisfies Config;
```

## Environment Variables

### Required Variables (`.env.local`)
```bash
# Database Configuration
DATABASE_URL="libsql://your-database-url"
DATABASE_AUTH_TOKEN="your-auth-token"

# Authentication
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# File Storage (Optional)
STORAGE_ENDPOINT="your-storage-endpoint"
STORAGE_ACCESS_KEY="your-access-key"
STORAGE_SECRET_KEY="your-secret-key"
STORAGE_BUCKET="your-bucket-name"

# Application
NODE_ENV="development"
```

## Package Dependencies

### Core Dependencies
```json
{
  "dependencies": {
    "next": "15.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "typescript": "^5.4.2",
    "@libsql/client": "^0.5.2",
    "drizzle-orm": "^0.29.0",
    "drizzle-kit": "^0.20.0",
    "tailwindcss": "^3.4.0",
    "@radix-ui/react-slot": "^1.0.2",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.1.0",
    "tailwind-merge": "^2.2.0",
    "lucide-react": "^0.344.0"
  }
}
```

## Setup Instructions

### 1. Initial Setup
```bash
# Clone atau create project
npx create-next-app@latest guruflow --typescript --tailwind --eslint --app

# Install dependencies
cd guruflow
bun install

# Install additional packages
bun add @libsql/client drizzle-orm drizzle-kit
bun add @radix-ui/react-slot class-variance-authority clsx tailwind-merge
bun add lucide-react @radix-ui/react-label @radix-ui/react-select
bun add -d @types/node
```

### 2. Configure Environment
```bash
# Copy environment template
cp .env.example .env.local

# Edit .env.local dengan konfigurasi yang sesuai
```

### 3. Setup Database
```bash
# Generate database schema
bun run db:generate

# Push schema to database
bun run db:push

# (Optional) Open Drizzle Studio
bun run db:studio
```

### 4. Development
```bash
# Start development server
bun run dev

# Open browser
open http://localhost:3000
```

## Scripts yang Tersedia

### Package.json Scripts
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "db:generate": "drizzle-kit generate:sqlite",
    "db:push": "drizzle-kit push:sqlite",
    "db:studio": "drizzle-kit studio",
    "db:migrate": "bun run tsx src/lib/migrate.ts",
    "type-check": "tsc --noEmit"
  }
}
```

### Custom Scripts
- `scripts/create-test-user.ts` - Membuat user test untuk development
- `scripts/generate-migration.ts` - Generate migration files
- `src/lib/migrate.ts` - Run migrations programmatically

## Folder Structure Detail

### `/src/app` - Next.js App Router
```
app/
├── (auth)/                    # Auth route group
│   ├── login/
│   └── onboarding/
├── (dashboard)/               # Dashboard route group
│   ├── guru/                  # Teacher routes
│   └── siswa/                 # Student routes
├── api/                       # API routes
├── globals.css                # Global styles
├── layout.tsx                 # Root layout
└── page.tsx                   # Landing page
```

### `/src/components` - React Components
```
components/
├── ui/                        # shadcn/ui components
│   ├── button.tsx
│   ├── card.tsx
│   ├── input.tsx
│   └── ...
├── auth/                      # Authentication components
├── dashboard/                 # Dashboard components
├── forms/                     # Form components
└── layout/                    # Layout components
```

### `/src/lib` - Utilities
```
lib/
├── auth.ts                    # Authentication utilities
├── db.ts                      # Database connection
├── utils.ts                   # General utilities
├── validations.ts             # Form validation schemas
└── constants.ts               # Application constants
```

## Best Practices

### 1. File Naming
- **Components**: PascalCase (`LoginForm.tsx`)
- **Pages**: lowercase (`page.tsx`, `layout.tsx`)
- **Utilities**: camelCase (`authUtils.ts`)
- **API Routes**: lowercase (`route.ts`)

### 2. Import Organization
```typescript
// 1. React and Next.js imports
import { useState } from 'react';
import { useRouter } from 'next/navigation';

// 2. Third-party library imports
import { z } from 'zod';

// 3. Internal component imports
import { Button } from '@/components/ui/button';

// 4. Utility and configuration imports
import { cn } from '@/lib/utils';

// 5. Type imports (with type keyword)
import type { User } from '@/types';
```

### 3. Component Structure
```typescript
'use client'; // Only for client components

import { useState } from 'react';
import { Button } from '@/components/ui/button';

interface ComponentProps {
  // Props definition
}

export function ComponentName({ prop }: ComponentProps) {
  // State and hooks
  const [state, setState] = useState();
  
  // Event handlers
  const handleClick = () => {
    // Handler logic
  };
  
  // Render
  return (
    <div>
      {/* Component JSX */}
    </div>
  );
}
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   ```bash
   # Check environment variables
   echo $DATABASE_URL
   
   # Test connection
   bun run db:studio
   ```

2. **TypeScript Errors**
   ```bash
   # Check TypeScript configuration
   bun run type-check
   
   # Restart TypeScript server in IDE
   ```

3. **Tailwind Styles Not Working**
   ```bash
   # Check Tailwind configuration
   # Ensure content paths are correct in tailwind.config.ts
   ```

4. **Import Path Errors**
   ```bash
   # Check tsconfig.json baseUrl and paths
   # Ensure @ alias is configured correctly
   ```

### Development Tips

1. **Hot Reload Issues**
   - Restart development server
   - Clear `.next` cache: `rm -rf .next`

2. **Database Schema Changes**
   ```bash
   # Generate new migration
   bun run db:generate
   
   # Apply changes
   bun run db:push
   ```

3. **Performance Optimization**
   - Use Server Components when possible
   - Implement proper loading states
   - Optimize images with next/image

## Next Steps

Setelah setup selesai, lanjutkan ke:
1. [Task 2 - Database Schema](./task-2-database-schema.md)
2. [Task 3 - Authentication System](./task-3-authentication-system.md)

## Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Drizzle ORM Documentation](https://orm.drizzle.team/)
- [shadcn/ui Documentation](https://ui.shadcn.com/)
- [Turso Documentation](https://docs.turso.tech/)