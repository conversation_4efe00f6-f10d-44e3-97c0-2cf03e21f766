# Task 2 - Database Schema and Migrations

## Overview

Task ini mencakup desain dan implementasi database schema untuk GuruFlow MVP menggunakan Drizzle ORM dengan Turso (libSQL) sebagai database.

## Database Schema

### Entity Relationship Diagram

```mermaid
erDiagram
    SCHOOLS ||--o{ USERS : "belongs_to"
    USERS ||--o{ SESSIONS : "has_many"
    USERS ||--o{ STUDENTS : "has_one"
    USERS ||--o{ CLASSROOM_SUBJECTS : "teaches"
    CLASSROOMS ||--o{ STUDENTS : "has_many"
    CLASSROOMS ||--o{ CLASSROOM_SUBJECTS : "has_many"
    CLASSROOMS ||--o{ ATTENDANCE_RECORDS : "has_many"
    SUBJECTS ||--o{ CLASSROOM_SUBJECTS : "has_many"
    STUDENTS ||--o{ ATTENDANCE_RECORDS : "has_many"
    STUDENTS ||--|| STUDENT_XP : "has_one"
    STUDENTS ||--o{ STUDENT_BADGES : "has_many"
    BADGES ||--o{ STUDENT_BADGES : "has_many"
```

## Table Definitions

### 1. Schools Table
```typescript
export const schools = sqliteTable('schools', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  name: text('name').notNull(),
  logo: text('logo'),                    // URL to logo file
  signature: text('signature'),          // URL to signature file
  level: text('level').notNull(),        // 'SD', 'SMP', 'SMA', 'SMK'
  address: text('address'),
  phone: text('phone'),
  email: text('email'),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`)
});
```

**Purpose**: Menyimpan informasi sekolah (singleton table)
**Key Fields**: 
- `name`: Nama sekolah
- `level`: Jenjang pendidikan (SD/SMP/SMA/SMK)
- `logo`, `signature`: File uploads untuk branding

### 2. Users Table
```typescript
export const users = sqliteTable('users', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  email: text('email').notNull().unique(),
  passwordHash: text('password_hash').notNull(),
  role: text('role').notNull(),          // 'teacher' | 'student'
  name: text('name').notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  isSuperAdmin: integer('is_super_admin', { mode: 'boolean' }).default(false),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`)
});
```

**Purpose**: Menyimpan semua user (guru dan siswa)
**Key Fields**:
- `role`: Membedakan teacher dan student
- `isSuperAdmin`: Hak akses admin untuk teacher
- `isActive`: Status aktif/nonaktif user

### 3. Sessions Table
```typescript
export const sessions = sqliteTable('sessions', {
  id: text('id').primaryKey(),           // UUID session ID
  userId: integer('user_id').notNull().references(() => users.id),
  expiresAt: text('expires_at').notNull(),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`)
});
```

**Purpose**: Manajemen session untuk authentication
**Key Fields**:
- `id`: Session token (UUID)
- `expiresAt`: Waktu kadaluarsa session

### 4. Classrooms Table
```typescript
export const classrooms = sqliteTable('classrooms', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  name: text('name').notNull(),          // e.g., "Kelas 5A"
  grade: text('grade').notNull(),        // e.g., "5"
  academicYear: text('academic_year').notNull(), // e.g., "2024/2025"
  homeroomTeacherId: integer('homeroom_teacher_id').references(() => users.id),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`)
});
```

**Purpose**: Definisi kelas dalam sekolah
**Key Fields**:
- `name`: Nama kelas (Kelas 5A, 6B, dll)
- `grade`: Tingkat kelas
- `academicYear`: Tahun ajaran
- `homeroomTeacherId`: Wali kelas

### 5. Subjects Table
```typescript
export const subjects = sqliteTable('subjects', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  name: text('name').notNull(),          // e.g., "Matematika"
  code: text('code').notNull().unique(), // e.g., "MTK"
  description: text('description'),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`)
});
```

**Purpose**: Master data mata pelajaran
**Key Fields**:
- `name`: Nama mata pelajaran
- `code`: Kode mata pelajaran (unik)

### 6. Classroom Subjects Table
```typescript
export const classroomSubjects = sqliteTable('classroom_subjects', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  classroomId: integer('classroom_id').notNull().references(() => classrooms.id),
  subjectId: integer('subject_id').notNull().references(() => subjects.id),
  teacherId: integer('teacher_id').notNull().references(() => users.id),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`)
});
```

**Purpose**: Relasi many-to-many antara kelas dan mata pelajaran
**Key Fields**:
- `teacherId`: Guru pengampu mata pelajaran di kelas tertentu

### 7. Students Table
```typescript
export const students = sqliteTable('students', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  userId: integer('user_id').notNull().references(() => users.id),
  studentId: text('student_id').notNull().unique(), // NIS
  classroomId: integer('classroom_id').notNull().references(() => classrooms.id),
  enrollmentDate: text('enrollment_date').notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`)
});
```

**Purpose**: Data siswa dan enrollment ke kelas
**Key Fields**:
- `studentId`: NIS (Nomor Induk Siswa)
- `classroomId`: Kelas tempat siswa terdaftar
- `enrollmentDate`: Tanggal masuk

### 8. Attendance Records Table
```typescript
export const attendanceRecords = sqliteTable('attendance_records', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  studentId: integer('student_id').notNull().references(() => students.id),
  classroomId: integer('classroom_id').notNull().references(() => classrooms.id),
  date: text('date').notNull(),          // YYYY-MM-DD format
  status: text('status').notNull(),      // 'H' | 'I' | 'S' | 'A'
  notes: text('notes'),
  markedBy: integer('marked_by').notNull().references(() => users.id),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`)
});
```

**Purpose**: Rekam absensi harian siswa
**Key Fields**:
- `status`: Kode absensi Indonesia (H/I/S/A)
- `date`: Tanggal absensi
- `markedBy`: Guru yang mencatat absensi

### 9. Student XP Table (Gamification)
```typescript
export const studentXP = sqliteTable('student_xp', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  studentId: integer('student_id').notNull().references(() => students.id),
  totalXP: integer('total_xp').default(0),
  level: integer('level').default(1),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`)
});
```

**Purpose**: Tracking XP dan level siswa untuk gamifikasi

### 10. Badges Table (Gamification)
```typescript
export const badges = sqliteTable('badges', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  name: text('name').notNull(),
  description: text('description').notNull(),
  icon: text('icon').notNull(),
  criteria: text('criteria').notNull(),  // JSON string
  xpReward: integer('xp_reward').default(0),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`)
});
```

**Purpose**: Definisi badge yang bisa diraih siswa

### 11. Student Badges Table
```typescript
export const studentBadges = sqliteTable('student_badges', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  studentId: integer('student_id').notNull().references(() => students.id),
  badgeId: integer('badge_id').notNull().references(() => badges.id),
  earnedAt: text('earned_at').default(sql`CURRENT_TIMESTAMP`)
});
```

**Purpose**: Relasi badge yang sudah diraih siswa

## Database Configuration

### Connection Setup (`src/lib/db.ts`)
```typescript
import { drizzle } from 'drizzle-orm/libsql';
import { createClient } from '@libsql/client';
import * as schema from '../db/schema';

// Create the database client
const client = createClient({
  url: process.env.DATABASE_URL!,
  authToken: process.env.DATABASE_AUTH_TOKEN!,
});

// Create the Drizzle database instance
export const db = drizzle(client, { schema });

// Database connection utility with error handling
export async function connectToDatabase() {
  try {
    await client.execute('SELECT 1');
    console.log('✅ Database connected successfully');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw new Error('Failed to connect to database');
  }
}
```

### Migration Configuration (`drizzle.config.ts`)
```typescript
import type { Config } from "drizzle-kit";

export default {
  schema: "./src/db/schema.ts",
  out: "./src/db/migrations",
  driver: "turso",
  dbCredentials: {
    url: process.env.DATABASE_URL!,
    authToken: process.env.DATABASE_AUTH_TOKEN!,
  },
} satisfies Config;
```

## Migration Management

### Generate Migrations
```bash
# Generate migration from schema changes
bun run db:generate

# This creates files in src/db/migrations/
```

### Apply Migrations
```bash
# Push schema directly to database (development)
bun run db:push

# Run migrations programmatically
bun run db:migrate
```

### Migration Script (`src/lib/migrate.ts`)
```typescript
import { migrate } from 'drizzle-orm/libsql/migrator';
import { db } from './db';

async function runMigrations() {
  try {
    console.log('🔄 Running migrations...');
    await migrate(db, { migrationsFolder: './src/db/migrations' });
    console.log('✅ Migrations completed successfully');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

runMigrations();
```

## Seed Data

### Default Badges (`src/lib/seed.ts`)
```typescript
export async function seedDatabase() {
  console.log('🌱 Seeding database with initial data...');

  // Seed default badges for gamification
  const defaultBadges = [
    {
      name: 'Rajin Hadir',
      description: 'Hadir 30 hari berturut-turut',
      icon: 'attendance-streak',
      criteria: JSON.stringify({
        type: 'attendance_streak',
        days: 30,
        status: 'H'
      }),
      xpReward: 100
    },
    // ... more badges
  ];

  for (const badge of defaultBadges) {
    await db.insert(badges).values(badge).onConflictDoNothing();
  }

  console.log('✅ Database seeding completed successfully');
}
```

## Database Utilities

### Query Helpers (`src/lib/database-utils.ts`)
```typescript
import { db } from './db';
import { users, students, classrooms } from '@/db/schema';
import { eq, and, desc } from 'drizzle-orm';

// Get user with student data
export async function getUserWithStudent(userId: number) {
  return await db
    .select({
      user: users,
      student: students,
      classroom: classrooms
    })
    .from(users)
    .leftJoin(students, eq(users.id, students.userId))
    .leftJoin(classrooms, eq(students.classroomId, classrooms.id))
    .where(eq(users.id, userId))
    .limit(1);
}

// Get classroom with students
export async function getClassroomWithStudents(classroomId: number) {
  return await db
    .select({
      classroom: classrooms,
      student: students,
      user: users
    })
    .from(classrooms)
    .leftJoin(students, eq(classrooms.id, students.classroomId))
    .leftJoin(users, eq(students.userId, users.id))
    .where(eq(classrooms.id, classroomId));
}
```

## Best Practices

### 1. Schema Design
- **Normalization**: Proper table relationships
- **Constraints**: Foreign keys dan unique constraints
- **Indexing**: Index pada kolom yang sering di-query
- **Timestamps**: Audit trail dengan created_at/updated_at

### 2. Data Types
- **Text vs Integer**: Gunakan integer untuk ID, text untuk string
- **Boolean**: Gunakan integer dengan mode boolean
- **Dates**: Simpan sebagai ISO string untuk konsistensi
- **JSON**: Gunakan text dengan JSON.stringify/parse

### 3. Naming Conventions
- **Tables**: snake_case (users, classroom_subjects)
- **Columns**: snake_case (created_at, user_id)
- **Foreign Keys**: table_id format (user_id, classroom_id)
- **Indexes**: idx_table_column format

### 4. Query Optimization
```typescript
// Good: Use select with specific fields
const users = await db
  .select({
    id: users.id,
    name: users.name,
    email: users.email
  })
  .from(users)
  .where(eq(users.isActive, true));

// Good: Use joins instead of multiple queries
const studentsWithClassrooms = await db
  .select()
  .from(students)
  .innerJoin(classrooms, eq(students.classroomId, classrooms.id))
  .innerJoin(users, eq(students.userId, users.id));
```

## Common Operations

### 1. User Management
```typescript
// Create new user
const newUser = await db.insert(users).values({
  email: '<EMAIL>',
  passwordHash: hashedPassword,
  role: 'student',
  name: 'John Doe'
}).returning();

// Get user by email
const user = await db
  .select()
  .from(users)
  .where(eq(users.email, email))
  .limit(1);
```

### 2. Student Enrollment
```typescript
// Enroll student to classroom
const enrollment = await db.insert(students).values({
  userId: userId,
  studentId: 'NIS123',
  classroomId: classroomId,
  enrollmentDate: new Date().toISOString().split('T')[0]
});
```

### 3. Attendance Recording
```typescript
// Record attendance
const attendance = await db.insert(attendanceRecords).values({
  studentId: studentId,
  classroomId: classroomId,
  date: '2024-12-16',
  status: 'H',
  markedBy: teacherId
});
```

## Troubleshooting

### Common Issues

1. **Connection Errors**
   ```bash
   # Check environment variables
   echo $DATABASE_URL
   echo $DATABASE_AUTH_TOKEN
   
   # Test connection
   bun run db:studio
   ```

2. **Migration Errors**
   ```bash
   # Reset migrations (development only)
   rm -rf src/db/migrations
   bun run db:generate
   bun run db:push
   ```

3. **Schema Conflicts**
   ```bash
   # Check current schema
   bun run db:studio
   
   # Generate new migration
   bun run db:generate
   ```

### Performance Tips

1. **Use Indexes**
   ```typescript
   // Add indexes for frequently queried columns
   export const userEmailIndex = index('idx_users_email').on(users.email);
   export const attendanceDateIndex = index('idx_attendance_date').on(attendanceRecords.date);
   ```

2. **Optimize Queries**
   ```typescript
   // Use limit for large datasets
   const recentAttendance = await db
     .select()
     .from(attendanceRecords)
     .orderBy(desc(attendanceRecords.date))
     .limit(100);
   ```

3. **Batch Operations**
   ```typescript
   // Insert multiple records at once
   await db.insert(attendanceRecords).values([
     { studentId: 1, date: '2024-12-16', status: 'H' },
     { studentId: 2, date: '2024-12-16', status: 'H' },
     // ... more records
   ]);
   ```

## Next Steps

Setelah database schema selesai, lanjutkan ke:
1. [Task 3 - Authentication System](./task-3-authentication-system.md)
2. [Task 4 - School Onboarding](./task-4-onboarding-system.md)

## Resources

- [Drizzle ORM Documentation](https://orm.drizzle.team/)
- [Turso Documentation](https://docs.turso.tech/)
- [SQLite Documentation](https://www.sqlite.org/docs.html)
- [Database Design Best Practices](https://www.sqlstyle.guide/)