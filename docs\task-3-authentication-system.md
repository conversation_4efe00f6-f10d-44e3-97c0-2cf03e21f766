# Task 3 - Authentication System Implementation

## Overview

Task ini mengimplementasikan sistem authentication lengkap dengan session-based auth, role-based access control, dan route protection untuk GuruFlow MVP.

## Arsitektur Authentication

### Flow Diagram
```mermaid
sequenceDiagram
    participant User
    participant Client
    participant Middleware
    participant API
    participant Database
    
    User->>Client: Login (email, password)
    Client->>API: POST /api/auth/login
    API->>Database: Verify credentials
    Database-->>API: User data
    API->>Database: Create session
    API-->>Client: Set HTTP-only cookie
    Client-->>User: Redirect to dashboard
    
    User->>Client: Access protected route
    Client->>Middleware: Request with cookie
    Middleware->>Database: Validate session
    Database-->>Middleware: Session valid
    Middleware-->>Client: Allow access
```

## Komponen Utama

### 1. Authentication Utilities (`src/lib/auth.ts`)

#### Password Management
```typescript
import bcrypt from 'bcryptjs';

export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}
```

#### Session Management
```typescript
export async function createSession(userId: number): Promise<string> {
  const sessionId = generateId();
  const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
  
  await db.insert(sessions).values({
    id: sessionId,
    userId,
    expiresAt: expiresAt.toISOString(),
  });
  
  return sessionId;
}

export async function validateSession(sessionId: string): Promise<SessionData | null> {
  const session = await db
    .select()
    .from(sessions)
    .where(eq(sessions.id, sessionId))
    .limit(1);
    
  if (session.length === 0) return null;
  
  const sessionData = session[0];
  const expiresAt = new Date(sessionData.expiresAt);
  
  // Check if session is expired
  if (expiresAt < new Date()) {
    await deleteSession(sessionId);
    return null;
  }
  
  return {
    id: sessionData.id,
    userId: sessionData.userId,
    expiresAt,
  };
}
```

#### Cookie Management
```typescript
const SESSION_COOKIE_NAME = 'guruflow-session';

export async function setSessionCookie(sessionId: string): Promise<void> {
  const cookieStore = await cookies();
  const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
  
  cookieStore.set(SESSION_COOKIE_NAME, sessionId, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    expires: expiresAt,
    path: '/',
  });
}
```

### 2. Authentication Guards (`src/lib/auth-guards.ts`)

#### Server-Side Guards
```typescript
// Basic authentication guard
export async function requireAuth(): Promise<AuthenticatedUser> {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect('/login');
  }
  
  return user;
}

// Role-based authentication guard
export async function requireRole(allowedRoles: Array<'teacher' | 'student'>): Promise<AuthenticatedUser> {
  const user = await requireAuth();
  
  if (!allowedRoles.includes(user.role)) {
    if (user.role === 'teacher') {
      redirect('/guru');
    } else if (user.role === 'student') {
      redirect('/siswa');
    } else {
      redirect('/login');
    }
  }
  
  return user;
}

// Specific role guards
export async function requireTeacher(): Promise<AuthenticatedUser> {
  return requireRole(['teacher']);
}

export async function requireStudent(): Promise<AuthenticatedUser> {
  return requireRole(['student']);
}

// Superadmin guard
export async function requireSuperAdmin(): Promise<AuthenticatedUser> {
  const user = await requireTeacher();
  
  if (!user.isSuperAdmin) {
    redirect('/guru');
  }
  
  return user;
}
```

#### API Route Guards
```typescript
// API authentication wrapper
export function withAuth<T extends any[]>(
  handler: (request: NextRequest, user: AuthenticatedUser, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const user = await requireAuthAPI();
      return await handler(request, user, ...args);
    } catch (error) {
      if (error instanceof Error && error.message === 'Authentication required') {
        return NextResponse.json(API_ERRORS.UNAUTHORIZED, { status: 401 });
      }
      return NextResponse.json(API_ERRORS.INTERNAL_ERROR, { status: 500 });
    }
  };
}

// Role-based API wrapper
export function withTeacher<T extends any[]>(
  handler: (request: NextRequest, user: AuthenticatedUser, ...args: T) => Promise<NextResponse>
) {
  return withRole(['teacher'], handler);
}
```

### 3. Client-Side Authentication (`src/hooks/useAuth.ts`)

#### Authentication Hook
```typescript
export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null,
  });
  const router = useRouter();

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/me', {
        method: 'GET',
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setAuthState({
            user: data.data.user,
            loading: false,
            error: null,
          });
        }
      } else {
        setAuthState({
          user: null,
          loading: false,
          error: 'Not authenticated',
        });
      }
    } catch (error) {
      setAuthState({
        user: null,
        loading: false,
        error: 'Authentication error',
      });
    }
  };

  const logout = async () => {
    await fetch('/api/auth/logout', {
      method: 'POST',
      credentials: 'include',
    });
    
    setAuthState({ user: null, loading: false, error: null });
    router.push('/login');
  };

  useEffect(() => {
    checkAuth();
  }, []);

  return {
    user: authState.user,
    loading: authState.loading,
    error: authState.error,
    logout,
    refetch: checkAuth,
  };
}
```

### 4. Route Protection Middleware (`middleware.ts`)

```typescript
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Skip middleware for static files and API routes
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/favicon.ico') ||
    (pathname.startsWith('/api') && pathname !== '/api/auth/redirect')
  ) {
    return NextResponse.next();
  }
  
  // Get session cookie
  const sessionCookie = request.cookies.get('guruflow-session');
  const isAuthenticated = !!sessionCookie?.value;
  
  // Define route types
  const protectedRoutes = ['/guru', '/siswa'];
  const authRoutes = ['/login'];
  
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  );
  const isAuthRoute = authRoutes.some(route => 
    pathname.startsWith(route)
  );
  
  // Redirect logic
  if (isProtectedRoute && !isAuthenticated) {
    return NextResponse.redirect(new URL('/login', request.url));
  }
  
  if (isAuthRoute && isAuthenticated) {
    return NextResponse.redirect(new URL('/api/auth/redirect', request.url));
  }
  
  return NextResponse.next();
}
```

### 5. React Auth Guards (`src/components/auth/AuthGuard.tsx`)

```typescript
export function AuthGuard({ 
  children, 
  allowedRoles, 
  requireSuperAdmin = false 
}: AuthGuardProps) {
  const { user, loading, requireAuth, requireRole } = useAuth();

  useEffect(() => {
    if (!loading) {
      if (allowedRoles) {
        requireRole(allowedRoles);
      } else {
        requireAuth();
      }
    }
  }, [user, loading, allowedRoles]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!user || (allowedRoles && !allowedRoles.includes(user.role))) {
    return null; // Redirect handled by useAuth
  }

  if (requireSuperAdmin && !user.isSuperAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p className="text-red-600">Access denied. Superadmin privileges required.</p>
      </div>
    );
  }

  return <>{children}</>;
}
```

## API Routes

### 1. Login Endpoint (`src/app/api/auth/login/route.ts`)

```typescript
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validationResult = loginSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json({
        success: false,
        error: { code: 'VALIDATION_ERROR', message: 'Data tidak valid' }
      }, { status: 400 });
    }
    
    const { email, password } = validationResult.data;
    
    // Authenticate user
    const user = await authenticateUser(email, password);
    if (!user) {
      return NextResponse.json({
        success: false,
        error: { code: 'INVALID_CREDENTIALS', message: 'Email atau password tidak valid' }
      }, { status: 401 });
    }
    
    // Create session
    const sessionId = await createSession(user.id);
    await setSessionCookie(sessionId);
    
    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          isSuperAdmin: user.isSuperAdmin,
        },
        role: user.role,
      },
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Terjadi kesalahan internal server' }
    }, { status: 500 });
  }
}
```

### 2. Logout Endpoint (`src/app/api/auth/logout/route.ts`)

```typescript
export async function POST(request: NextRequest) {
  try {
    const sessionId = await getSessionCookie();
    
    if (sessionId) {
      await deleteSession(sessionId);
    }
    
    await deleteSessionCookie();
    
    return NextResponse.json({
      success: true,
      message: 'Logout berhasil',
    });
  } catch (error) {
    await deleteSessionCookie();
    return NextResponse.json({
      success: false,
      error: { code: 'LOGOUT_ERROR', message: 'Terjadi kesalahan saat logout' }
    }, { status: 500 });
  }
}
```

### 3. User Info Endpoint (`src/app/api/auth/me/route.ts`)

```typescript
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({
        success: false,
        error: { code: 'NOT_AUTHENTICATED', message: 'User not authenticated' }
      }, { status: 401 });
    }
    
    return NextResponse.json({
      success: true,
      data: { user }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
    }, { status: 500 });
  }
}
```

## Form Components

### 1. Login Form (`src/components/forms/LoginForm.tsx`)

```typescript
export function LoginForm() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: formData.email.toLowerCase().trim(),
          password: formData.password,
        }),
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error?.message || 'Login gagal');
      }
      
      if (result.success) {
        // Redirect based on user role
        if (result.data.role === 'teacher') {
          router.push('/guru');
        } else if (result.data.role === 'student') {
          router.push('/siswa');
        }
        router.refresh();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Terjadi kesalahan saat login');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Masuk ke GuruFlow</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              disabled={isLoading}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={formData.password}
              onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
              disabled={isLoading}
              required
            />
          </div>
          
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? 'Memproses...' : 'Masuk'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
```

### 2. Logout Button (`src/components/auth/LogoutButton.tsx`)

```typescript
export function LogoutButton() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleLogout = async () => {
    setIsLoading(true);
    
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });
      
      router.push('/login');
      router.refresh();
    } catch (error) {
      console.error('Logout error:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button variant="ghost" onClick={handleLogout} disabled={isLoading}>
      {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <LogOut className="h-4 w-4" />}
      <span className="ml-2">Keluar</span>
    </Button>
  );
}
```

## Permission System

### Resource-Based Permissions
```typescript
export function hasPermission(
  user: AuthenticatedUser,
  resource: string,
  action: 'read' | 'write' | 'delete'
): boolean {
  switch (resource) {
    case 'classroom':
      return user.role === 'teacher';
    
    case 'student':
      if (action === 'read') return true;
      return user.role === 'teacher';
    
    case 'attendance':
      if (action === 'read') return true;
      return user.role === 'teacher';
    
    case 'school_settings':
      return user.role === 'teacher' && user.isSuperAdmin;
    
    default:
      return false;
  }
}
```

### Permission Matrix

| Resource | Teacher Read | Teacher Write | Student Read | Student Write |
|----------|--------------|---------------|--------------|---------------|
| classroom | ✅ | ✅ | ❌ | ❌ |
| student | ✅ | ✅ | ✅ | ❌ |
| attendance | ✅ | ✅ | ✅ | ❌ |
| assignment | ✅ | ✅ | ✅ | ❌ |
| grade | ✅ | ✅ | ✅ | ❌ |
| school_settings | ✅ (superadmin) | ✅ (superadmin) | ❌ | ❌ |

## Usage Examples

### 1. Protecting Pages
```typescript
// Teacher-only page
export default async function TeacherPage() {
  const user = await requireTeacher();
  
  return (
    <div>
      <h1>Welcome, {user.name}</h1>
      {/* Teacher content */}
    </div>
  );
}

// Student-only page
export default async function StudentPage() {
  const user = await requireStudent();
  
  return (
    <div>
      <h1>Welcome, {user.name}</h1>
      {/* Student content */}
    </div>
  );
}
```

### 2. Protecting API Routes
```typescript
// Teacher-only API endpoint
export const POST = withTeacher(async (request, user) => {
  // Only teachers can access this
  return NextResponse.json({ success: true });
});

// Permission-based API endpoint
export const GET = withPermission('attendance', 'read', async (request, user) => {
  // Both teachers and students can read attendance
  return NextResponse.json({ records: [] });
});
```

### 3. Client-Side Protection
```typescript
function MyComponent() {
  const { user, loading, hasPermission } = useAuth();
  
  if (loading) return <div>Loading...</div>;
  if (!user) return <div>Please login</div>;
  
  return (
    <div>
      <h1>Welcome, {user.name}</h1>
      {hasPermission('classroom', 'write') && (
        <button>Create Classroom</button>
      )}
    </div>
  );
}
```

## Security Best Practices

### 1. Session Security
- HTTP-only cookies untuk mencegah XSS
- Secure flag untuk HTTPS
- SameSite protection
- Session expiration (30 hari)
- Session cleanup otomatis

### 2. Password Security
- bcrypt dengan salt rounds 12
- Password strength requirements
- No password storage in plain text
- Secure password reset flow

### 3. Route Protection
- Middleware-level protection
- Server-side validation
- Client-side guards untuk UX
- Role-based access control

### 4. API Security
- Authentication required untuk protected endpoints
- Role validation pada setiap request
- Input validation dengan Zod
- Error handling yang aman

## Testing

### Test Credentials
```typescript
// Created by scripts/create-test-user.ts
const testCredentials = {
  teacher: {
    email: '<EMAIL>',
    password: 'password123',
    role: 'teacher',
    isSuperAdmin: true
  },
  student: {
    email: '<EMAIL>',
    password: 'password123',
    role: 'student',
    isSuperAdmin: false
  }
};
```

### Manual Testing
1. **Login Flow**
   - Test dengan credentials yang valid
   - Test dengan credentials yang invalid
   - Test redirect berdasarkan role

2. **Session Management**
   - Test session persistence
   - Test session expiration
   - Test logout functionality

3. **Route Protection**
   - Test akses ke protected routes tanpa auth
   - Test akses dengan role yang salah
   - Test middleware redirect

## Troubleshooting

### Common Issues

1. **Session Cookie Not Set**
   ```typescript
   // Check cookie configuration
   cookieStore.set(SESSION_COOKIE_NAME, sessionId, {
     httpOnly: true,
     secure: process.env.NODE_ENV === 'production', // Important!
     sameSite: 'lax',
     path: '/',
   });
   ```

2. **Middleware Redirect Loop**
   ```typescript
   // Ensure proper route exclusions
   if (pathname.startsWith('/api') && pathname !== '/api/auth/redirect') {
     return NextResponse.next();
   }
   ```

3. **Authentication State Issues**
   ```typescript
   // Check if useAuth hook is properly initialized
   useEffect(() => {
     checkAuth();
   }, []); // Empty dependency array
   ```

### Debug Tips

1. **Check Session in Database**
   ```bash
   bun run db:studio
   # Check sessions table for active sessions
   ```

2. **Inspect Cookies**
   ```javascript
   // In browser console
   document.cookie
   ```

3. **Test API Endpoints**
   ```bash
   # Test login
   curl -X POST http://localhost:3000/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password123"}'
   ```

## Next Steps

Setelah authentication system selesai, lanjutkan ke:
1. [Task 4 - School Onboarding System](./task-4-onboarding-system.md)
2. [Task 5 - Dashboard Implementation](./task-5-dashboard-implementation.md)

## Resources

- [Next.js Authentication](https://nextjs.org/docs/app/building-your-application/authentication)
- [HTTP-only Cookies](https://developer.mozilla.org/en-US/docs/Web/HTTP/Cookies)
- [bcrypt Documentation](https://www.npmjs.com/package/bcryptjs)
- [Session Management Best Practices](https://cheatsheetseries.owasp.org/cheatsheets/Session_Management_Cheat_Sheet.html)