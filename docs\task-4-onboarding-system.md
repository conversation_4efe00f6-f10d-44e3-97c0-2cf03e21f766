# Task 4 - School Onboarding System

## Overview

Task ini mengimplementasikan sistem onboarding untuk setup awal sekolah, termasuk wizard multi-step untuk konfigurasi sekolah dan pembuatan akun administrator pertama.

## Arsitektur Onboarding

### Flow Diagram
```mermaid
sequenceDiagram
    participant User
    participant Client
    participant API
    participant Database
    
    User->>Client: Access GuruFlow first time
    Client->>API: Check onboarding status
    API->>Database: Query schools table
    Database-->>API: No schools found
    API-->>Client: Redirect to onboarding
    
    User->>Client: Complete school setup
    User->>Client: Complete teacher setup
    Client->>API: POST /api/onboarding
    API->>Database: Create school & teacher
    API->>Database: Create session
    API-->>Client: Success + session cookie
    Client-->>User: Redirect to dashboard
```

## Komponen Utama

### 1. Onboarding Wizard (`src/components/onboarding/OnboardingWizard.tsx`)

Main wizard component yang mengatur flow multi-step:

```typescript
export function OnboardingWizard() {
  const [currentStep, setCurrentStep] = useState(0);
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    school: { name: '', level: 'SD', logo: null, signature: null },
    teacher: { name: '', email: '', password: '', confirmPassword: '' },
  });

  const STEPS = [
    { id: 'school', title: 'Informasi Sekolah' },
    { id: 'teacher', title: 'Akun Administrator' },
    { id: 'complete', title: 'Selesai' },
  ];

  const submitOnboarding = async () => {
    const formData = new FormData();
    formData.append('schoolName', onboardingData.school.name);
    formData.append('schoolLevel', onboardingData.school.level);
    // ... append other fields and files
    
    const response = await fetch('/api/onboarding', {
      method: 'POST',
      body: formData,
    });
  };
}
```
### 2. Scho
ol Setup Step (`src/components/onboarding/steps/SchoolSetupStep.tsx`)

Form untuk mengumpulkan informasi sekolah:

```typescript
export function SchoolSetupStep({ data, onUpdate, onNext }: SchoolSetupStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  const handleFileChange = (field: 'logo' | 'signature', file: File | null) => {
    onUpdate({ ...data, [field]: file });
    
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        if (field === 'logo') setLogoPreview(result);
      };
      reader.readAsDataURL(file);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!data.name.trim()) {
      newErrors.name = 'Nama sekolah wajib diisi';
    }
    
    if (!data.level) {
      newErrors.level = 'Jenjang sekolah wajib dipilih';
    }
    
    // File validation
    if (data.logo && data.logo.size > 2 * 1024 * 1024) {
      newErrors.logo = 'Ukuran logo maksimal 2MB';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
}
```

**Fitur:**
- Input nama sekolah dengan validasi
- Dropdown jenjang sekolah (SD/SMP/SMA/SMK)
- Upload logo sekolah (opsional, max 2MB)
- Upload tanda tangan kepala sekolah (opsional, max 1MB)
- Preview gambar yang diupload
- Validasi file type dan size

### 3. Teacher Setup Step (`src/components/onboarding/steps/TeacherSetupStep.tsx`)

Form untuk membuat akun administrator pertama:

```typescript
export function TeacherSetupStep({ data, onUpdate, onNext, isSubmitting }: TeacherSetupStepProps) {
  const [showPassword, setShowPassword] = useState(false);
  
  const getPasswordStrength = () => {
    let score = 0;
    if (data.password.length >= 8) score++;
    if (/[A-Z]/.test(data.password)) score++;
    if (/[a-z]/.test(data.password)) score++;
    if (/\d/.test(data.password)) score++;
    
    if (score <= 2) return { strength: 25, label: 'Lemah', color: 'bg-red-500' };
    if (score === 3) return { strength: 50, label: 'Sedang', color: 'bg-yellow-500' };
    if (score === 4) return { strength: 75, label: 'Kuat', color: 'bg-blue-500' };
    return { strength: 100, label: 'Sangat Kuat', color: 'bg-green-500' };
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!data.name.trim() || data.name.length < 2) {
      newErrors.name = 'Nama guru minimal 2 karakter';
    }
    
    if (!data.email.trim() || !emailRegex.test(data.email)) {
      newErrors.email = 'Format email tidak valid';
    }
    
    if (data.password.length < 8) {
      newErrors.password = 'Password minimal 8 karakter';
    }
    
    if (data.password !== data.confirmPassword) {
      newErrors.confirmPassword = 'Password tidak cocok';
    }
    
    return Object.keys(newErrors).length === 0;
  };
}
```

**Fitur:**
- Input nama lengkap guru
- Input email dengan validasi format
- Password dengan strength indicator
- Konfirmasi password
- Show/hide password toggle
- Real-time password strength checking
- Comprehensive validation

### 4. Completion Step (`src/components/onboarding/steps/CompletionStep.tsx`)

Halaman konfirmasi dan panduan langkah selanjutnya:

```typescript
export function CompletionStep({ schoolName, teacherName, teacherEmail, onComplete }: CompletionStepProps) {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          Selamat! Setup Berhasil
        </h3>
      </div>

      {/* School Info Summary */}
      <Card className="border-green-200 bg-green-50">
        <CardHeader>
          <CardTitle className="text-green-800">Informasi Sekolah</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Nama Sekolah:</span>
              <span>{schoolName}</span>
            </div>
            <div className="flex justify-between">
              <span>Status:</span>
              <span className="font-medium">Aktif</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Next Steps Guide */}
      <Card className="border-purple-200 bg-purple-50">
        <CardHeader>
          <CardTitle className="text-purple-800">Langkah Selanjutnya</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">1</div>
              <div>
                <p className="font-medium">Login ke Dashboard</p>
                <p className="text-sm text-purple-600">Gunakan email dan password yang telah dibuat</p>
              </div>
            </div>
            {/* More steps... */}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

**Fitur:**
- Konfirmasi setup berhasil
- Ringkasan informasi sekolah dan admin
- Panduan langkah selanjutnya
- Visual feedback dengan icons dan colors

## API Implementation

### 1. Onboarding API (`src/app/api/onboarding/route.ts`)

#### POST - Process Onboarding
```typescript
export async function POST(request: NextRequest) {
  try {
    // Check if already onboarded
    const existingSchools = await db.select().from(schools).limit(1);
    if (existingSchools.length > 0) {
      return NextResponse.json({
        success: false,
        error: { code: 'ALREADY_ONBOARDED', message: 'Onboarding sudah pernah dilakukan' }
      }, { status: 400 });
    }

    // Parse form data (including file uploads)
    const formData = await request.formData();
    const onboardingData = {
      schoolName: formData.get('schoolName') as string,
      schoolLevel: formData.get('schoolLevel') as string,
      teacherName: formData.get('teacherName') as string,
      teacherEmail: formData.get('teacherEmail') as string,
      teacherPassword: formData.get('teacherPassword') as string,
    };

    // Validate data
    const validationResult = schoolOnboardingSchema.safeParse(onboardingData);
    if (!validationResult.success) {
      return NextResponse.json({
        success: false,
        error: { code: 'VALIDATION_ERROR', message: 'Data tidak valid' }
      }, { status: 400 });
    }

    // Database transaction
    const result = await db.transaction(async (tx) => {
      // Create school
      const schoolResult = await tx.insert(schools).values({
        name: schoolName,
        level: schoolLevel,
        logo: logoUrl,
        signature: signatureUrl,
      }).returning();

      // Create teacher with superadmin privileges
      const passwordHash = await hashPassword(teacherPassword);
      const userResult = await tx.insert(users).values({
        email: teacherEmail.toLowerCase(),
        passwordHash,
        role: 'teacher',
        name: teacherName,
        isActive: true,
        isSuperAdmin: true,
      }).returning();

      return { school: schoolResult[0], teacher: userResult[0] };
    });

    // Create session for new teacher
    const sessionId = await createSession(result.teacher.id);
    await setSessionCookie(sessionId);

    return NextResponse.json({
      success: true,
      data: {
        school: result.school,
        teacher: result.teacher,
        message: 'Onboarding berhasil diselesaikan',
      },
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Terjadi kesalahan saat proses onboarding' }
    }, { status: 500 });
  }
}
```

#### GET - Check Onboarding Status
```typescript
export async function GET(request: NextRequest) {
  try {
    const existingSchools = await db.select().from(schools).limit(1);
    
    return NextResponse.json({
      success: true,
      data: {
        isOnboarded: existingSchools.length > 0,
        schoolCount: existingSchools.length,
      },
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal memeriksa status onboarding' }
    }, { status: 500 });
  }
}
```

### 2. Onboarding Utilities (`src/lib/onboarding.ts`)

```typescript
export async function isSystemOnboarded(): Promise<boolean> {
  try {
    const existingSchools = await db.select().from(schools).limit(1);
    return existingSchools.length > 0;
  } catch (error) {
    console.error('Error checking onboarding status:', error);
    return false; // Safe default
  }
}

export async function getSchoolInfo() {
  try {
    const schools_result = await db.select().from(schools).limit(1);
    return schools_result.length > 0 ? schools_result[0] : null;
  } catch (error) {
    console.error('Error getting school info:', error);
    return null;
  }
}
```

## Validation Schema

### School Onboarding Schema (`src/lib/validations.ts`)
```typescript
export const schoolOnboardingSchema = z.object({
  schoolName: z
    .string()
    .min(1, 'Nama sekolah wajib diisi')
    .min(3, 'Nama sekolah minimal 3 karakter')
    .max(200, 'Nama sekolah maksimal 200 karakter'),
  schoolLevel: z
    .enum(['SD', 'SMP', 'SMA', 'SMK'])
    .refine((val) => ['SD', 'SMP', 'SMA', 'SMK'].includes(val), 'Jenjang sekolah tidak valid'),
  teacherName: z
    .string()
    .min(1, 'Nama guru wajib diisi')
    .min(2, 'Nama guru minimal 2 karakter')
    .max(100, 'Nama guru maksimal 100 karakter'),
  teacherEmail: z
    .string()
    .min(1, 'Email guru wajib diisi')
    .email('Format email tidak valid'),
  teacherPassword: z
    .string()
    .min(8, 'Password minimal 8 karakter')
    .regex(/[A-Z]/, 'Password harus mengandung huruf besar')
    .regex(/[a-z]/, 'Password harus mengandung huruf kecil')
    .regex(/[0-9]/, 'Password harus mengandung angka'),
});
```

## Tutorial Penggunaan

### 1. First Time Access
1. User mengakses GuruFlow untuk pertama kali
2. Sistem mengecek apakah sudah ada sekolah terdaftar
3. Jika belum, redirect otomatis ke `/onboarding`
4. Wizard onboarding dimulai

### 2. School Setup (Step 1)
1. Input nama sekolah
2. Pilih jenjang pendidikan (SD/SMP/SMA/SMK)
3. Upload logo sekolah (opsional)
4. Upload tanda tangan kepala sekolah (opsional)
5. Klik "Lanjutkan"

### 3. Teacher Setup (Step 2)
1. Input nama lengkap guru
2. Input email untuk login
3. Buat password yang kuat (min 8 karakter, huruf besar/kecil, angka)
4. Konfirmasi password
5. Klik "Selesaikan Setup"

### 4. Completion (Step 3)
1. Lihat ringkasan informasi yang telah diinput
2. Baca panduan langkah selanjutnya
3. Klik "Masuk ke Dashboard"
4. Otomatis login sebagai superadmin

## Integration dengan Sistem Lain

### 1. Middleware Integration
```typescript
// middleware.ts
export async function middleware(request: NextRequest) {
  // Check onboarding status for non-API routes
  if (!pathname.startsWith('/api')) {
    try {
      const onboardingResponse = await fetch('/api/onboarding');
      const onboardingData = await onboardingResponse.json();
      const isOnboarded = onboardingData.data?.isOnboarded || false;
      
      // Redirect to onboarding if not completed
      if (!isOnboarded && pathname !== '/onboarding') {
        return NextResponse.redirect(new URL('/onboarding', request.url));
      }
      
      // Redirect to login if onboarding completed
      if (isOnboarded && pathname === '/onboarding') {
        return NextResponse.redirect(new URL('/login', request.url));
      }
    } catch (error) {
      console.error('Error checking onboarding status:', error);
    }
  }
}
```

### 2. Landing Page Integration
```typescript
// src/app/page.tsx
export default async function HomePage() {
  // Check if user is authenticated
  const user = await getCurrentUser();
  if (user) {
    // Redirect to appropriate dashboard
    if (user.role === 'teacher') redirect('/guru');
    if (user.role === 'student') redirect('/siswa');
  }
  
  // Check if system needs onboarding
  const isOnboarded = await isSystemOnboarded();
  if (!isOnboarded) {
    redirect('/onboarding');
  }
  
  // Show landing page for onboarded but not logged in users
  return <LandingPageContent />;
}
```

### 3. Authentication Flow Integration
```typescript
// src/app/(auth)/onboarding/page.tsx
export default async function OnboardingPage() {
  // Check if user is already authenticated
  const user = await getCurrentUser();
  if (user) {
    // Redirect to appropriate dashboard
    if (user.role === 'teacher') redirect('/guru');
    if (user.role === 'student') redirect('/siswa');
  }

  // Check if onboarding already completed
  const existingSchools = await db.select().from(schools).limit(1);
  if (existingSchools.length > 0) {
    redirect('/login');
  }

  return <OnboardingWizard />;
}
```

## File Upload Handling

### Current Implementation
```typescript
// In API route
const logoFile = formData.get('logo') as File | null;
const signatureFile = formData.get('signature') as File | null;

// TODO: Implement actual file upload
if (logoFile && logoFile.size > 0) {
  // 1. Validate file type and size
  // 2. Upload to storage (S3, Cloudflare R2, etc.)
  // 3. Get URL of uploaded file
  console.log('Logo file received:', logoFile.name, logoFile.size);
}
```

### Future Implementation
```typescript
// Example with Cloudflare R2
async function uploadFile(file: File, path: string): Promise<string> {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData,
  });
  
  const result = await response.json();
  return result.url;
}
```

## UI Components

### Progress Indicator
```typescript
// Visual progress dengan step indicators
<div className="flex justify-between items-center mb-4">
  {STEPS.map((step, index) => (
    <div key={step.id} className="flex items-center">
      <div className={`w-10 h-10 rounded-full border-2 ${
        index <= currentStep
          ? 'bg-primary-600 border-primary-600 text-white'
          : 'bg-white border-gray-300 text-gray-400'
      }`}>
        {index < currentStep ? (
          <CheckCircle className="w-6 h-6" />
        ) : (
          <span>{index + 1}</span>
        )}
      </div>
      {index < STEPS.length - 1 && (
        <div className={`flex-1 h-1 mx-4 ${
          index < currentStep ? 'bg-primary-600' : 'bg-gray-200'
        }`} />
      )}
    </div>
  ))}
</div>
```

### File Upload Component
```typescript
// Drag & drop file upload dengan preview
<div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
  {logoPreview ? (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-3">
        <img src={logoPreview} alt="Logo preview" className="w-12 h-12 object-cover rounded" />
        <div>
          <p className="text-sm font-medium">{data.logo?.name}</p>
          <p className="text-xs text-gray-500">{(data.logo.size / 1024).toFixed(1)} KB</p>
        </div>
      </div>
      <Button onClick={() => handleFileChange('logo', null)}>
        <X className="w-4 h-4" />
      </Button>
    </div>
  ) : (
    <div className="text-center">
      <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
      <p className="text-sm text-gray-600">Klik untuk upload logo sekolah</p>
      <input type="file" accept="image/*" onChange={handleFileUpload} />
    </div>
  )}
</div>
```

## Testing

### Manual Testing Flow
1. **Reset Database** (development only)
   ```bash
   # Clear schools table to trigger onboarding
   bun run db:studio
   # Delete all records from schools table
   ```

2. **Test Onboarding Flow**
   - Access `http://localhost:3000`
   - Should redirect to `/onboarding`
   - Complete all steps
   - Should redirect to teacher dashboard

3. **Test Validation**
   - Try submitting empty forms
   - Test password strength requirements
   - Test file upload validation

### Automated Testing
```typescript
// Example test for onboarding API
describe('/api/onboarding', () => {
  it('should create school and teacher', async () => {
    const formData = new FormData();
    formData.append('schoolName', 'Test School');
    formData.append('schoolLevel', 'SD');
    formData.append('teacherName', 'Test Teacher');
    formData.append('teacherEmail', '<EMAIL>');
    formData.append('teacherPassword', 'Password123');
    
    const response = await fetch('/api/onboarding', {
      method: 'POST',
      body: formData,
    });
    
    expect(response.status).toBe(200);
    const result = await response.json();
    expect(result.success).toBe(true);
  });
});
```

## Troubleshooting

### Common Issues

1. **Onboarding Loop**
   ```typescript
   // Check if schools table has data
   const schools = await db.select().from(schools);
   console.log('Schools count:', schools.length);
   ```

2. **File Upload Issues**
   ```typescript
   // Check file size and type
   if (file.size > 2 * 1024 * 1024) {
     throw new Error('File too large');
   }
   
   if (!['image/jpeg', 'image/png'].includes(file.type)) {
     throw new Error('Invalid file type');
   }
   ```

3. **Session Creation Issues**
   ```typescript
   // Check if session is created after onboarding
   const sessions = await db.select().from(sessions).where(eq(sessions.userId, teacherId));
   console.log('Sessions for teacher:', sessions);
   ```

## Security Considerations

### 1. First-Time Setup Security
- Onboarding hanya bisa dilakukan sekali
- Validation untuk prevent duplicate setup
- Automatic superadmin privileges untuk teacher pertama

### 2. File Upload Security
- File type validation (hanya image)
- File size limits (logo: 2MB, signature: 1MB)
- Secure file storage (TODO: implement S3/R2)

### 3. Data Validation
- Server-side validation dengan Zod
- Email uniqueness check
- Password strength requirements

## Next Steps

Setelah onboarding system selesai, lanjutkan ke:
1. [Task 5 - Dashboard Implementation](./task-5-dashboard-implementation.md)
2. [Task 6 - Classroom Management](./task-6-classroom-management.md)

## Resources

- [Multi-step Forms Best Practices](https://uxdesign.cc/design-better-forms-96fadca0f49c)
- [File Upload Security](https://cheatsheetseries.owasp.org/cheatsheets/File_Upload_Cheat_Sheet.html)
- [Form Validation Patterns](https://react-hook-form.com/get-started)