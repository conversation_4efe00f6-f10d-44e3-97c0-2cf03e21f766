# Task 5: Dashboard Implementation

## 📋 Overview

Task ini mengimplementasikan dashboard untuk dua role utama: Teacher (Guru) dan <PERSON> (Siswa). Dashboard dirancang dengan pendekatan role-based interface yang memberikan pengalaman yang sesuai dengan kebutuhan masing-masing pengguna.

## ✅ Komponen yang Diimplementasi

### 5.1 Teacher Dashboard Layout and Components ✅
### 5.2 Student Dashboard Layout and Components ✅

## 🏗️ Arsitektur Dashboard

### Dashboard Flow
```mermaid
graph TD
    A[User Login] --> B{Check Role}
    B -->|Teacher| C[Teacher Dashboard]
    B -->|Student| D[Student Dashboard]
    
    C --> E[Teaching Schedule]
    C --> F[Quick Actions]
    C --> G[XP Leaderboard]
    C --> H[Stats Cards]
    
    D --> I[Active Assignments]
    D --> J[Academic Progress]
    D --> K[Gamification Stats]
    D --> L[Personal Stats]
```

## 🎯 Teacher Dashboard Components

### 1. Dashboard Layout
**File**: `src/components/layout/DashboardLayout.tsx`

```typescript
'use client';

import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { MobileNav } from './MobileNav';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <MobileNav 
        isOpen={sidebarOpen} 
        onClose={() => setSidebarOpen(false)}
        userRole={user?.role}
      />
      
      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <Sidebar userRole={user?.role} />
      </div>
      
      {/* Main content */}
      <div className="lg:pl-64">
        <Header 
          onMenuClick={() => setSidebarOpen(true)}
          user={user}
        />
        
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
```

**Fitur:**
- Responsive layout dengan sidebar collapse
- Role-based navigation menu
- Mobile-first design dengan hamburger menu
- Consistent header dan footer
- Auto-collapse sidebar pada mobile

### 2. Teacher Dashboard Page
**File**: `src/app/(dashboard)/guru/page.tsx`

```typescript
import { getCurrentUser } from '@/lib/auth';
import { redirect } from 'next/navigation';
import { StatsCards } from '@/components/dashboard/StatsCards';
import { TeachingSchedule } from '@/components/dashboard/TeachingSchedule';
import { QuickActions } from '@/components/dashboard/QuickActions';
import { XPLeaderboard } from '@/components/dashboard/XPLeaderboard';

export default async function TeacherDashboard() {
  const user = await getCurrentUser();
  
  if (!user || user.role !== 'teacher') {
    redirect('/login');
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-900">
          Selamat datang, {user.name}!
        </h1>
        <p className="text-gray-600 mt-1">
          Kelola kelas dan siswa Anda dengan mudah
        </p>
      </div>

      {/* Stats Overview */}
      <StatsCards />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Schedule & Actions */}
        <div className="lg:col-span-2 space-y-6">
          <TeachingSchedule />
          <QuickActions />
        </div>
        
        {/* Right Column - Leaderboard */}
        <div className="lg:col-span-1">
          <XPLeaderboard />
        </div>
      </div>
    </div>
  );
}
```

### 3. Stats Cards Component
**File**: `src/components/dashboard/StatsCards.tsx`

```typescript
'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, BookOpen, Calendar, TrendingUp } from 'lucide-react';

interface DashboardStats {
  totalStudents: number;
  totalClasses: number;
  todayAttendance: number;
  averageAttendance: number;
}

export function StatsCards() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/dashboard/stats');
      const data = await response.json();
      
      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const statItems = [
    {
      title: 'Total Siswa',
      value: stats?.totalStudents || 0,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Total Kelas',
      value: stats?.totalClasses || 0,
      icon: BookOpen,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Kehadiran Hari Ini',
      value: `${stats?.todayAttendance || 0}%`,
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      title: 'Rata-rata Kehadiran',
      value: `${stats?.averageAttendance || 0}%`,
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {statItems.map((item, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  {item.title}
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {item.value}
                </p>
              </div>
              <div className={`p-3 rounded-full ${item.bgColor}`}>
                <item.icon className={`h-6 w-6 ${item.color}`} />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
```

### 4. Teaching Schedule Component
**File**: `src/components/dashboard/TeachingSchedule.tsx`

```typescript
'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, MapPin, Users } from 'lucide-react';

interface ScheduleItem {
  id: number;
  subject: string;
  classroom: string;
  time: string;
  duration: string;
  studentCount: number;
  status: 'upcoming' | 'ongoing' | 'completed';
}

export function TeachingSchedule() {
  const [schedule, setSchedule] = useState<ScheduleItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchTodaySchedule();
  }, []);

  const fetchTodaySchedule = async () => {
    try {
      const response = await fetch('/api/dashboard/schedule');
      const data = await response.json();
      
      if (data.success) {
        setSchedule(data.data);
      }
    } catch (error) {
      console.error('Error fetching schedule:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'bg-blue-100 text-blue-800';
      case 'ongoing': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'upcoming': return 'Akan Datang';
      case 'ongoing': return 'Sedang Berlangsung';
      case 'completed': return 'Selesai';
      default: return 'Unknown';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Clock className="h-5 w-5" />
          <span>Jadwal Mengajar Hari Ini</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        ) : schedule.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Tidak ada jadwal mengajar hari ini</p>
          </div>
        ) : (
          <div className="space-y-4">
            {schedule.map((item) => (
              <div
                key={item.id}
                className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-semibold text-gray-900">
                        {item.subject}
                      </h4>
                      <Badge className={getStatusColor(item.status)}>
                        {getStatusText(item.status)}
                      </Badge>
                    </div>
                    
                    <div className="space-y-1 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4" />
                        <span>{item.classroom}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>{item.time} ({item.duration})</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4" />
                        <span>{item.studentCount} siswa</span>
                      </div>
                    </div>
                  </div>
                  
                  {item.status === 'upcoming' && (
                    <div className="flex space-x-2">
                      <button className="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                        Mulai Kelas
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
```

### 5. Quick Actions Component
**File**: `src/components/dashboard/QuickActions.tsx`

```typescript
'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  UserPlus, 
  Calendar, 
  FileText, 
  BarChart3, 
  Settings,
  BookOpen 
} from 'lucide-react';
import Link from 'next/link';

export function QuickActions() {
  const actions = [
    {
      title: 'Catat Absensi',
      description: 'Catat kehadiran siswa hari ini',
      icon: Calendar,
      href: '/guru/absensi',
      color: 'bg-blue-600 hover:bg-blue-700',
    },
    {
      title: 'Tambah Siswa',
      description: 'Daftarkan siswa baru ke kelas',
      icon: UserPlus,
      href: '/guru/siswa/tambah',
      color: 'bg-green-600 hover:bg-green-700',
    },
    {
      title: 'Buat Tugas',
      description: 'Buat tugas baru untuk siswa',
      icon: FileText,
      href: '/guru/tugas/buat',
      color: 'bg-purple-600 hover:bg-purple-700',
    },
    {
      title: 'Lihat Laporan',
      description: 'Analisis performa dan kehadiran',
      icon: BarChart3,
      href: '/guru/laporan',
      color: 'bg-orange-600 hover:bg-orange-700',
    },
    {
      title: 'Kelola Kelas',
      description: 'Atur kelas dan mata pelajaran',
      icon: BookOpen,
      href: '/guru/kelas',
      color: 'bg-indigo-600 hover:bg-indigo-700',
    },
    {
      title: 'Pengaturan',
      description: 'Konfigurasi sistem sekolah',
      icon: Settings,
      href: '/guru/pengaturan',
      color: 'bg-gray-600 hover:bg-gray-700',
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Aksi Cepat</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {actions.map((action, index) => (
            <Link key={index} href={action.href}>
              <Button
                variant="outline"
                className="h-auto p-4 flex flex-col items-start space-y-2 hover:shadow-md transition-all"
              >
                <div className={`p-2 rounded-lg ${action.color} text-white`}>
                  <action.icon className="h-5 w-5" />
                </div>
                <div className="text-left">
                  <h4 className="font-semibold text-gray-900">
                    {action.title}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {action.description}
                  </p>
                </div>
              </Button>
            </Link>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
```

### 6. XP Leaderboard Component
**File**: `src/components/dashboard/XPLeaderboard.tsx`

```typescript
'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Trophy, Medal, Award, Star } from 'lucide-react';

interface LeaderboardEntry {
  id: number;
  name: string;
  className: string;
  totalXP: number;
  level: number;
  rank: number;
  avatar?: string;
}

export function XPLeaderboard() {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchLeaderboard();
  }, []);

  const fetchLeaderboard = async () => {
    try {
      const response = await fetch('/api/dashboard/leaderboard');
      const data = await response.json();
      
      if (data.success) {
        setLeaderboard(data.data);
      }
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 2: return <Medal className="h-5 w-5 text-gray-400" />;
      case 3: return <Award className="h-5 w-5 text-orange-500" />;
      default: return <Star className="h-5 w-5 text-blue-500" />;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1: return 'bg-yellow-100 text-yellow-800';
      case 2: return 'bg-gray-100 text-gray-800';
      case 3: return 'bg-orange-100 text-orange-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Trophy className="h-5 w-5" />
          <span>Leaderboard XP</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : leaderboard.length === 0 ? (
          <div className="text-center py-8">
            <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Belum ada data XP siswa</p>
          </div>
        ) : (
          <div className="space-y-3">
            {leaderboard.map((entry) => (
              <div
                key={entry.id}
                className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-center w-8 h-8">
                  {getRankIcon(entry.rank)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <p className="font-semibold text-gray-900 truncate">
                      {entry.name}
                    </p>
                    <Badge className={getRankColor(entry.rank)}>
                      #{entry.rank}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">
                    {entry.className} • Level {entry.level}
                  </p>
                </div>
                
                <div className="text-right">
                  <p className="font-bold text-blue-600">
                    {entry.totalXP.toLocaleString()} XP
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
```

## 🎓 Student Dashboard Components

### 1. Student Dashboard Page
**File**: `src/app/(dashboard)/siswa/page.tsx`

```typescript
import { getCurrentUser } from '@/lib/auth';
import { redirect } from 'next/navigation';
import { ActiveAssignments } from '@/components/dashboard/ActiveAssignments';
import { AcademicProgress } from '@/components/dashboard/AcademicProgress';
import { GamificationStats } from '@/components/dashboard/GamificationStats';

export default async function StudentDashboard() {
  const user = await getCurrentUser();
  
  if (!user || user.role !== 'student') {
    redirect('/login');
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow p-6 text-white">
        <h1 className="text-2xl font-bold">
          Halo, {user.name}! 👋
        </h1>
        <p className="mt-1 opacity-90">
          Semangat belajar hari ini! Lihat tugas dan progress kamu di bawah.
        </p>
      </div>

      {/* Gamification Stats */}
      <GamificationStats />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column - Assignments */}
        <div className="space-y-6">
          <ActiveAssignments />
        </div>
        
        {/* Right Column - Progress */}
        <div className="space-y-6">
          <AcademicProgress />
        </div>
      </div>
    </div>
  );
}
```

### 2. Active Assignments Component
**File**: `src/components/dashboard/ActiveAssignments.tsx`

```typescript
'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { FileText, Clock, Calendar, CheckCircle } from 'lucide-react';
import Link from 'next/link';

interface Assignment {
  id: number;
  title: string;
  subject: string;
  dueDate: string;
  status: 'pending' | 'submitted' | 'graded';
  score?: number;
  maxScore: number;
  xpReward: number;
}

export function ActiveAssignments() {
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAssignments();
  }, []);

  const fetchAssignments = async () => {
    try {
      const response = await fetch('/api/dashboard/assignments');
      const data = await response.json();
      
      if (data.success) {
        setAssignments(data.data);
      }
    } catch (error) {
      console.error('Error fetching assignments:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-red-100 text-red-800';
      case 'submitted': return 'bg-yellow-100 text-yellow-800';
      case 'graded': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Belum Dikerjakan';
      case 'submitted': return 'Sudah Dikumpulkan';
      case 'graded': return 'Sudah Dinilai';
      default: return 'Unknown';
    }
  };

  const getDaysUntilDue = (dueDate: string) => {
    const due = new Date(dueDate);
    const now = new Date();
    const diffTime = due.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'Terlambat';
    if (diffDays === 0) return 'Hari ini';
    if (diffDays === 1) return 'Besok';
    return `${diffDays} hari lagi`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <FileText className="h-5 w-5" />
          <span>Tugas Aktif</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        ) : assignments.length === 0 ? (
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
            <p className="text-gray-500">Tidak ada tugas aktif</p>
            <p className="text-sm text-gray-400">Semua tugas sudah selesai!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {assignments.map((assignment) => (
              <div
                key={assignment.id}
                className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-1">
                      {assignment.title}
                    </h4>
                    <p className="text-sm text-gray-600 mb-2">
                      {assignment.subject}
                    </p>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>{getDaysUntilDue(assignment.dueDate)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{assignment.xpReward} XP</span>
                      </div>
                    </div>
                  </div>
                  
                  <Badge className={getStatusColor(assignment.status)}>
                    {getStatusText(assignment.status)}
                  </Badge>
                </div>
                
                {assignment.status === 'graded' && assignment.score !== undefined && (
                  <div className="mb-3 p-2 bg-green-50 rounded">
                    <p className="text-sm text-green-800">
                      Nilai: {assignment.score}/{assignment.maxScore} 
                      ({Math.round((assignment.score / assignment.maxScore) * 100)}%)
                    </p>
                  </div>
                )}
                
                <div className="flex space-x-2">
                  {assignment.status === 'pending' && (
                    <Link href={`/siswa/tugas/${assignment.id}`}>
                      <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                        Kerjakan Tugas
                      </Button>
                    </Link>
                  )}
                  <Link href={`/siswa/tugas/${assignment.id}`}>
                    <Button size="sm" variant="outline">
                      Lihat Detail
                    </Button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
```

### 3. Academic Progress Component
**File**: `src/components/dashboard/AcademicProgress.tsx`

```typescript
'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { BarChart3, TrendingUp, Calendar } from 'lucide-react';

interface SubjectProgress {
  subject: string;
  averageScore: number;
  completedAssignments: number;
  totalAssignments: number;
  attendanceRate: number;
  grade: string;
}

interface AcademicData {
  overallAverage: number;
  attendanceRate: number;
  completedAssignments: number;
  totalAssignments: number;
  subjects: SubjectProgress[];
}

export function AcademicProgress() {
  const [academicData, setAcademicData] = useState<AcademicData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAcademicProgress();
  }, []);

  const fetchAcademicProgress = async () => {
    try {
      const response = await fetch('/api/dashboard/academic-progress');
      const data = await response.json();
      
      if (data.success) {
        setAcademicData(data.data);
      }
    } catch (error) {
      console.error('Error fetching academic progress:', error);
    } finally {
      setLoading(false);
    }
  };

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A': return 'text-green-600 bg-green-100';
      case 'B': return 'text-blue-600 bg-blue-100';
      case 'C': return 'text-yellow-600 bg-yellow-100';
      case 'D': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <BarChart3 className="h-5 w-5" />
          <span>Progress Akademik</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-2 bg-gray-200 rounded w-full mb-4"></div>
            </div>
          </div>
        ) : !academicData ? (
          <div className="text-center py-8">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Belum ada data akademik</p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Overall Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-center mb-2">
                  <TrendingUp className="h-5 w-5 text-blue-600" />
                </div>
                <p className="text-2xl font-bold text-blue-600">
                  {academicData.overallAverage.toFixed(1)}
                </p>
                <p className="text-sm text-gray-600">Rata-rata Nilai</p>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="flex items-center justify-center mb-2">
                  <Calendar className="h-5 w-5 text-green-600" />
                </div>
                <p className="text-2xl font-bold text-green-600">
                  {academicData.attendanceRate.toFixed(1)}%
                </p>
                <p className="text-sm text-gray-600">Kehadiran</p>
              </div>
            </div>

            {/* Assignment Progress */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">
                  Progress Tugas
                </span>
                <span className="text-sm text-gray-500">
                  {academicData.completedAssignments}/{academicData.totalAssignments}
                </span>
              </div>
              <Progress 
                value={(academicData.completedAssignments / academicData.totalAssignments) * 100}
                className="h-2"
              />
            </div>

            {/* Subject Progress */}
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">
                Progress per Mata Pelajaran
              </h4>
              <div className="space-y-3">
                {academicData.subjects.map((subject, index) => (
                  <div key={index} className="border rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-900">
                        {subject.subject}
                      </span>
                      <span className={`px-2 py-1 rounded text-xs font-bold ${getGradeColor(subject.grade)}`}>
                        {subject.grade}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                      <div>
                        <p>Nilai Rata-rata</p>
                        <p className="font-semibold text-gray-900">
                          {subject.averageScore.toFixed(1)}
                        </p>
                      </div>
                      <div>
                        <p>Kehadiran</p>
                        <p className="font-semibold text-gray-900">
                          {subject.attendanceRate.toFixed(1)}%
                        </p>
                      </div>
                    </div>
                    
                    <div className="mt-2">
                      <div className="flex justify-between text-xs text-gray-500 mb-1">
                        <span>Tugas Selesai</span>
                        <span>{subject.completedAssignments}/{subject.totalAssignments}</span>
                      </div>
                      <Progress 
                        value={(subject.completedAssignments / subject.totalAssignments) * 100}
                        className="h-1"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
```

### 4. Gamification Stats Component
**File**: `src/components/dashboard/GamificationStats.tsx`

```typescript
'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Star, Trophy, Zap, Target } from 'lucide-react';

interface Badge {
  id: number;
  name: string;
  description: string;
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  earnedAt: string;
}

interface GamificationData {
  currentXP: number;
  currentLevel: number;
  xpToNextLevel: number;
  totalXP: number;
  rank: number;
  totalStudents: number;
  badges: Badge[];
  recentAchievements: string[];
}

export function GamificationStats() {
  const [gamificationData, setGamificationData] = useState<GamificationData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchGamificationData();
  }, []);

  const fetchGamificationData = async () => {
    try {
      const response = await fetch('/api/dashboard/gamification');
      const data = await response.json();
      
      if (data.success) {
        setGamificationData(data.data);
      }
    } catch (error) {
      console.error('Error fetching gamification data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-100 text-gray-800';
      case 'rare': return 'bg-blue-100 text-blue-800';
      case 'epic': return 'bg-purple-100 text-purple-800';
      case 'legendary': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getLevelProgress = () => {
    if (!gamificationData) return 0;
    const currentLevelXP = gamificationData.currentXP % 1000; // Assuming 1000 XP per level
    return (currentLevelXP / 1000) * 100;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* XP and Level */}
      <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Zap className="h-6 w-6" />
            <span className="text-sm opacity-90">Total XP</span>
          </div>
          <p className="text-2xl font-bold mb-1">
            {gamificationData?.totalXP.toLocaleString() || 0}
          </p>
          <p className="text-sm opacity-90">
            Level {gamificationData?.currentLevel || 1}
          </p>
          {gamificationData && (
            <div className="mt-3">
              <div className="flex justify-between text-xs mb-1">
                <span>Progress ke Level {gamificationData.currentLevel + 1}</span>
                <span>{gamificationData.xpToNextLevel} XP lagi</span>
              </div>
              <Progress 
                value={getLevelProgress()}
                className="h-2 bg-blue-400"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Rank */}
      <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Trophy className="h-6 w-6" />
            <span className="text-sm opacity-90">Peringkat</span>
          </div>
          <p className="text-2xl font-bold mb-1">
            #{gamificationData?.rank || '-'}
          </p>
          <p className="text-sm opacity-90">
            dari {gamificationData?.totalStudents || 0} siswa
          </p>
        </CardContent>
      </Card>

      {/* Badges */}
      <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Star className="h-6 w-6" />
            <span className="text-sm opacity-90">Badge</span>
          </div>
          <p className="text-2xl font-bold mb-1">
            {gamificationData?.badges.length || 0}
          </p>
          <p className="text-sm opacity-90">
            Badge terkumpul
          </p>
        </CardContent>
      </Card>

      {/* Recent Achievements */}
      <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Target className="h-6 w-6" />
            <span className="text-sm opacity-90">Pencapaian</span>
          </div>
          <p className="text-2xl font-bold mb-1">
            {gamificationData?.recentAchievements.length || 0}
          </p>
          <p className="text-sm opacity-90">
            Minggu ini
          </p>
        </CardContent>
      </Card>

      {/* Badges Detail */}
      {gamificationData && gamificationData.badges.length > 0 && (
        <Card className="md:col-span-2 lg:col-span-4">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Star className="h-5 w-5" />
              <span>Badge Terbaru</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {gamificationData.badges.slice(0, 6).map((badge) => (
                <div
                  key={badge.id}
                  className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="text-2xl">
                    {badge.icon || '🏆'}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <p className="font-semibold text-gray-900 truncate">
                        {badge.name}
                      </p>
                      <Badge className={getRarityColor(badge.rarity)}>
                        {badge.rarity}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 truncate">
                      {badge.description}
                    </p>
                    <p className="text-xs text-gray-400">
                      {new Date(badge.earnedAt).toLocaleDateString('id-ID')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
```

## 🔌 API Endpoints

### Dashboard Stats API
**File**: `src/app/api/dashboard/stats/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { students, classrooms, attendance } from '@/db/schema';
import { count, eq, and, gte } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    // Get total students
    const totalStudentsResult = await db
      .select({ count: count() })
      .from(students);
    
    // Get total classrooms
    const totalClassroomsResult = await db
      .select({ count: count() })
      .from(classrooms)
      .where(eq(classrooms.isActive, true));

    // Get today's attendance rate
    const today = new Date().toISOString().split('T')[0];
    const todayAttendanceResult = await db
      .select({ count: count() })
      .from(attendance)
      .where(and(
        eq(attendance.date, today),
        eq(attendance.status, 'H')
      ));

    // Calculate average attendance (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentAttendanceResult = await db
      .select({ count: count() })
      .from(attendance)
      .where(and(
        gte(attendance.date, thirtyDaysAgo.toISOString().split('T')[0]),
        eq(attendance.status, 'H')
      ));

    const stats = {
      totalStudents: totalStudentsResult[0]?.count || 0,
      totalClasses: totalClassroomsResult[0]?.count || 0,
      todayAttendance: Math.round((todayAttendanceResult[0]?.count || 0) / (totalStudentsResult[0]?.count || 1) * 100),
      averageAttendance: Math.round((recentAttendanceResult[0]?.count || 0) / ((totalStudentsResult[0]?.count || 1) * 30) * 100),
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal mengambil statistik dashboard' }
    }, { status: 500 });
  }
}
```

## 📱 Responsive Design

### Mobile Navigation
```typescript
// Mobile bottom navigation for student dashboard
<div className="fixed bottom-0 left-0 right-0 bg-white border-t lg:hidden">
  <div className="grid grid-cols-4 gap-1">
    <Link href="/siswa" className="flex flex-col items-center py-2">
      <Home className="h-5 w-5" />
      <span className="text-xs">Beranda</span>
    </Link>
    <Link href="/siswa/tugas" className="flex flex-col items-center py-2">
      <FileText className="h-5 w-5" />
      <span className="text-xs">Tugas</span>
    </Link>
    <Link href="/siswa/nilai" className="flex flex-col items-center py-2">
      <BarChart3 className="h-5 w-5" />
      <span className="text-xs">Nilai</span>
    </Link>
    <Link href="/siswa/profil" className="flex flex-col items-center py-2">
      <User className="h-5 w-5" />
      <span className="text-xs">Profil</span>
    </Link>
  </div>
</div>
```

### Responsive Grid Layout
```css
/* Dashboard responsive grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: 2fr 1fr;
  }
}
```

## 🧪 Testing Dashboard

### Component Testing
```typescript
// Example test for StatsCards component
import { render, screen, waitFor } from '@testing-library/react';
import { StatsCards } from '@/components/dashboard/StatsCards';

// Mock fetch
global.fetch = jest.fn();

describe('StatsCards', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  it('should display loading state initially', () => {
    render(<StatsCards />);
    expect(screen.getAllByTestId('stats-skeleton')).toHaveLength(4);
  });

  it('should display stats after loading', async () => {
    const mockStats = {
      totalStudents: 150,
      totalClasses: 8,
      todayAttendance: 95,
      averageAttendance: 92,
    };

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: mockStats }),
    });

    render(<StatsCards />);

    await waitFor(() => {
      expect(screen.getByText('150')).toBeInTheDocument();
      expect(screen.getByText('8')).toBeInTheDocument();
      expect(screen.getByText('95%')).toBeInTheDocument();
      expect(screen.getByText('92%')).toBeInTheDocument();
    });
  });
});
```

### Integration Testing
```typescript
// Test dashboard API endpoints
describe('/api/dashboard/stats', () => {
  it('should return teacher dashboard stats', async () => {
    // Mock authenticated teacher user
    const mockUser = { id: 1, role: 'teacher', name: 'Test Teacher' };
    
    const response = await fetch('/api/dashboard/stats', {
      headers: { Cookie: 'session=mock-session' }
    });
    
    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toHaveProperty('totalStudents');
    expect(data.data).toHaveProperty('totalClasses');
  });
});
```

## 🎨 UI/UX Considerations

### Loading States
- Skeleton screens untuk semua komponen data
- Progressive loading untuk dashboard sections
- Smooth transitions dan animations

### Error Handling
- Graceful error messages
- Retry mechanisms untuk failed requests
- Fallback UI untuk missing data

### Performance
- Lazy loading untuk heavy components
- Memoization untuk expensive calculations
- Efficient data fetching dengan caching

## 📊 Analytics Integration

### User Activity Tracking
```typescript
// Track dashboard interactions
export function trackDashboardAction(action: string, data?: any) {
  // Send to analytics service
  if (typeof window !== 'undefined') {
    window.gtag?.('event', action, {
      event_category: 'dashboard',
      event_label: data?.label,
      value: data?.value,
    });
  }
}

// Usage in components
const handleQuickAction = (actionType: string) => {
  trackDashboardAction('quick_action_click', { label: actionType });
  // Handle action...
};
```

## 🔒 Security Considerations

### Data Access Control
- Role-based data filtering
- User-specific data queries
- Secure API endpoints dengan authentication

### Privacy Protection
- No sensitive data dalam client-side state
- Proper data sanitization
- Audit logs untuk data access

---

## 🎯 Next Steps

Setelah Task 5 selesai, dashboard siap untuk:
1. **Classroom Management Integration** (Task 6)
2. **Student Management Integration** (Task 7)
3. **Attendance System Integration** (Task 8)

## 📚 Resources

- [Dashboard Design Patterns](https://www.nngroup.com/articles/dashboard-design/)
- [React Performance Best Practices](https://react.dev/learn/render-and-commit)
- [Responsive Design Guidelines](https://web.dev/responsive-web-design-basics/)
- [Accessibility in Dashboards](https://www.w3.org/WAI/tutorials/page-structure/)

---

**Status**: ✅ **Completed**  
**Duration**: ~6 hours  
**Complexity**: Medium-High  
**Dependencies**: Task 1-4 (Project Setup, Database, Auth, Onboarding)