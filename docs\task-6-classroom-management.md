# Task 6: Classroom Management System

## 📋 Overview

Task ini mengimplementasikan sistem manajemen kelas yang komprehensif, mencakup CRUD operations untuk kelas, manajemen mata pelajaran, dan assignment guru ke kelas. Sistem dirancang untuk mendukung struktur akademik yang fleksibel dengan multiple teachers per subject.

## ✅ Komponen yang Diimplementasi

### 6.1 Classroom CRUD Operations ✅
### 6.2 Subject Management within Classrooms ✅

## 🏗️ Arsitektur Classroom Management

### System Flow
```mermaid
graph TD
    A[Teacher Login] --> B[Classroom List]
    B --> C{Action}
    C -->|Create| D[Create Classroom Form]
    C -->|View| E[Classroom Detail]
    C -->|Edit| F[Edit Classroom]
    C -->|Delete| G[Delete Confirmation]
    
    E --> H[Manage Subjects]
    E --> I[Manage Students]
    E --> J[View Statistics]
    
    H --> K[Add Subject]
    H --> L[Assign Teacher]
    H --> M[Remove Subject]
```

## 🎯 Core Components

### 1. Classroom List Page
**File**: `src/app/(dashboard)/guru/kelas/page.tsx`

```typescript
import { getCurrentUser } from '@/lib/auth';
import { redirect } from 'next/navigation';
import { ClassroomList } from '@/components/classroom/ClassroomList';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import Link from 'next/link';

export default async function ClassroomsPage() {
  const user = await getCurrentUser();
  
  if (!user || user.role !== 'teacher') {
    redirect('/login');
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Manajemen Kelas
          </h1>
          <p className="text-gray-600">
            Kelola kelas, mata pelajaran, dan siswa
          </p>
        </div>
        
        <Link href="/guru/kelas/buat">
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Buat Kelas Baru
          </Button>
        </Link>
      </div>

      {/* Classroom List */}
      <ClassroomList />
    </div>
  );
}
```###
 2. Classroom List Component
**File**: `src/components/classroom/ClassroomList.tsx`

```typescript
'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Users, 
  BookOpen, 
  MoreVertical,
  Edit,
  Trash2,
  Eye
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Link from 'next/link';

interface Classroom {
  id: number;
  name: string;
  grade: string;
  academicYear: string;
  homeroomTeacher?: string;
  studentCount: number;
  subjectCount: number;
  isActive: boolean;
}

export function ClassroomList() {
  const [classrooms, setClassrooms] = useState<Classroom[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredClassrooms, setFilteredClassrooms] = useState<Classroom[]>([]);

  useEffect(() => {
    fetchClassrooms();
  }, []);

  useEffect(() => {
    const filtered = classrooms.filter(classroom =>
      classroom.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      classroom.grade.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredClassrooms(filtered);
  }, [classrooms, searchTerm]);

  const fetchClassrooms = async () => {
    try {
      const response = await fetch('/api/classrooms');
      const data = await response.json();
      
      if (data.success) {
        setClassrooms(data.data);
      }
    } catch (error) {
      console.error('Error fetching classrooms:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClassroom = async (classroomId: number) => {
    if (!confirm('Apakah Anda yakin ingin menghapus kelas ini?')) {
      return;
    }

    try {
      const response = await fetch(`/api/classrooms/${classroomId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setClassrooms(prev => prev.filter(c => c.id !== classroomId));
      }
    } catch (error) {
      console.error('Error deleting classroom:', error);
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Cari kelas atau tingkat..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Classroom Grid */}
      {filteredClassrooms.length === 0 ? (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {searchTerm ? 'Kelas tidak ditemukan' : 'Belum ada kelas'}
          </h3>
          <p className="text-gray-600 mb-4">
            {searchTerm 
              ? 'Coba ubah kata kunci pencarian'
              : 'Mulai dengan membuat kelas pertama Anda'
            }
          </p>
          {!searchTerm && (
            <Link href="/guru/kelas/buat">
              <Button>Buat Kelas Baru</Button>
            </Link>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredClassrooms.map((classroom) => (
            <Card key={classroom.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-1">
                      {classroom.name}
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">
                        {classroom.grade}
                      </Badge>
                      <Badge 
                        variant={classroom.isActive ? "default" : "secondary"}
                        className={classroom.isActive ? "bg-green-100 text-green-800" : ""}
                      >
                        {classroom.isActive ? 'Aktif' : 'Tidak Aktif'}
                      </Badge>
                    </div>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`/guru/kelas/${classroom.id}`}>
                          <Eye className="h-4 w-4 mr-2" />
                          Lihat Detail
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/guru/kelas/${classroom.id}/edit`}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Kelas
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => handleDeleteClassroom(classroom.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Hapus Kelas
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  <div className="text-sm text-gray-600">
                    <p>Tahun Ajaran: {classroom.academicYear}</p>
                    {classroom.homeroomTeacher && (
                      <p>Wali Kelas: {classroom.homeroomTeacher}</p>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-1 text-blue-600">
                      <Users className="h-4 w-4" />
                      <span>{classroom.studentCount} siswa</span>
                    </div>
                    <div className="flex items-center space-x-1 text-green-600">
                      <BookOpen className="h-4 w-4" />
                      <span>{classroom.subjectCount} mapel</span>
                    </div>
                  </div>
                  
                  <div className="pt-2 border-t">
                    <Link href={`/guru/kelas/${classroom.id}`}>
                      <Button variant="outline" size="sm" className="w-full">
                        Kelola Kelas
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
```

### 3. Create Classroom Form
**File**: `src/components/classroom/CreateClassroomForm.tsx`

```typescript
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

interface CreateClassroomFormData {
  name: string;
  grade: string;
  academicYear: string;
}

export function CreateClassroomForm() {
  const [formData, setFormData] = useState<CreateClassroomFormData>({
    name: '',
    grade: '',
    academicYear: '2024/2025',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const gradeOptions = [
    { value: '1', label: 'Kelas 1' },
    { value: '2', label: 'Kelas 2' },
    { value: '3', label: 'Kelas 3' },
    { value: '4', label: 'Kelas 4' },
    { value: '5', label: 'Kelas 5' },
    { value: '6', label: 'Kelas 6' },
    { value: '7', label: 'Kelas 7' },
    { value: '8', label: 'Kelas 8' },
    { value: '9', label: 'Kelas 9' },
    { value: '10', label: 'Kelas 10' },
    { value: '11', label: 'Kelas 11' },
    { value: '12', label: 'Kelas 12' },
  ];

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nama kelas wajib diisi';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Nama kelas minimal 2 karakter';
    }

    if (!formData.grade) {
      newErrors.grade = 'Tingkat kelas wajib dipilih';
    }

    if (!formData.academicYear.trim()) {
      newErrors.academicYear = 'Tahun ajaran wajib diisi';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/classrooms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error?.message || 'Gagal membuat kelas');
      }

      if (result.success) {
        router.push('/guru/kelas');
        router.refresh();
      }
    } catch (error) {
      setErrors({
        submit: error instanceof Error ? error.message : 'Terjadi kesalahan'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/guru/kelas">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Buat Kelas Baru
          </h1>
          <p className="text-gray-600">
            Tambahkan kelas baru ke sistem
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>Informasi Kelas</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {errors.submit && (
              <Alert variant="destructive">
                <AlertDescription>{errors.submit}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="name">Nama Kelas *</Label>
              <Input
                id="name"
                placeholder="Contoh: 5A, 7 Sains, XII IPA 1"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className={errors.name ? 'border-red-500' : ''}
                disabled={isSubmitting}
              />
              {errors.name && (
                <p className="text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="grade">Tingkat Kelas *</Label>
              <Select
                value={formData.grade}
                onValueChange={(value) => setFormData(prev => ({ ...prev, grade: value }))}
                disabled={isSubmitting}
              >
                <SelectTrigger className={errors.grade ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Pilih tingkat kelas" />
                </SelectTrigger>
                <SelectContent>
                  {gradeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.grade && (
                <p className="text-sm text-red-600">{errors.grade}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="academicYear">Tahun Ajaran *</Label>
              <Input
                id="academicYear"
                placeholder="Contoh: 2024/2025"
                value={formData.academicYear}
                onChange={(e) => setFormData(prev => ({ ...prev, academicYear: e.target.value }))}
                className={errors.academicYear ? 'border-red-500' : ''}
                disabled={isSubmitting}
              />
              {errors.academicYear && (
                <p className="text-sm text-red-600">{errors.academicYear}</p>
              )}
            </div>

            <div className="flex space-x-4 pt-4">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Membuat Kelas...
                  </>
                ) : (
                  'Buat Kelas'
                )}
              </Button>
              <Link href="/guru/kelas">
                <Button type="button" variant="outline" disabled={isSubmitting}>
                  Batal
                </Button>
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
```### 4. C
lassroom Detail Component
**File**: `src/components/classroom/ClassroomDetail.tsx`

```typescript
'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  BookOpen, 
  Plus, 
  Edit,
  Trash2,
  UserPlus,
  Calendar,
  BarChart3
} from 'lucide-react';
import Link from 'next/link';

interface ClassroomDetailData {
  id: number;
  name: string;
  grade: string;
  academicYear: string;
  homeroomTeacher?: {
    id: number;
    name: string;
  };
  isActive: boolean;
  students: Array<{
    id: number;
    name: string;
    studentId: string;
    enrolledAt: string;
  }>;
  subjects: Array<{
    id: number;
    name: string;
    code: string;
    teacher: {
      id: number;
      name: string;
    };
    schedule?: string;
  }>;
  stats: {
    totalStudents: number;
    totalSubjects: number;
    averageAttendance: number;
    activeAssignments: number;
  };
}

interface ClassroomDetailProps {
  classroomId: number;
}

export function ClassroomDetail({ classroomId }: ClassroomDetailProps) {
  const [classroom, setClassroom] = useState<ClassroomDetailData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchClassroomDetail();
  }, [classroomId]);

  const fetchClassroomDetail = async () => {
    try {
      const response = await fetch(`/api/classrooms/${classroomId}`);
      const data = await response.json();
      
      if (data.success) {
        setClassroom(data.data);
      }
    } catch (error) {
      console.error('Error fetching classroom detail:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveSubject = async (subjectId: number) => {
    if (!confirm('Apakah Anda yakin ingin menghapus mata pelajaran ini dari kelas?')) {
      return;
    }

    try {
      const response = await fetch(`/api/classrooms/${classroomId}/subjects/${subjectId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchClassroomDetail(); // Refresh data
      }
    } catch (error) {
      console.error('Error removing subject:', error);
    }
  };

  const handleRemoveStudent = async (studentId: number) => {
    if (!confirm('Apakah Anda yakin ingin mengeluarkan siswa ini dari kelas?')) {
      return;
    }

    try {
      const response = await fetch(`/api/classrooms/${classroomId}/students/${studentId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchClassroomDetail(); // Refresh data
      }
    } catch (error) {
      console.error('Error removing student:', error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="h-20 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!classroom) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Kelas tidak ditemukan</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <h1 className="text-2xl font-bold text-gray-900">
              {classroom.name}
            </h1>
            <Badge variant={classroom.isActive ? "default" : "secondary"}>
              {classroom.isActive ? 'Aktif' : 'Tidak Aktif'}
            </Badge>
          </div>
          <div className="text-gray-600 space-y-1">
            <p>Tingkat: {classroom.grade}</p>
            <p>Tahun Ajaran: {classroom.academicYear}</p>
            {classroom.homeroomTeacher && (
              <p>Wali Kelas: {classroom.homeroomTeacher.name}</p>
            )}
          </div>
        </div>
        
        <div className="flex space-x-2">
          <Link href={`/guru/kelas/${classroom.id}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit Kelas
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{classroom.stats.totalStudents}</p>
                <p className="text-sm text-gray-600">Siswa</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-2xl font-bold">{classroom.stats.totalSubjects}</p>
                <p className="text-sm text-gray-600">Mata Pelajaran</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-2xl font-bold">{classroom.stats.averageAttendance}%</p>
                <p className="text-sm text-gray-600">Kehadiran</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-2xl font-bold">{classroom.stats.activeAssignments}</p>
                <p className="text-sm text-gray-600">Tugas Aktif</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="students">Siswa ({classroom.students.length})</TabsTrigger>
          <TabsTrigger value="subjects">Mata Pelajaran ({classroom.subjects.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Students */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Siswa Terbaru</span>
                  <Link href={`/guru/kelas/${classroom.id}/siswa/tambah`}>
                    <Button size="sm">
                      <UserPlus className="h-4 w-4 mr-2" />
                      Tambah Siswa
                    </Button>
                  </Link>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {classroom.students.slice(0, 5).map((student) => (
                  <div key={student.id} className="flex items-center justify-between py-2 border-b last:border-b-0">
                    <div>
                      <p className="font-medium">{student.name}</p>
                      <p className="text-sm text-gray-600">{student.studentId}</p>
                    </div>
                    <p className="text-xs text-gray-500">
                      {new Date(student.enrolledAt).toLocaleDateString('id-ID')}
                    </p>
                  </div>
                ))}
                {classroom.students.length === 0 && (
                  <p className="text-gray-500 text-center py-4">Belum ada siswa</p>
                )}
              </CardContent>
            </Card>

            {/* Subjects */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Mata Pelajaran</span>
                  <Link href={`/guru/kelas/${classroom.id}/mapel/tambah`}>
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Tambah Mapel
                    </Button>
                  </Link>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {classroom.subjects.map((subject) => (
                  <div key={subject.id} className="flex items-center justify-between py-2 border-b last:border-b-0">
                    <div>
                      <p className="font-medium">{subject.name}</p>
                      <p className="text-sm text-gray-600">
                        {subject.teacher.name} • {subject.code}
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveSubject(subject.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                {classroom.subjects.length === 0 && (
                  <p className="text-gray-500 text-center py-4">Belum ada mata pelajaran</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="students" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Daftar Siswa</h3>
            <Link href={`/guru/kelas/${classroom.id}/siswa/tambah`}>
              <Button>
                <UserPlus className="h-4 w-4 mr-2" />
                Tambah Siswa
              </Button>
            </Link>
          </div>
          
          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Nama</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">NIS</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Tanggal Masuk</th>
                      <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">Aksi</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {classroom.students.map((student) => (
                      <tr key={student.id} className="hover:bg-gray-50">
                        <td className="px-4 py-3">
                          <div>
                            <p className="font-medium text-gray-900">{student.name}</p>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-600">
                          {student.studentId}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-600">
                          {new Date(student.enrolledAt).toLocaleDateString('id-ID')}
                        </td>
                        <td className="px-4 py-3 text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveStudent(student.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {classroom.students.length === 0 && (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Belum ada siswa di kelas ini</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subjects" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Mata Pelajaran</h3>
            <Link href={`/guru/kelas/${classroom.id}/mapel/tambah`}>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Mata Pelajaran
              </Button>
            </Link>
          </div>
          
          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Mata Pelajaran</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Kode</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Guru</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Jadwal</th>
                      <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">Aksi</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {classroom.subjects.map((subject) => (
                      <tr key={subject.id} className="hover:bg-gray-50">
                        <td className="px-4 py-3">
                          <p className="font-medium text-gray-900">{subject.name}</p>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-600">
                          {subject.code}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-600">
                          {subject.teacher.name}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-600">
                          {subject.schedule || '-'}
                        </td>
                        <td className="px-4 py-3 text-right">
                          <div className="flex justify-end space-x-2">
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRemoveSubject(subject.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {classroom.subjects.length === 0 && (
                  <div className="text-center py-8">
                    <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Belum ada mata pelajaran di kelas ini</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
```## 🔌 AP
I Implementation

### 1. Classrooms API Routes
**File**: `src/app/api/classrooms/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { classrooms, users, teachers, classroomStudents, classroomSubjects, subjects } from '@/db/schema';
import { eq, and, count, sql } from 'drizzle-orm';
import { createClassroomSchema } from '@/lib/validations';

// GET - List all classrooms
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    // Get classrooms with stats
    const classroomsWithStats = await db
      .select({
        id: classrooms.id,
        name: classrooms.name,
        grade: classrooms.grade,
        academicYear: classrooms.academicYear,
        isActive: classrooms.isActive,
        homeroomTeacher: users.name,
        studentCount: sql<number>`COALESCE(${count(classroomStudents.studentId)}, 0)`,
        subjectCount: sql<number>`COALESCE(subject_counts.count, 0)`,
      })
      .from(classrooms)
      .leftJoin(teachers, eq(classrooms.homeroomTeacherId, teachers.id))
      .leftJoin(users, eq(teachers.userId, users.id))
      .leftJoin(classroomStudents, and(
        eq(classrooms.id, classroomStudents.classroomId),
        eq(classroomStudents.isActive, true)
      ))
      .leftJoin(
        sql`(
          SELECT classroom_id, COUNT(*) as count 
          FROM classroom_subjects 
          WHERE is_active = true 
          GROUP BY classroom_id
        ) as subject_counts`,
        sql`subject_counts.classroom_id = ${classrooms.id}`
      )
      .groupBy(classrooms.id, users.name)
      .orderBy(classrooms.name);

    return NextResponse.json({
      success: true,
      data: classroomsWithStats,
    });
  } catch (error) {
    console.error('Get classrooms error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal mengambil data kelas' }
    }, { status: 500 });
  }
}

// POST - Create new classroom
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    const body = await request.json();
    
    // Validate request body
    const validationResult = createClassroomSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Data tidak valid',
          details: validationResult.error.errors,
        },
      }, { status: 400 });
    }

    const { name, grade, academicYear } = validationResult.data;

    // Check for duplicate classroom name in same academic year
    const existingClassroom = await db
      .select()
      .from(classrooms)
      .where(and(
        eq(classrooms.name, name),
        eq(classrooms.academicYear, academicYear)
      ))
      .limit(1);

    if (existingClassroom.length > 0) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'DUPLICATE_CLASSROOM',
          message: 'Kelas dengan nama yang sama sudah ada di tahun ajaran ini',
        },
      }, { status: 400 });
    }

    // Create classroom
    const newClassroom = await db
      .insert(classrooms)
      .values({
        name,
        grade,
        academicYear,
        isActive: true,
      })
      .returning();

    return NextResponse.json({
      success: true,
      data: newClassroom[0],
      message: 'Kelas berhasil dibuat',
    });
  } catch (error) {
    console.error('Create classroom error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal membuat kelas' }
    }, { status: 500 });
  }
}
```

### 2. Individual Classroom API
**File**: `src/app/api/classrooms/[id]/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { 
  classrooms, 
  users, 
  teachers, 
  students,
  classroomStudents, 
  classroomSubjects, 
  subjects,
  attendance
} from '@/db/schema';
import { eq, and, count, avg, sql } from 'drizzle-orm';

// GET - Get classroom detail
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    const classroomId = parseInt(params.id);

    // Get classroom basic info
    const classroomResult = await db
      .select({
        id: classrooms.id,
        name: classrooms.name,
        grade: classrooms.grade,
        academicYear: classrooms.academicYear,
        isActive: classrooms.isActive,
        homeroomTeacher: {
          id: teachers.id,
          name: users.name,
        },
      })
      .from(classrooms)
      .leftJoin(teachers, eq(classrooms.homeroomTeacherId, teachers.id))
      .leftJoin(users, eq(teachers.userId, users.id))
      .where(eq(classrooms.id, classroomId))
      .limit(1);

    if (classroomResult.length === 0) {
      return NextResponse.json({
        success: false,
        error: { code: 'NOT_FOUND', message: 'Kelas tidak ditemukan' }
      }, { status: 404 });
    }

    const classroom = classroomResult[0];

    // Get students
    const studentsResult = await db
      .select({
        id: students.id,
        name: users.name,
        studentId: students.studentId,
        enrolledAt: classroomStudents.enrolledAt,
      })
      .from(classroomStudents)
      .innerJoin(students, eq(classroomStudents.studentId, students.id))
      .innerJoin(users, eq(students.userId, users.id))
      .where(and(
        eq(classroomStudents.classroomId, classroomId),
        eq(classroomStudents.isActive, true)
      ))
      .orderBy(users.name);

    // Get subjects with teachers
    const subjectsResult = await db
      .select({
        id: classroomSubjects.id,
        name: subjects.name,
        code: subjects.code,
        teacher: {
          id: teachers.id,
          name: users.name,
        },
        schedule: classroomSubjects.schedule,
      })
      .from(classroomSubjects)
      .innerJoin(subjects, eq(classroomSubjects.subjectId, subjects.id))
      .innerJoin(teachers, eq(classroomSubjects.teacherId, teachers.id))
      .innerJoin(users, eq(teachers.userId, users.id))
      .where(and(
        eq(classroomSubjects.classroomId, classroomId),
        eq(classroomSubjects.isActive, true)
      ))
      .orderBy(subjects.name);

    // Get stats
    const totalStudents = studentsResult.length;
    const totalSubjects = subjectsResult.length;

    // Calculate average attendance (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const attendanceStats = await db
      .select({
        totalRecords: count(),
        presentRecords: sql<number>`SUM(CASE WHEN ${attendance.status} = 'H' THEN 1 ELSE 0 END)`,
      })
      .from(attendance)
      .innerJoin(classroomStudents, eq(attendance.studentId, classroomStudents.studentId))
      .where(and(
        eq(classroomStudents.classroomId, classroomId),
        sql`${attendance.date} >= ${thirtyDaysAgo.toISOString().split('T')[0]}`
      ));

    const averageAttendance = attendanceStats[0]?.totalRecords > 0 
      ? Math.round((attendanceStats[0].presentRecords / attendanceStats[0].totalRecords) * 100)
      : 0;

    const stats = {
      totalStudents,
      totalSubjects,
      averageAttendance,
      activeAssignments: 0, // TODO: Calculate from assignments table
    };

    return NextResponse.json({
      success: true,
      data: {
        ...classroom,
        students: studentsResult,
        subjects: subjectsResult,
        stats,
      },
    });
  } catch (error) {
    console.error('Get classroom detail error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal mengambil detail kelas' }
    }, { status: 500 });
  }
}

// PUT - Update classroom
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    const classroomId = parseInt(params.id);
    const body = await request.json();
    
    // Validate request body
    const validationResult = createClassroomSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Data tidak valid',
          details: validationResult.error.errors,
        },
      }, { status: 400 });
    }

    const { name, grade, academicYear } = validationResult.data;

    // Check if classroom exists
    const existingClassroom = await db
      .select()
      .from(classrooms)
      .where(eq(classrooms.id, classroomId))
      .limit(1);

    if (existingClassroom.length === 0) {
      return NextResponse.json({
        success: false,
        error: { code: 'NOT_FOUND', message: 'Kelas tidak ditemukan' }
      }, { status: 404 });
    }

    // Update classroom
    const updatedClassroom = await db
      .update(classrooms)
      .set({
        name,
        grade,
        academicYear,
        updatedAt: new Date().toISOString(),
      })
      .where(eq(classrooms.id, classroomId))
      .returning();

    return NextResponse.json({
      success: true,
      data: updatedClassroom[0],
      message: 'Kelas berhasil diperbarui',
    });
  } catch (error) {
    console.error('Update classroom error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal memperbarui kelas' }
    }, { status: 500 });
  }
}

// DELETE - Delete classroom
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    const classroomId = parseInt(params.id);

    // Check if classroom exists
    const existingClassroom = await db
      .select()
      .from(classrooms)
      .where(eq(classrooms.id, classroomId))
      .limit(1);

    if (existingClassroom.length === 0) {
      return NextResponse.json({
        success: false,
        error: { code: 'NOT_FOUND', message: 'Kelas tidak ditemukan' }
      }, { status: 404 });
    }

    // Check if classroom has active students
    const activeStudents = await db
      .select({ count: count() })
      .from(classroomStudents)
      .where(and(
        eq(classroomStudents.classroomId, classroomId),
        eq(classroomStudents.isActive, true)
      ));

    if (activeStudents[0].count > 0) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'CLASSROOM_HAS_STUDENTS',
          message: 'Tidak dapat menghapus kelas yang masih memiliki siswa aktif',
        },
      }, { status: 400 });
    }

    // Soft delete classroom (set isActive to false)
    await db
      .update(classrooms)
      .set({
        isActive: false,
        updatedAt: new Date().toISOString(),
      })
      .where(eq(classrooms.id, classroomId));

    return NextResponse.json({
      success: true,
      message: 'Kelas berhasil dihapus',
    });
  } catch (error) {
    console.error('Delete classroom error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal menghapus kelas' }
    }, { status: 500 });
  }
}
```

## 📝 Validation Schemas

### Classroom Validation
**File**: `src/lib/validations.ts` (addition)

```typescript
export const createClassroomSchema = z.object({
  name: z
    .string()
    .min(1, 'Nama kelas wajib diisi')
    .min(2, 'Nama kelas minimal 2 karakter')
    .max(50, 'Nama kelas maksimal 50 karakter'),
  grade: z
    .string()
    .min(1, 'Tingkat kelas wajib dipilih')
    .regex(/^(1|2|3|4|5|6|7|8|9|10|11|12)$/, 'Tingkat kelas tidak valid'),
  academicYear: z
    .string()
    .min(1, 'Tahun ajaran wajib diisi')
    .regex(/^\d{4}\/\d{4}$/, 'Format tahun ajaran tidak valid (contoh: 2024/2025)'),
});

export const addSubjectToClassroomSchema = z.object({
  subjectId: z.number().min(1, 'Mata pelajaran wajib dipilih'),
  teacherId: z.number().min(1, 'Guru wajib dipilih'),
  schedule: z.string().optional(),
});
```

## 🎯 Subject Management

### Add Subject to Classroom API
**File**: `src/app/api/classrooms/[id]/subjects/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { classroomSubjects, subjects, teachers, users } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { addSubjectToClassroomSchema } from '@/lib/validations';

// POST - Add subject to classroom
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    const classroomId = parseInt(params.id);
    const body = await request.json();
    
    // Validate request body
    const validationResult = addSubjectToClassroomSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Data tidak valid',
          details: validationResult.error.errors,
        },
      }, { status: 400 });
    }

    const { subjectId, teacherId, schedule } = validationResult.data;

    // Check if subject already assigned to classroom
    const existingAssignment = await db
      .select()
      .from(classroomSubjects)
      .where(and(
        eq(classroomSubjects.classroomId, classroomId),
        eq(classroomSubjects.subjectId, subjectId),
        eq(classroomSubjects.isActive, true)
      ))
      .limit(1);

    if (existingAssignment.length > 0) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'SUBJECT_ALREADY_ASSIGNED',
          message: 'Mata pelajaran sudah ditugaskan ke kelas ini',
        },
      }, { status: 400 });
    }

    // Add subject to classroom
    const newAssignment = await db
      .insert(classroomSubjects)
      .values({
        classroomId,
        subjectId,
        teacherId,
        schedule,
        isActive: true,
      })
      .returning();

    // Get complete assignment data with subject and teacher info
    const assignmentWithDetails = await db
      .select({
        id: classroomSubjects.id,
        subject: {
          id: subjects.id,
          name: subjects.name,
          code: subjects.code,
        },
        teacher: {
          id: teachers.id,
          name: users.name,
        },
        schedule: classroomSubjects.schedule,
      })
      .from(classroomSubjects)
      .innerJoin(subjects, eq(classroomSubjects.subjectId, subjects.id))
      .innerJoin(teachers, eq(classroomSubjects.teacherId, teachers.id))
      .innerJoin(users, eq(teachers.userId, users.id))
      .where(eq(classroomSubjects.id, newAssignment[0].id))
      .limit(1);

    return NextResponse.json({
      success: true,
      data: assignmentWithDetails[0],
      message: 'Mata pelajaran berhasil ditambahkan ke kelas',
    });
  } catch (error) {
    console.error('Add subject to classroom error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal menambahkan mata pelajaran' }
    }, { status: 500 });
  }
}
```

## 🧪 Testing

### Component Testing
```typescript
// Example test for ClassroomList component
import { render, screen, waitFor } from '@testing-library/react';
import { ClassroomList } from '@/components/classroom/ClassroomList';

// Mock fetch
global.fetch = jest.fn();

describe('ClassroomList', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  it('should display loading state initially', () => {
    render(<ClassroomList />);
    expect(screen.getAllByTestId('classroom-skeleton')).toHaveLength(6);
  });

  it('should display classrooms after loading', async () => {
    const mockClassrooms = [
      {
        id: 1,
        name: '5A',
        grade: '5',
        academicYear: '2024/2025',
        studentCount: 25,
        subjectCount: 8,
        isActive: true,
      },
    ];

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: mockClassrooms }),
    });

    render(<ClassroomList />);

    await waitFor(() => {
      expect(screen.getByText('5A')).toBeInTheDocument();
      expect(screen.getByText('25 siswa')).toBeInTheDocument();
      expect(screen.getByText('8 mapel')).toBeInTheDocument();
    });
  });

  it('should filter classrooms by search term', async () => {
    const mockClassrooms = [
      { id: 1, name: '5A', grade: '5' },
      { id: 2, name: '5B', grade: '5' },
      { id: 3, name: '6A', grade: '6' },
    ];

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: mockClassrooms }),
    });

    render(<ClassroomList />);

    await waitFor(() => {
      expect(screen.getByText('5A')).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText('Cari kelas atau tingkat...');
    fireEvent.change(searchInput, { target: { value: '6' } });

    expect(screen.getByText('6A')).toBeInTheDocument();
    expect(screen.queryByText('5A')).not.toBeInTheDocument();
  });
});
```

### API Testing
```typescript
// Test classroom API endpoints
describe('/api/classrooms', () => {
  it('should create new classroom', async () => {
    const classroomData = {
      name: 'Test Class',
      grade: '5',
      academicYear: '2024/2025',
    };

    const response = await fetch('/api/classrooms', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(classroomData),
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.name).toBe('Test Class');
  });

  it('should prevent duplicate classroom names', async () => {
    // Create first classroom
    await createTestClassroom('Duplicate Class');

    // Try to create duplicate
    const response = await fetch('/api/classrooms', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'Duplicate Class',
        grade: '5',
        academicYear: '2024/2025',
      }),
    });

    expect(response.status).toBe(400);
    const data = await response.json();
    expect(data.error.code).toBe('DUPLICATE_CLASSROOM');
  });
});
```

## 📱 Mobile Responsiveness

### Responsive Grid Layout
```css
/* Classroom grid responsive design */
.classroom-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .classroom-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .classroom-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Mobile table responsive */
@media (max-width: 768px) {
  .classroom-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .classroom-table th,
  .classroom-table td {
    min-width: 120px;
  }
}
```

### Mobile Navigation
```typescript
// Mobile-friendly classroom actions
<div className="flex flex-col sm:flex-row gap-2">
  <Button size="sm" className="flex-1">
    <Eye className="h-4 w-4 mr-2" />
    Lihat
  </Button>
  <Button size="sm" variant="outline" className="flex-1">
    <Edit className="h-4 w-4 mr-2" />
    Edit
  </Button>
</div>
```

## 🔒 Security Considerations

### Access Control
- Role-based access (hanya teacher yang bisa manage classrooms)
- Ownership validation untuk classroom operations
- Input sanitization dan validation

### Data Integrity
- Foreign key constraints
- Soft delete untuk data preservation
- Audit trail untuk perubahan data

---

## 🎯 Next Steps

Setelah Task 6 selesai, classroom management siap untuk:
1. **Student Management Integration** (Task 7)
2. **Attendance System Integration** (Task 8)
3. **Assignment Management** (Future tasks)

## 📚 Resources

- [CRUD Operations Best Practices](https://restfulapi.net/http-methods/)
- [Database Relationships](https://www.lucidchart.com/pages/database-diagram/database-design)
- [React Table Patterns](https://react-table.tanstack.com/)
- [Form Validation Patterns](https://react-hook-form.com/get-started)

---

**Status**: ✅ **Completed**  
**Duration**: ~8 hours  
**Complexity**: Medium-High  
**Dependencies**: Task 1-5 (Project Setup, Database, Auth, Onboarding, Dashboard)