# Task 7: Student Management System

## 📋 Overview

Task ini mengimplementasikan sistem manajemen siswa yang komprehensif, mencakup CRUD operations untuk profil siswa, sistem enrollment ke kelas, dan bulk import data siswa melalui CSV. Sistem dirancang dengan gamifikasi terintegrasi untuk tracking XP dan level siswa.

## ✅ Komponen yang Diimplementasi

### 7.1 Student Profile Management ✅
### 7.2 Student Enrollment System ✅

## 🏗️ Arsitektur Student Management

### System Flow
```mermaid
graph TD
    A[Teacher Access] --> B[Student Management]
    B --> C{Action Type}
    
    C -->|Individual| D[Create Student Form]
    C -->|Bulk| E[CSV Import Modal]
    C -->|View| F[Student List]
    C -->|Edit| G[Edit Student Profile]
    
    D --> H[Student Profile Created]
    E --> I[Bulk Students Created]
    F --> J[Student Detail View]
    G --> K[Profile Updated]
    
    H --> L[Enroll to Classroom]
    I --> L
    J --> M[Manage Enrollment]
    K --> M
    
    L --> N[Active Student]
    M --> O[Transfer/Deactivate]
```

## 🎯 Core Components

### 1. Student List Component
**File**: `src/components/student/StudentList.tsx`

```typescript
'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  UserPlus, 
  Upload,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Users
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Link from 'next/link';
import { BulkImportModal } from './BulkImportModal';

interface Student {
  id: number;
  name: string;
  studentId: string;
  email: string;
  dateOfBirth: string;
  gender: 'male' | 'female';
  currentClassroom?: {
    id: number;
    name: string;
  };
  totalXP: number;
  level: number;
  isActive: boolean;
  enrollmentDate: string;
}

export function StudentList() {
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [showBulkImport, setShowBulkImport] = useState(false);

  useEffect(() => {
    fetchStudents();
  }, []);

  useEffect(() => {
    const filtered = students.filter(student =>
      student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredStudents(filtered);
  }, [students, searchTerm]);

  const fetchStudents = async () => {
    try {
      const response = await fetch('/api/students');
      const data = await response.json();
      
      if (data.success) {
        setStudents(data.data);
      }
    } catch (error) {
      console.error('Error fetching students:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteStudent = async (studentId: number) => {
    if (!confirm('Apakah Anda yakin ingin menghapus siswa ini?')) {
      return;
    }

    try {
      const response = await fetch(`/api/students/${studentId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setStudents(prev => prev.filter(s => s.id !== studentId));
      }
    } catch (error) {
      console.error('Error deleting student:', error);
    }
  };

  const getGenderText = (gender: string) => {
    return gender === 'male' ? 'Laki-laki' : 'Perempuan';
  };

  const getLevelColor = (level: number) => {
    if (level >= 10) return 'bg-purple-100 text-purple-800';
    if (level >= 5) return 'bg-blue-100 text-blue-800';
    return 'bg-green-100 text-green-800';
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-10 bg-gray-200 rounded mb-4"></div>
        </div>
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="animate-pulse h-16 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Cari siswa..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Button
            onClick={() => setShowBulkImport(true)}
            variant="outline"
          >
            <Upload className="h-4 w-4 mr-2" />
            Import CSV
          </Button>
          <Link href="/guru/siswa/tambah">
            <Button>
              <UserPlus className="h-4 w-4 mr-2" />
              Tambah Siswa
            </Button>
          </Link>
        </div>
      </div>

      {/* Student List */}
      {filteredStudents.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {searchTerm ? 'Siswa tidak ditemukan' : 'Belum ada siswa'}
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm 
                ? 'Coba ubah kata kunci pencarian'
                : 'Mulai dengan menambahkan siswa pertama'
              }
            </p>
            {!searchTerm && (
              <div className="flex justify-center gap-2">
                <Link href="/guru/siswa/tambah">
                  <Button>Tambah Siswa</Button>
                </Link>
                <Button
                  variant="outline"
                  onClick={() => setShowBulkImport(true)}
                >
                  Import CSV
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>
              Daftar Siswa ({filteredStudents.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Siswa
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                      NIS
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Kelas
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Level/XP
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Status
                    </th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">
                      Aksi
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {filteredStudents.map((student) => (
                    <tr key={student.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3">
                        <div>
                          <p className="font-medium text-gray-900">
                            {student.name}
                          </p>
                          <p className="text-sm text-gray-600">
                            {student.email}
                          </p>
                          <p className="text-xs text-gray-500">
                            {getGenderText(student.gender)} • 
                            {new Date(student.dateOfBirth).toLocaleDateString('id-ID')}
                          </p>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        {student.studentId}
                      </td>
                      <td className="px-4 py-3">
                        {student.currentClassroom ? (
                          <Badge variant="secondary">
                            {student.currentClassroom.name}
                          </Badge>
                        ) : (
                          <span className="text-sm text-gray-500">
                            Belum ada kelas
                          </span>
                        )}
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex items-center space-x-2">
                          <Badge className={getLevelColor(student.level)}>
                            Level {student.level}
                          </Badge>
                          <span className="text-sm text-gray-600">
                            {student.totalXP.toLocaleString()} XP
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <Badge 
                          variant={student.isActive ? "default" : "secondary"}
                          className={student.isActive ? "bg-green-100 text-green-800" : ""}
                        >
                          {student.isActive ? 'Aktif' : 'Tidak Aktif'}
                        </Badge>
                      </td>
                      <td className="px-4 py-3 text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/guru/siswa/${student.id}`}>
                                <Eye className="h-4 w-4 mr-2" />
                                Lihat Detail
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/guru/siswa/${student.id}/edit`}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Profil
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleDeleteStudent(student.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Hapus Siswa
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bulk Import Modal */}
      <BulkImportModal
        isOpen={showBulkImport}
        onClose={() => setShowBulkImport(false)}
        onSuccess={() => {
          setShowBulkImport(false);
          fetchStudents();
        }}
      />
    </div>
  );
}
```### 2. C
reate Student Form
**File**: `src/components/student/CreateStudentForm.tsx`

```typescript
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, ArrowLeft, User, Mail, Calendar, Phone } from 'lucide-react';
import Link from 'next/link';

interface CreateStudentFormData {
  name: string;
  email: string;
  studentId: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | '';
  address: string;
  phone: string;
  parentName: string;
  parentPhone: string;
  parentEmail: string;
  password: string;
  confirmPassword: string;
}

export function CreateStudentForm() {
  const [formData, setFormData] = useState<CreateStudentFormData>({
    name: '',
    email: '',
    studentId: '',
    dateOfBirth: '',
    gender: '',
    address: '',
    phone: '',
    parentName: '',
    parentPhone: '',
    parentEmail: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Basic validation
    if (!formData.name.trim()) {
      newErrors.name = 'Nama siswa wajib diisi';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Nama siswa minimal 2 karakter';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email wajib diisi';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format email tidak valid';
    }

    if (!formData.studentId.trim()) {
      newErrors.studentId = 'NIS wajib diisi';
    }

    if (!formData.dateOfBirth) {
      newErrors.dateOfBirth = 'Tanggal lahir wajib diisi';
    } else {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age < 5 || age > 25) {
        newErrors.dateOfBirth = 'Usia siswa harus antara 5-25 tahun';
      }
    }

    if (!formData.gender) {
      newErrors.gender = 'Jenis kelamin wajib dipilih';
    }

    if (!formData.parentName.trim()) {
      newErrors.parentName = 'Nama orang tua/wali wajib diisi';
    }

    if (!formData.parentPhone.trim()) {
      newErrors.parentPhone = 'Nomor telepon orang tua wajib diisi';
    }

    if (!formData.password) {
      newErrors.password = 'Password wajib diisi';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password minimal 6 karakter';
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Konfirmasi password tidak cocok';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/students', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error?.message || 'Gagal membuat siswa');
      }

      if (result.success) {
        router.push('/guru/siswa');
        router.refresh();
      }
    } catch (error) {
      setErrors({
        submit: error instanceof Error ? error.message : 'Terjadi kesalahan'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (field: keyof CreateStudentFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/guru/siswa">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Tambah Siswa Baru
          </h1>
          <p className="text-gray-600">
            Daftarkan siswa baru ke sistem
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {errors.submit && (
          <Alert variant="destructive">
            <AlertDescription>{errors.submit}</AlertDescription>
          </Alert>
        )}

        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Informasi Pribadi</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nama Lengkap *</Label>
                <Input
                  id="name"
                  placeholder="Masukkan nama lengkap siswa"
                  value={formData.name}
                  onChange={(e) => updateFormData('name', e.target.value)}
                  className={errors.name ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.name && (
                  <p className="text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="studentId">NIS (Nomor Induk Siswa) *</Label>
                <Input
                  id="studentId"
                  placeholder="Contoh: 2024001"
                  value={formData.studentId}
                  onChange={(e) => updateFormData('studentId', e.target.value)}
                  className={errors.studentId ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.studentId && (
                  <p className="text-sm text-red-600">{errors.studentId}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={(e) => updateFormData('email', e.target.value)}
                  className={errors.email ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.email && (
                  <p className="text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Nomor Telepon</Label>
                <Input
                  id="phone"
                  placeholder="08123456789"
                  value={formData.phone}
                  onChange={(e) => updateFormData('phone', e.target.value)}
                  disabled={isSubmitting}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dateOfBirth">Tanggal Lahir *</Label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => updateFormData('dateOfBirth', e.target.value)}
                  className={errors.dateOfBirth ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.dateOfBirth && (
                  <p className="text-sm text-red-600">{errors.dateOfBirth}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="gender">Jenis Kelamin *</Label>
                <Select
                  value={formData.gender}
                  onValueChange={(value) => updateFormData('gender', value)}
                  disabled={isSubmitting}
                >
                  <SelectTrigger className={errors.gender ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Pilih jenis kelamin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Laki-laki</SelectItem>
                    <SelectItem value="female">Perempuan</SelectItem>
                  </SelectContent>
                </Select>
                {errors.gender && (
                  <p className="text-sm text-red-600">{errors.gender}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Alamat</Label>
              <Input
                id="address"
                placeholder="Alamat lengkap siswa"
                value={formData.address}
                onChange={(e) => updateFormData('address', e.target.value)}
                disabled={isSubmitting}
              />
            </div>
          </CardContent>
        </Card>

        {/* Parent Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Phone className="h-5 w-5" />
              <span>Informasi Orang Tua/Wali</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="parentName">Nama Orang Tua/Wali *</Label>
                <Input
                  id="parentName"
                  placeholder="Nama lengkap orang tua/wali"
                  value={formData.parentName}
                  onChange={(e) => updateFormData('parentName', e.target.value)}
                  className={errors.parentName ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.parentName && (
                  <p className="text-sm text-red-600">{errors.parentName}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="parentPhone">Nomor Telepon Orang Tua *</Label>
                <Input
                  id="parentPhone"
                  placeholder="08123456789"
                  value={formData.parentPhone}
                  onChange={(e) => updateFormData('parentPhone', e.target.value)}
                  className={errors.parentPhone ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.parentPhone && (
                  <p className="text-sm text-red-600">{errors.parentPhone}</p>
                )}
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="parentEmail">Email Orang Tua</Label>
                <Input
                  id="parentEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.parentEmail}
                  onChange={(e) => updateFormData('parentEmail', e.target.value)}
                  disabled={isSubmitting}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Account Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mail className="h-5 w-5" />
              <span>Informasi Akun</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="password">Password *</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Minimal 6 karakter"
                  value={formData.password}
                  onChange={(e) => updateFormData('password', e.target.value)}
                  className={errors.password ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.password && (
                  <p className="text-sm text-red-600">{errors.password}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Konfirmasi Password *</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  placeholder="Ulangi password"
                  value={formData.confirmPassword}
                  onChange={(e) => updateFormData('confirmPassword', e.target.value)}
                  className={errors.confirmPassword ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.confirmPassword && (
                  <p className="text-sm text-red-600">{errors.confirmPassword}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit Buttons */}
        <div className="flex space-x-4">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex-1"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Membuat Siswa...
              </>
            ) : (
              'Buat Siswa'
            )}
          </Button>
          <Link href="/guru/siswa">
            <Button type="button" variant="outline" disabled={isSubmitting}>
              Batal
            </Button>
          </Link>
        </div>
      </form>
    </div>
  );
}
```

### 3. Student Detail Component
**File**: `src/components/student/StudentDetail.tsx`

```typescript
'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  MapPin,
  Users,
  BookOpen,
  Trophy,
  Star,
  Edit,
  UserX,
  UserCheck
} from 'lucide-react';
import Link from 'next/link';

interface StudentDetailData {
  id: number;
  name: string;
  email: string;
  studentId: string;
  dateOfBirth: string;
  gender: 'male' | 'female';
  address?: string;
  phone?: string;
  parentName?: string;
  parentPhone?: string;
  parentEmail?: string;
  enrollmentDate: string;
  totalXP: number;
  level: number;
  isActive: boolean;
  currentClassroom?: {
    id: number;
    name: string;
    grade: string;
  };
  academicProgress: {
    averageScore: number;
    attendanceRate: number;
    completedAssignments: number;
    totalAssignments: number;
  };
  badges: Array<{
    id: number;
    name: string;
    description: string;
    icon: string;
    rarity: string;
    earnedAt: string;
  }>;
  recentActivities: Array<{
    id: number;
    type: string;
    description: string;
    date: string;
    xpGained?: number;
  }>;
}

interface StudentDetailProps {
  studentId: number;
}

export function StudentDetail({ studentId }: StudentDetailProps) {
  const [student, setStudent] = useState<StudentDetailData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchStudentDetail();
  }, [studentId]);

  const fetchStudentDetail = async () => {
    try {
      const response = await fetch(`/api/students/${studentId}`);
      const data = await response.json();
      
      if (data.success) {
        setStudent(data.data);
      }
    } catch (error) {
      console.error('Error fetching student detail:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async () => {
    if (!student) return;

    const action = student.isActive ? 'deactivate' : 'activate';
    const confirmMessage = student.isActive 
      ? 'Apakah Anda yakin ingin menonaktifkan siswa ini?'
      : 'Apakah Anda yakin ingin mengaktifkan siswa ini?';

    if (!confirm(confirmMessage)) return;

    try {
      const response = await fetch(`/api/students/${studentId}/${action}`, {
        method: 'PATCH',
      });

      if (response.ok) {
        fetchStudentDetail(); // Refresh data
      }
    } catch (error) {
      console.error(`Error ${action} student:`, error);
    }
  };

  const getGenderText = (gender: string) => {
    return gender === 'male' ? 'Laki-laki' : 'Perempuan';
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-100 text-gray-800';
      case 'rare': return 'bg-blue-100 text-blue-800';
      case 'epic': return 'bg-purple-100 text-purple-800';
      case 'legendary': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getLevelProgress = () => {
    if (!student) return 0;
    const currentLevelXP = student.totalXP % 1000; // Assuming 1000 XP per level
    return (currentLevelXP / 1000) * 100;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="animate-pulse h-32 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!student) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Siswa tidak ditemukan</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <h1 className="text-2xl font-bold text-gray-900">
              {student.name}
            </h1>
            <Badge variant={student.isActive ? "default" : "secondary"}>
              {student.isActive ? 'Aktif' : 'Tidak Aktif'}
            </Badge>
            <Badge className="bg-blue-100 text-blue-800">
              Level {student.level}
            </Badge>
          </div>
          <div className="text-gray-600 space-y-1">
            <p>NIS: {student.studentId}</p>
            <p>Email: {student.email}</p>
            {student.currentClassroom && (
              <p>Kelas: {student.currentClassroom.name}</p>
            )}
          </div>
        </div>
        
        <div className="flex space-x-2">
          <Link href={`/guru/siswa/${student.id}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit Profil
            </Button>
          </Link>
          <Button
            variant={student.isActive ? "destructive" : "default"}
            onClick={handleToggleStatus}
          >
            {student.isActive ? (
              <>
                <UserX className="h-4 w-4 mr-2" />
                Nonaktifkan
              </>
            ) : (
              <>
                <UserCheck className="h-4 w-4 mr-2" />
                Aktifkan
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <Trophy className="h-6 w-6" />
              <span className="text-sm opacity-90">Total XP</span>
            </div>
            <p className="text-2xl font-bold mb-1">
              {student.totalXP.toLocaleString()}
            </p>
            <div className="mt-2">
              <div className="flex justify-between text-xs mb-1">
                <span>Level {student.level}</span>
                <span>{1000 - (student.totalXP % 1000)} XP lagi</span>
              </div>
              <Progress 
                value={getLevelProgress()}
                className="h-2 bg-blue-400"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-2xl font-bold">
                  {student.academicProgress.averageScore.toFixed(1)}
                </p>
                <p className="text-sm text-gray-600">Rata-rata Nilai</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-2xl font-bold">
                  {student.academicProgress.attendanceRate.toFixed(1)}%
                </p>
                <p className="text-sm text-gray-600">Kehadiran</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Star className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-2xl font-bold">{student.badges.length}</p>
                <p className="text-sm text-gray-600">Badge</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="academic">Akademik</TabsTrigger>
          <TabsTrigger value="badges">Badge ({student.badges.length})</TabsTrigger>
          <TabsTrigger value="activities">Aktivitas</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-5 w-5" />
                  <span>Informasi Pribadi</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Email</p>
                    <p className="font-medium">{student.email}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Tanggal Lahir</p>
                    <p className="font-medium">
                      {new Date(student.dateOfBirth).toLocaleDateString('id-ID')} 
                      ({getGenderText(student.gender)})
                    </p>
                  </div>
                </div>

                {student.phone && (
                  <div className="flex items-center space-x-3">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Telepon</p>
                      <p className="font-medium">{student.phone}</p>
                    </div>
                  </div>
                )}

                {student.address && (
                  <div className="flex items-center space-x-3">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Alamat</p>
                      <p className="font-medium">{student.address}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Parent Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="h-5 w-5" />
                  <span>Informasi Orang Tua</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {student.parentName && (
                  <div>
                    <p className="text-sm text-gray-600">Nama Orang Tua/Wali</p>
                    <p className="font-medium">{student.parentName}</p>
                  </div>
                )}

                {student.parentPhone && (
                  <div className="flex items-center space-x-3">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Telepon</p>
                      <p className="font-medium">{student.parentPhone}</p>
                    </div>
                  </div>
                )}

                {student.parentEmail && (
                  <div className="flex items-center space-x-3">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Email</p>
                      <p className="font-medium">{student.parentEmail}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="academic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Progress Akademik</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-3">Statistik Umum</h4>
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Rata-rata Nilai</span>
                        <span>{student.academicProgress.averageScore.toFixed(1)}</span>
                      </div>
                      <Progress value={student.academicProgress.averageScore} />
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Tingkat Kehadiran</span>
                        <span>{student.academicProgress.attendanceRate.toFixed(1)}%</span>
                      </div>
                      <Progress value={student.academicProgress.attendanceRate} />
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3">Progress Tugas</h4>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <p className="text-2xl font-bold text-blue-600">
                      {student.academicProgress.completedAssignments}
                    </p>
                    <p className="text-sm text-gray-600">
                      dari {student.academicProgress.totalAssignments} tugas selesai
                    </p>
                    <Progress 
                      value={(student.academicProgress.completedAssignments / student.academicProgress.totalAssignments) * 100}
                      className="mt-2"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="badges" className="space-y-4">
          {student.badges.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Belum ada badge yang diraih</p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {student.badges.map((badge) => (
                <Card key={badge.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-3">
                      <div className="text-2xl">{badge.icon || '🏆'}</div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-semibold truncate">{badge.name}</h4>
                          <Badge className={getRarityColor(badge.rarity)}>
                            {badge.rarity}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">
                          {badge.description}
                        </p>
                        <p className="text-xs text-gray-400">
                          Diraih: {new Date(badge.earnedAt).toLocaleDateString('id-ID')}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="activities" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Aktivitas Terbaru</CardTitle>
            </CardHeader>
            <CardContent>
              {student.recentActivities.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Belum ada aktivitas</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {student.recentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                      <div className="flex-1">
                        <p className="font-medium">{activity.description}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <p className="text-sm text-gray-600">
                            {new Date(activity.date).toLocaleDateString('id-ID')}
                          </p>
                          {activity.xpGained && (
                            <Badge variant="secondary" className="text-xs">
                              +{activity.xpGained} XP
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
```#
## 4. Bulk Import Modal Component
**File**: `src/components/student/BulkImportModal.tsx`

```typescript
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { 
  Upload, 
  Download, 
  FileText, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  X
} from 'lucide-react';

interface BulkImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface ImportResult {
  success: boolean;
  totalRows: number;
  successCount: number;
  errorCount: number;
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
}

export function BulkImportModal({ isOpen, onClose, onSuccess }: BulkImportModalProps) {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [step, setStep] = useState<'upload' | 'processing' | 'result'>('upload');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
        alert('Hanya file CSV yang diperbolehkan');
        return;
      }
      if (selectedFile.size > 5 * 1024 * 1024) { // 5MB limit
        alert('Ukuran file maksimal 5MB');
        return;
      }
      setFile(selectedFile);
    }
  };

  const downloadTemplate = () => {
    const csvContent = [
      'name,email,studentId,dateOfBirth,gender,address,phone,parentName,parentPhone,parentEmail,password',
      'John Doe,<EMAIL>,2024001,2010-01-15,male,Jl. Contoh No. 1,08123456789,Jane Doe,08987654321,<EMAIL>,password123',
      'Jane Smith,<EMAIL>,2024002,2010-03-20,female,Jl. Contoh No. 2,08123456790,John Smith,08987654322,<EMAIL>,password123'
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'template_import_siswa.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const handleUpload = async () => {
    if (!file) return;

    setIsUploading(true);
    setStep('processing');
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file);

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      const response = await fetch('/api/students/bulk-import', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      const result = await response.json();

      if (result.success) {
        setImportResult(result.data);
        setStep('result');
        
        if (result.data.errorCount === 0) {
          setTimeout(() => {
            onSuccess();
            handleClose();
          }, 2000);
        }
      } else {
        throw new Error(result.error?.message || 'Import gagal');
      }
    } catch (error) {
      console.error('Import error:', error);
      setImportResult({
        success: false,
        totalRows: 0,
        successCount: 0,
        errorCount: 1,
        errors: [{
          row: 0,
          field: 'general',
          message: error instanceof Error ? error.message : 'Terjadi kesalahan'
        }]
      });
      setStep('result');
    } finally {
      setIsUploading(false);
    }
  };

  const handleClose = () => {
    setFile(null);
    setIsUploading(false);
    setUploadProgress(0);
    setImportResult(null);
    setStep('upload');
    onClose();
  };

  const renderUploadStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">Import Data Siswa</h3>
        <p className="text-gray-600">
          Upload file CSV untuk menambahkan multiple siswa sekaligus
        </p>
      </div>

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Format CSV yang diperlukan:</strong>
          <br />
          name, email, studentId, dateOfBirth, gender, address, phone, parentName, parentPhone, parentEmail, password
          <br />
          <Button
            variant="link"
            className="p-0 h-auto text-blue-600"
            onClick={downloadTemplate}
          >
            <Download className="h-4 w-4 mr-1" />
            Download template CSV
          </Button>
        </AlertDescription>
      </Alert>

      <div className="space-y-4">
        <div>
          <Label htmlFor="csvFile">Pilih File CSV</Label>
          <Input
            id="csvFile"
            type="file"
            accept=".csv"
            onChange={handleFileChange}
            className="mt-1"
          />
        </div>

        {file && (
          <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg">
            <FileText className="h-5 w-5 text-blue-600" />
            <div className="flex-1">
              <p className="font-medium text-blue-900">{file.name}</p>
              <p className="text-sm text-blue-600">
                {(file.size / 1024).toFixed(1)} KB
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFile(null)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <div className="flex space-x-3">
        <Button
          onClick={handleUpload}
          disabled={!file || isUploading}
          className="flex-1"
        >
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Mengupload...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Upload & Import
            </>
          )}
        </Button>
        <Button variant="outline" onClick={handleClose}>
          Batal
        </Button>
      </div>
    </div>
  );

  const renderProcessingStep = () => (
    <div className="space-y-6 text-center">
      <div>
        <Loader2 className="h-12 w-12 text-blue-600 mx-auto mb-4 animate-spin" />
        <h3 className="text-lg font-semibold mb-2">Memproses Data</h3>
        <p className="text-gray-600">
          Sedang mengimpor data siswa, mohon tunggu...
        </p>
      </div>

      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>Progress</span>
          <span>{uploadProgress}%</span>
        </div>
        <Progress value={uploadProgress} />
      </div>
    </div>
  );

  const renderResultStep = () => {
    if (!importResult) return null;

    const isSuccess = importResult.errorCount === 0;

    return (
      <div className="space-y-6">
        <div className="text-center">
          {isSuccess ? (
            <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
          ) : (
            <AlertCircle className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
          )}
          <h3 className="text-lg font-semibold mb-2">
            {isSuccess ? 'Import Berhasil!' : 'Import Selesai dengan Peringatan'}
          </h3>
        </div>

        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="p-3 bg-blue-50 rounded-lg">
            <p className="text-2xl font-bold text-blue-600">
              {importResult.totalRows}
            </p>
            <p className="text-sm text-gray-600">Total Baris</p>
          </div>
          <div className="p-3 bg-green-50 rounded-lg">
            <p className="text-2xl font-bold text-green-600">
              {importResult.successCount}
            </p>
            <p className="text-sm text-gray-600">Berhasil</p>
          </div>
          <div className="p-3 bg-red-50 rounded-lg">
            <p className="text-2xl font-bold text-red-600">
              {importResult.errorCount}
            </p>
            <p className="text-sm text-gray-600">Error</p>
          </div>
        </div>

        {importResult.errors.length > 0 && (
          <div>
            <h4 className="font-semibold mb-3 text-red-800">
              Error yang Ditemukan:
            </h4>
            <div className="max-h-40 overflow-y-auto space-y-2">
              {importResult.errors.map((error, index) => (
                <div key={index} className="p-2 bg-red-50 border border-red-200 rounded text-sm">
                  <p className="font-medium text-red-800">
                    Baris {error.row}: {error.field}
                  </p>
                  <p className="text-red-600">{error.message}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex space-x-3">
          {isSuccess ? (
            <Button onClick={handleClose} className="flex-1">
              Selesai
            </Button>
          ) : (
            <>
              <Button onClick={() => setStep('upload')} variant="outline">
                Import Ulang
              </Button>
              <Button onClick={handleClose} className="flex-1">
                Selesai
              </Button>
            </>
          )}
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Import Data Siswa</DialogTitle>
        </DialogHeader>
        
        {step === 'upload' && renderUploadStep()}
        {step === 'processing' && renderProcessingStep()}
        {step === 'result' && renderResultStep()}
      </DialogContent>
    </Dialog>
  );
}
```

## 🔌 API Implementation

### 1. Students API Routes
**File**: `src/app/api/students/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { users, students, teachers, classroomStudents, classrooms } from '@/db/schema';
import { eq, and, sql } from 'drizzle-orm';
import { hashPassword } from '@/lib/auth';
import { createStudentSchema } from '@/lib/validations';

// GET - List all students
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    // Get students with their current classroom
    const studentsWithClassroom = await db
      .select({
        id: students.id,
        name: users.name,
        email: users.email,
        studentId: students.studentId,
        dateOfBirth: students.dateOfBirth,
        gender: students.gender,
        address: students.address,
        phone: students.phone,
        parentName: students.parentName,
        parentPhone: students.parentPhone,
        parentEmail: students.parentEmail,
        enrollmentDate: students.enrollmentDate,
        totalXP: students.totalXP,
        level: students.level,
        isActive: users.isActive,
        currentClassroom: {
          id: classrooms.id,
          name: classrooms.name,
        },
      })
      .from(students)
      .innerJoin(users, eq(students.userId, users.id))
      .leftJoin(classroomStudents, and(
        eq(students.id, classroomStudents.studentId),
        eq(classroomStudents.isActive, true)
      ))
      .leftJoin(classrooms, eq(classroomStudents.classroomId, classrooms.id))
      .orderBy(users.name);

    return NextResponse.json({
      success: true,
      data: studentsWithClassroom,
    });
  } catch (error) {
    console.error('Get students error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal mengambil data siswa' }
    }, { status: 500 });
  }
}

// POST - Create new student
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    const body = await request.json();
    
    // Validate request body
    const validationResult = createStudentSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Data tidak valid',
          details: validationResult.error.errors,
        },
      }, { status: 400 });
    }

    const studentData = validationResult.data;

    // Check for duplicate email
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.email, studentData.email.toLowerCase()))
      .limit(1);

    if (existingUser.length > 0) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'DUPLICATE_EMAIL',
          message: 'Email sudah digunakan',
        },
      }, { status: 400 });
    }

    // Check for duplicate student ID
    const existingStudent = await db
      .select()
      .from(students)
      .where(eq(students.studentId, studentData.studentId))
      .limit(1);

    if (existingStudent.length > 0) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'DUPLICATE_STUDENT_ID',
          message: 'NIS sudah digunakan',
        },
      }, { status: 400 });
    }

    // Create student in transaction
    const result = await db.transaction(async (tx) => {
      // Create user account
      const passwordHash = await hashPassword(studentData.password);
      const newUser = await tx
        .insert(users)
        .values({
          email: studentData.email.toLowerCase(),
          passwordHash,
          role: 'student',
          name: studentData.name,
          isActive: true,
        })
        .returning();

      // Create student profile
      const newStudent = await tx
        .insert(students)
        .values({
          userId: newUser[0].id,
          studentId: studentData.studentId,
          dateOfBirth: studentData.dateOfBirth,
          gender: studentData.gender,
          address: studentData.address || null,
          phone: studentData.phone || null,
          parentName: studentData.parentName || null,
          parentPhone: studentData.parentPhone || null,
          parentEmail: studentData.parentEmail || null,
          enrollmentDate: new Date().toISOString(),
          totalXP: 0,
          level: 1,
        })
        .returning();

      return { user: newUser[0], student: newStudent[0] };
    });

    return NextResponse.json({
      success: true,
      data: {
        id: result.student.id,
        name: result.user.name,
        email: result.user.email,
        studentId: result.student.studentId,
      },
      message: 'Siswa berhasil dibuat',
    });
  } catch (error) {
    console.error('Create student error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal membuat siswa' }
    }, { status: 500 });
  }
}
```

### 2. Bulk Import API
**File**: `src/app/api/students/bulk-import/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { users, students } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { hashPassword } from '@/lib/auth';

interface CSVRow {
  name: string;
  email: string;
  studentId: string;
  dateOfBirth: string;
  gender: 'male' | 'female';
  address?: string;
  phone?: string;
  parentName?: string;
  parentPhone?: string;
  parentEmail?: string;
  password: string;
}

interface ImportError {
  row: number;
  field: string;
  message: string;
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({
        success: false,
        error: { code: 'NO_FILE', message: 'File tidak ditemukan' }
      }, { status: 400 });
    }

    if (!file.name.endsWith('.csv')) {
      return NextResponse.json({
        success: false,
        error: { code: 'INVALID_FILE_TYPE', message: 'Hanya file CSV yang diperbolehkan' }
      }, { status: 400 });
    }

    // Read and parse CSV
    const csvText = await file.text();
    const lines = csvText.split('\n').filter(line => line.trim());
    
    if (lines.length < 2) {
      return NextResponse.json({
        success: false,
        error: { code: 'EMPTY_FILE', message: 'File CSV kosong atau tidak valid' }
      }, { status: 400 });
    }

    const headers = lines[0].split(',').map(h => h.trim());
    const requiredHeaders = ['name', 'email', 'studentId', 'dateOfBirth', 'gender', 'password'];
    
    // Validate headers
    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
    if (missingHeaders.length > 0) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'INVALID_HEADERS',
          message: `Header yang diperlukan tidak ditemukan: ${missingHeaders.join(', ')}`
        }
      }, { status: 400 });
    }

    // Parse data rows
    const dataRows: CSVRow[] = [];
    const errors: ImportError[] = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      const row: any = {};

      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });

      // Validate row data
      const rowErrors = validateCSVRow(row, i + 1);
      if (rowErrors.length > 0) {
        errors.push(...rowErrors);
      } else {
        dataRows.push(row as CSVRow);
      }
    }

    // Check for duplicate emails and student IDs within the CSV
    const emailSet = new Set();
    const studentIdSet = new Set();

    dataRows.forEach((row, index) => {
      if (emailSet.has(row.email)) {
        errors.push({
          row: index + 2, // +2 because we start from line 1 and skip header
          field: 'email',
          message: 'Email duplikat dalam file CSV'
        });
      } else {
        emailSet.add(row.email);
      }

      if (studentIdSet.has(row.studentId)) {
        errors.push({
          row: index + 2,
          field: 'studentId',
          message: 'NIS duplikat dalam file CSV'
        });
      } else {
        studentIdSet.add(row.studentId);
      }
    });

    // Check for existing emails and student IDs in database
    for (let i = 0; i < dataRows.length; i++) {
      const row = dataRows[i];
      
      const existingUser = await db
        .select()
        .from(users)
        .where(eq(users.email, row.email.toLowerCase()))
        .limit(1);

      if (existingUser.length > 0) {
        errors.push({
          row: i + 2,
          field: 'email',
          message: 'Email sudah terdaftar di sistem'
        });
      }

      const existingStudent = await db
        .select()
        .from(students)
        .where(eq(students.studentId, row.studentId))
        .limit(1);

      if (existingStudent.length > 0) {
        errors.push({
          row: i + 2,
          field: 'studentId',
          message: 'NIS sudah terdaftar di sistem'
        });
      }
    }

    // If there are validation errors, return them
    if (errors.length > 0) {
      return NextResponse.json({
        success: true,
        data: {
          success: false,
          totalRows: lines.length - 1,
          successCount: 0,
          errorCount: errors.length,
          errors: errors.slice(0, 10), // Limit to first 10 errors
        },
      });
    }

    // Import valid rows
    let successCount = 0;
    const importErrors: ImportError[] = [];

    for (let i = 0; i < dataRows.length; i++) {
      const row = dataRows[i];
      
      try {
        await db.transaction(async (tx) => {
          // Create user account
          const passwordHash = await hashPassword(row.password);
          const newUser = await tx
            .insert(users)
            .values({
              email: row.email.toLowerCase(),
              passwordHash,
              role: 'student',
              name: row.name,
              isActive: true,
            })
            .returning();

          // Create student profile
          await tx
            .insert(students)
            .values({
              userId: newUser[0].id,
              studentId: row.studentId,
              dateOfBirth: row.dateOfBirth,
              gender: row.gender,
              address: row.address || null,
              phone: row.phone || null,
              parentName: row.parentName || null,
              parentPhone: row.parentPhone || null,
              parentEmail: row.parentEmail || null,
              enrollmentDate: new Date().toISOString(),
              totalXP: 0,
              level: 1,
            });
        });

        successCount++;
      } catch (error) {
        console.error(`Error importing row ${i + 2}:`, error);
        importErrors.push({
          row: i + 2,
          field: 'general',
          message: 'Gagal menyimpan data ke database'
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        success: importErrors.length === 0,
        totalRows: dataRows.length,
        successCount,
        errorCount: importErrors.length,
        errors: importErrors,
      },
      message: `Import selesai: ${successCount} berhasil, ${importErrors.length} gagal`,
    });
  } catch (error) {
    console.error('Bulk import error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal mengimpor data siswa' }
    }, { status: 500 });
  }
}

function validateCSVRow(row: any, rowNumber: number): ImportError[] {
  const errors: ImportError[] = [];

  // Required fields validation
  if (!row.name?.trim()) {
    errors.push({ row: rowNumber, field: 'name', message: 'Nama wajib diisi' });
  }

  if (!row.email?.trim()) {
    errors.push({ row: rowNumber, field: 'email', message: 'Email wajib diisi' });
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row.email)) {
    errors.push({ row: rowNumber, field: 'email', message: 'Format email tidak valid' });
  }

  if (!row.studentId?.trim()) {
    errors.push({ row: rowNumber, field: 'studentId', message: 'NIS wajib diisi' });
  }

  if (!row.dateOfBirth?.trim()) {
    errors.push({ row: rowNumber, field: 'dateOfBirth', message: 'Tanggal lahir wajib diisi' });
  } else {
    const date = new Date(row.dateOfBirth);
    if (isNaN(date.getTime())) {
      errors.push({ row: rowNumber, field: 'dateOfBirth', message: 'Format tanggal tidak valid (YYYY-MM-DD)' });
    }
  }

  if (!row.gender?.trim()) {
    errors.push({ row: rowNumber, field: 'gender', message: 'Jenis kelamin wajib diisi' });
  } else if (!['male', 'female'].includes(row.gender)) {
    errors.push({ row: rowNumber, field: 'gender', message: 'Jenis kelamin harus "male" atau "female"' });
  }

  if (!row.password?.trim()) {
    errors.push({ row: rowNumber, field: 'password', message: 'Password wajib diisi' });
  } else if (row.password.length < 6) {
    errors.push({ row: rowNumber, field: 'password', message: 'Password minimal 6 karakter' });
  }

  return errors;
}
```

## 📝 Validation Schemas

### Student Validation
**File**: `src/lib/validations.ts` (addition)

```typescript
export const createStudentSchema = z.object({
  name: z
    .string()
    .min(1, 'Nama siswa wajib diisi')
    .min(2, 'Nama siswa minimal 2 karakter')
    .max(100, 'Nama siswa maksimal 100 karakter'),
  email: z
    .string()
    .min(1, 'Email wajib diisi')
    .email('Format email tidak valid'),
  studentId: z
    .string()
    .min(1, 'NIS wajib diisi')
    .max(20, 'NIS maksimal 20 karakter'),
  dateOfBirth: z
    .string()
    .min(1, 'Tanggal lahir wajib diisi')
    .refine((date) => {
      const birthDate = new Date(date);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      return age >= 5 && age <= 25;
    }, 'Usia siswa harus antara 5-25 tahun'),
  gender: z
    .enum(['male', 'female'], {
      errorMap: () => ({ message: 'Jenis kelamin wajib dipilih' })
    }),
  address: z.string().optional(),
  phone: z.string().optional(),
  parentName: z.string().optional(),
  parentPhone: z.string().optional(),
  parentEmail: z.string().email('Format email orang tua tidak valid').optional().or(z.literal('')),
  password: z
    .string()
    .min(6, 'Password minimal 6 karakter'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Konfirmasi password tidak cocok',
  path: ['confirmPassword'],
});
```

## 🧪 Testing

### Component Testing
```typescript
// Example test for StudentList component
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { StudentList } from '@/components/student/StudentList';

// Mock fetch
global.fetch = jest.fn();

describe('StudentList', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  it('should display students after loading', async () => {
    const mockStudents = [
      {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        studentId: '2024001',
        totalXP: 1500,
        level: 2,
        isActive: true,
      },
    ];

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: mockStudents }),
    });

    render(<StudentList />);

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('2024001')).toBeInTheDocument();
      expect(screen.getByText('Level 2')).toBeInTheDocument();
    });
  });

  it('should filter students by search term', async () => {
    const mockStudents = [
      { id: 1, name: 'John Doe', studentId: '2024001' },
      { id: 2, name: 'Jane Smith', studentId: '2024002' },
    ];

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: mockStudents }),
    });

    render(<StudentList />);

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText('Cari siswa...');
    fireEvent.change(searchInput, { target: { value: 'Jane' } });

    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
  });
});
```

### API Testing
```typescript
// Test student API endpoints
describe('/api/students', () => {
  it('should create new student', async () => {
    const studentData = {
      name: 'Test Student',
      email: '<EMAIL>',
      studentId: 'TEST001',
      dateOfBirth: '2010-01-01',
      gender: 'male',
      password: 'password123',
      confirmPassword: 'password123',
    };

    const response = await fetch('/api/students', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(studentData),
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.name).toBe('Test Student');
  });

  it('should prevent duplicate email', async () => {
    // Create first student
    await createTestStudent('<EMAIL>');

    // Try to create duplicate
    const response = await fetch('/api/students', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'Duplicate Student',
        email: '<EMAIL>',
        studentId: 'DUP001',
        dateOfBirth: '2010-01-01',
        gender: 'female',
        password: 'password123',
        confirmPassword: 'password123',
      }),
    });

    expect(response.status).toBe(400);
    const data = await response.json();
    expect(data.error.code).toBe('DUPLICATE_EMAIL');
  });
});
```

## 📱 Mobile Responsiveness

### Responsive Table Design
```css
/* Student table responsive design */
@media (max-width: 768px) {
  .student-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .student-table th,
  .student-table td {
    min-width: 120px;
    padding: 8px 12px;
  }
  
  .student-card {
    display: block;
  }
  
  .student-card .desktop-only {
    display: none;
  }
}

/* Mobile student cards */
.mobile-student-card {
  @apply p-4 border rounded-lg space-y-2;
}

.mobile-student-card .student-name {
  @apply font-semibold text-lg;
}

.mobile-student-card .student-details {
  @apply text-sm text-gray-600 space-y-1;
}
```

### Mobile-Optimized Forms
```typescript
// Mobile-friendly form layout
<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
  <div className="sm:col-span-2">
    <Label htmlFor="name">Nama Lengkap *</Label>
    <Input id="name" className="text-base" /> {/* Prevent zoom on iOS */}
  </div>
  
  <div>
    <Label htmlFor="email">Email *</Label>
    <Input id="email" type="email" inputMode="email" />
  </div>
  
  <div>
    <Label htmlFor="phone">Telepon</Label>
    <Input id="phone" type="tel" inputMode="tel" />
  </div>
</div>
```

## 🔒 Security Considerations

### Data Protection
- Password hashing dengan bcrypt
- Input sanitization dan validation
- Role-based access control
- Secure file upload handling

### Privacy Compliance
- Minimal data collection
- Secure storage of personal information
- Parent consent for student data
- Data retention policies

---

## 🎯 Next Steps

Setelah Task 7 selesai, student management siap untuk:
1. **Attendance System Integration** (Task 8)
2. **Assignment Management** (Future tasks)
3. **Gamification Enhancement** (Badge system, XP rewards)

## 📚 Resources

- [CSV Processing Best Practices](https://www.rfc-editor.org/rfc/rfc4180.html)
- [File Upload Security](https://cheatsheetseries.owasp.org/cheatsheets/File_Upload_Cheat_Sheet.html)
- [Student Data Privacy](https://studentprivacy.ed.gov/)
- [Bulk Import UX Patterns](https://uxdesign.cc/designing-better-bulk-import-experiences-4d8b8b8b8b8b)

---

**Status**: ✅ **Completed**  
**Duration**: ~10 hours  
**Complexity**: High  
**Dependencies**: Task 1-6 (Project Setup, Database, Auth, Onboarding, Dashboard, Classroom Management)