# Task 8: Attendance Management System

## 📋 Overview

Task ini mengimplementasikan sistem manajemen absensi yang komprehensif untuk GuruFlow MVP. Sistem mendukung pencatatan kehadiran siswa dengan kode absensi Indonesia (H/I/S/A), audit trail untuk perubahan data, dan reporting yang lengkap dengan export ke berbagai format.

## ✅ Komponen yang Diimplementasi

### 8.1 Create Attendance Input Interface ✅
### 8.2 Implement Attendance Data Management ✅  
### 8.3 Build Attendance Reporting and Export Features ✅

## 🏗️ Arsitektur Attendance Management

### System Flow
```mermaid
graph TD
    A[Teacher Login] --> B[Attendance Manager]
    B --> C{Select Tab}
    
    C -->|Input| D[Attendance Grid]
    C -->|History| E[Attendance History]
    C -->|Stats| F[Attendance Stats]
    C -->|Reports| G[Attendance Reports]
    
    D --> H[Select Classroom & Subject]
    D --> I[Mark Attendance]
    D --> J[Save Records]
    
    E --> K[View History]
    E --> L[Edit Records]
    E --> M[Export CSV]
    
    F --> N[View Statistics]
    F --> O[Analyze Trends]
    
    G --> P[Generate Reports]
    G --> Q[Export PDF/CSV]
```

## 🎯 Core Components

### 1. Attendance Manager (Main Component)
**File**: `src/components/attendance/AttendanceManager.tsx`

```typescript
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AttendanceGrid } from './AttendanceGrid';
import { AttendanceHistory } from './AttendanceHistory';
import { AttendanceStats } from './AttendanceStats';
import { AttendanceReports } from './AttendanceReports';
import { Calendar, Users, Save, RotateCcw } from 'lucide-react';

export function AttendanceManager() {
  const [classrooms, setClassrooms] = useState<Classroom[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [selectedClassroom, setSelectedClassroom] = useState<string>('');
  const [selectedSubject, setSelectedSubject] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  const [activeTab, setActiveTab] = useState<'input' | 'history' | 'stats' | 'reports'>('input');

  // Component implementation...
}
```

**Fitur:**
- **Tab Navigation**: Input, History, Statistics, Reports
- **Classroom & Subject Selection**: Dynamic filtering
- **Date Selection**: Dengan validasi maksimal hari ini
- **Responsive Design**: Mobile-friendly interface

### 2. Attendance Grid (Daily Input)
**File**: `src/components/attendance/AttendanceGrid.tsx`

```typescript
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const ATTENDANCE_STATUS = {
  H: { label: 'Hadir', color: 'bg-green-100 text-green-800 hover:bg-green-200', icon: UserCheck },
  I: { label: 'Izin', color: 'bg-blue-100 text-blue-800 hover:bg-blue-200', icon: Clock },
  S: { label: 'Sakit', color: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200', icon: AlertCircle },
  A: { label: 'Alpa', color: 'bg-red-100 text-red-800 hover:bg-red-200', icon: UserX },
} as const;

export function AttendanceGrid({ classroomId, subjectId, date }: AttendanceGridProps) {
  // Implementation with real-time statistics and bulk operations
}
```

**Fitur:**
- **Indonesian Attendance Codes**: H (Hadir), I (Izin), S (Sakit), A (Alpa)
- **Real-time Statistics**: Live calculation of attendance rates
- **Bulk Operations**: "Semua Hadir" button untuk efficiency
- **Visual Feedback**: Color-coded status buttons
- **Auto-save**: Deteksi perubahan dan save functionality

### 3. Attendance History (Data Management)
**File**: `src/components/attendance/AttendanceHistory.tsx`

```typescript
'use client';

import { useState, useEffect } from 'react';
import { EditAttendanceModal } from './EditAttendanceModal';

export function AttendanceHistory({ classroomId, subjectId }: AttendanceHistoryProps) {
  const [records, setRecords] = useState<AttendanceHistoryRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<AttendanceHistoryRecord[]>([]);
  const [editingAttendance, setEditingAttendance] = useState<AttendanceHistoryRecord | null>(null);

  // Advanced filtering and search functionality
  // Edit capabilities with audit trail
  // CSV export functionality
}
```

**Fitur:**
- **Advanced Filtering**: By date, status, student name
- **Search Functionality**: Real-time search across records
- **Edit Capabilities**: Modal untuk edit dengan audit trail
- **Export to CSV**: Download filtered data
- **Pagination**: Efficient handling of large datasets

### 4. Edit Attendance Modal (Audit Trail)
**File**: `src/components/attendance/EditAttendanceModal.tsx`

```typescript
'use client';

import { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';

interface AttendanceEditData {
  status: 'H' | 'I' | 'S' | 'A';
  notes: string;
  reason: string; // Required for audit trail
}

export function EditAttendanceModal({ 
  isOpen, 
  onClose, 
  onSave, 
  attendance 
}: EditAttendanceModalProps) {
  // Form validation with required reason for changes
  // Audit trail integration
  // Status change tracking
}
```

**Fitur:**
- **Audit Trail**: Mandatory reason untuk setiap perubahan
- **Status Change Tracking**: Record old vs new values
- **Validation**: Comprehensive form validation
- **User Context**: Display student dan class information

### 5. Attendance Statistics (Analytics)
**File**: `src/components/attendance/AttendanceStats.tsx`

```typescript
'use client';

import { useState, useEffect } from 'react';
import { Progress } from '@/components/ui/progress';

export function AttendanceStats({ classroomId, subjectId }: AttendanceStatsProps) {
  const [stats, setStats] = useState<AttendanceStatsData | null>(null);
  const [timeRange, setTimeRange] = useState('30'); // days

  // Statistical analysis and trend calculation
  // Monthly breakdown and comparison
  // Visual progress indicators
}
```

**Fitur:**
- **Time Range Selection**: 7 days, 30 days, 3 months, 1 year
- **Trend Analysis**: Month-over-month comparison
- **Visual Indicators**: Progress bars dan color-coded metrics
- **Breakdown by Status**: Detailed statistics per attendance code
- **Monthly Trends**: Historical data visualization

### 6. Attendance Reports (Export & Reporting)
**File**: `src/components/attendance/AttendanceReports.tsx`

```typescript
'use client';

import { useState, useEffect } from 'react';

interface ReportFilters {
  classroomId: string;
  subjectId: string;
  startDate: string;
  endDate: string;
  reportType: 'summary' | 'detailed' | 'individual';
  format: 'pdf' | 'csv';
}

export function AttendanceReports() {
  // Comprehensive report generation
  // Multiple format support
  // Quick report templates
}
```

**Fitur:**
- **Multiple Report Types**: Summary, Detailed, Individual
- **Export Formats**: PDF dan CSV
- **Date Range Selection**: Flexible period selection
- **Quick Templates**: Pre-configured common reports
- **Validation**: Comprehensive input validation

## 🔌 API Implementation

### 1. Main Attendance API
**File**: `src/app/api/attendance/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';

// GET - Retrieve attendance records
export async function GET(request: NextRequest) {
  // Query attendance records with filters
  // Support for classroom, subject, and date filtering
  // Join with student and subject data
}

// POST - Save attendance records
export async function POST(request: NextRequest) {
  // Batch save attendance records
  // Transaction-based operations
  // Validation and error handling
}
```

**Endpoints:**
- `GET /api/attendance` - Retrieve records dengan filtering
- `POST /api/attendance` - Batch save attendance records

### 2. Attendance History API
**File**: `src/app/api/attendance/history/route.ts`

```typescript
// GET - Get attendance history with pagination
export async function GET(request: NextRequest) {
  // Advanced filtering capabilities
  // Pagination support
  // Join dengan student, classroom, dan subject data
  // Sorting by date and student name
}
```

### 3. Individual Attendance API
**File**: `src/app/api/attendance/[id]/route.ts`

```typescript
// PUT - Update individual attendance record
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  // Update attendance dengan audit trail
  // Validation untuk required reason
  // Transaction untuk consistency
}

// GET - Get attendance record dengan audit trail
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  // Retrieve record dengan complete audit history
  // Include change tracking information
}
```

### 4. Attendance Statistics API
**File**: `src/app/api/attendance/stats/route.ts`

```typescript
// GET - Generate attendance statistics
export async function GET(request: NextRequest) {
  // Calculate comprehensive statistics
  // Time-based filtering
  // Trend analysis calculations
  // Monthly breakdown generation
}
```

### 5. Attendance Reports API
**File**: `src/app/api/attendance/reports/route.ts`

```typescript
// GET - Generate and download reports
export async function GET(request: NextRequest) {
  // Multiple report type generation
  // CSV and PDF format support
  // Dynamic content generation
  // File download handling
}
```

## 🗄️ Database Schema Extensions

### Attendance Audit Trail
**File**: `src/db/schema.ts` (addition)

```typescript
// Attendance audit trail
export const attendanceAudit = sqliteTable('attendance_audit', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  attendanceId: integer('attendance_id').notNull().references(() => attendance.id, { onDelete: 'cascade' }),
  oldStatus: text('old_status', { enum: ['H', 'I', 'S', 'A'] }),
  newStatus: text('new_status', { enum: ['H', 'I', 'S', 'A'] }).notNull(),
  oldNotes: text('old_notes'),
  newNotes: text('new_notes'),
  reason: text('reason').notNull(),
  changedBy: integer('changed_by').notNull().references(() => teachers.id),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
});
```

**Purpose**: 
- Track semua perubahan attendance records
- Compliance dan accountability
- Forensic analysis capabilities
- Change history untuk reporting

## 🎨 UI Components

### New UI Components Created

#### 1. Dialog Component
**File**: `src/components/ui/dialog.tsx`
- Modal dialog dengan Radix UI
- Accessible dan keyboard navigation
- Backdrop blur dan animations

#### 2. Textarea Component  
**File**: `src/components/ui/textarea.tsx`
- Multi-line text input
- Consistent styling dengan design system
- Auto-resize capabilities

#### 3. Progress Component
**File**: `src/components/ui/progress.tsx`
- Visual progress indicators
- Animated progress bars
- Customizable colors dan sizes

## 📊 Indonesian Attendance System

### Attendance Codes
```typescript
const ATTENDANCE_STATUS = {
  H: { label: 'Hadir', color: 'bg-green-100 text-green-800' },      // Present
  I: { label: 'Izin', color: 'bg-blue-100 text-blue-800' },        // Excused
  S: { label: 'Sakit', color: 'bg-yellow-100 text-yellow-800' },   // Sick
  A: { label: 'Alpa', color: 'bg-red-100 text-red-800' },          // Absent
} as const;
```

### Calculation Logic
```typescript
// Attendance rate calculation
const attendanceRate = totalRecords > 0 ? (presentCount / totalRecords) * 100 : 0;

// Status breakdown
const statusBreakdown = {
  H: records.filter(r => r.status === 'H').length,
  I: records.filter(r => r.status === 'I').length,
  S: records.filter(r => r.status === 'S').length,
  A: records.filter(r => r.status === 'A').length,
};
```

## 🔒 Security & Validation

### Access Control
```typescript
// Role-based access control
if (!user || user.role !== 'teacher') {
  return NextResponse.json({
    success: false,
    error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
  }, { status: 401 });
}
```

### Data Validation
```typescript
// Input validation
if (!classroomId || !subjectId || !date || !records || !Array.isArray(records)) {
  return NextResponse.json({
    success: false,
    error: { code: 'INVALID_DATA', message: 'Data tidak valid' }
  }, { status: 400 });
}
```

### Audit Trail Security
```typescript
// Mandatory reason for changes
if (!status || !reason) {
  return NextResponse.json({
    success: false,
    error: { code: 'INVALID_DATA', message: 'Status dan alasan perubahan wajib diisi' }
  }, { status: 400 });
}
```

## 📱 Mobile Responsiveness

### Responsive Grid Layout
```css
/* Attendance grid responsive design */
.attendance-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .attendance-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .attendance-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

### Mobile-Optimized Controls
```typescript
// Touch-friendly attendance buttons
<div className="grid grid-cols-2 gap-2">
  {Object.entries(ATTENDANCE_STATUS).map(([status, config]) => (
    <Button
      key={status}
      variant={isSelected ? "default" : "outline"}
      size="sm"
      className={`touch-target ${isSelected ? config.color : 'hover:bg-gray-100'}`}
    >
      <Icon className="h-4 w-4 mr-1" />
      {status}
    </Button>
  ))}
</div>
```

## 🧪 Testing

### Component Testing
```typescript
// Example test for AttendanceGrid
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { AttendanceGrid } from '@/components/attendance/AttendanceGrid';

describe('AttendanceGrid', () => {
  it('should display students and allow status changes', async () => {
    const mockStudents = [
      { id: 1, name: 'John Doe', studentId: '2024001' },
    ];

    render(
      <AttendanceGrid 
        classroomId={1} 
        subjectId={1} 
        date="2024-01-15" 
      />
    );

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    // Test status change
    const hadirButton = screen.getByText('H');
    fireEvent.click(hadirButton);
    
    expect(hadirButton).toHaveClass('bg-green-100');
  });
});
```

### API Testing
```typescript
// Test attendance API endpoints
describe('/api/attendance', () => {
  it('should save attendance records', async () => {
    const attendanceData = {
      classroomId: 1,
      subjectId: 1,
      date: '2024-01-15',
      records: [
        { studentId: 1, status: 'H', notes: '' }
      ]
    };

    const response = await fetch('/api/attendance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(attendanceData),
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
  });
});
```

## 📈 Performance Optimizations

### Database Indexing
```sql
-- Performance indexes for attendance queries
CREATE INDEX idx_attendance_date ON attendance(date);
CREATE INDEX idx_attendance_classroom_subject ON attendance(classroom_subject_id);
CREATE INDEX idx_attendance_student_date ON attendance(student_id, date);
CREATE INDEX idx_attendance_status ON attendance(status);

-- Audit trail indexes
CREATE INDEX idx_attendance_audit_attendance_id ON attendance_audit(attendance_id);
CREATE INDEX idx_attendance_audit_created_at ON attendance_audit(created_at);
```

### Query Optimization
```typescript
// Efficient batch operations
await db.transaction(async (tx) => {
  // Delete existing records for this date
  await tx.delete(attendance).where(and(
    eq(attendance.classroomSubjectId, classroomSubjectId),
    eq(attendance.date, date)
  ));

  // Insert new records in batch
  if (attendanceData.length > 0) {
    await tx.insert(attendance).values(attendanceData);
  }
});
```

### Client-Side Optimization
```typescript
// Memoized calculations
const stats = useMemo(() => {
  const records = Object.values(attendance);
  return {
    H: records.filter(r => r.status === 'H').length,
    I: records.filter(r => r.status === 'I').length,
    S: records.filter(r => r.status === 'S').length,
    A: records.filter(r => r.status === 'A').length,
  };
}, [attendance]);
```

## 📊 Reporting Features

### Report Types

#### 1. Summary Report
- Daily attendance statistics
- Percentage calculations
- Status breakdown (H/I/S/A)
- Trend analysis

#### 2. Detailed Report  
- Individual student records
- Date-by-date breakdown
- Subject-specific data
- Notes and comments

#### 3. Individual Report
- Per-student statistics
- Attendance patterns
- Performance metrics
- Historical trends

### Export Formats

#### CSV Export
```typescript
const csvContent = [
  ['Tanggal', 'Nama Siswa', 'NIS', 'Kelas', 'Mata Pelajaran', 'Status', 'Keterangan'],
  ...filteredRecords.map(record => [
    new Date(record.date).toLocaleDateString('id-ID'),
    record.studentName,
    record.studentId,
    record.classroomName,
    record.subjectName,
    ATTENDANCE_STATUS[record.status].label,
    record.notes || ''
  ])
].map(row => row.join(',')).join('\n');
```

#### PDF Export (HTML Template)
```html
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Laporan Absensi</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .header { text-align: center; margin-bottom: 30px; }
    table { width: 100%; border-collapse: collapse; }
    th, td { border: 1px solid #ddd; padding: 8px; }
  </style>
</head>
<body>
  <div class="header">
    <h1>{{schoolName}}</h1>
    <h2>Laporan Absensi</h2>
  </div>
  <!-- Dynamic content generation -->
</body>
</html>
```

## 🔧 Configuration & Setup

### Environment Variables
```bash
# Database configuration (already configured)
DATABASE_URL="your-turso-database-url"
DATABASE_AUTH_TOKEN="your-turso-auth-token"

# Optional: File storage for report caching
STORAGE_ENDPOINT="your-storage-endpoint"
```

### Navigation Integration
```typescript
// Updated QuickActions component
const teacherActions: QuickAction[] = [
  {
    title: 'Catat Absensi',
    description: 'Rekam kehadiran siswa hari ini',
    href: '/guru/absensi',
    icon: Calendar,
    color: 'bg-blue-500 hover:bg-blue-600',
  },
  // ... other actions
];
```

## 🚀 Usage Guide

### Daily Attendance Workflow

1. **Access Attendance Page**
   ```
   /guru/absensi
   ```

2. **Select Classroom and Subject**
   - Choose from dropdown lists
   - Select date (default: today)

3. **Mark Attendance**
   - Click status buttons (H/I/S/A) for each student
   - Use "Semua Hadir" for bulk marking
   - Add notes if needed

4. **Save Records**
   - Click "Simpan Absensi" button
   - Confirmation message displayed

### Editing Attendance

1. **Access History Tab**
   - Switch to "Riwayat Absensi" tab
   - Use filters to find records

2. **Edit Record**
   - Click edit button on record
   - Change status and add reason
   - Save changes (creates audit trail)

### Generating Reports

1. **Access Reports Tab**
   - Switch to "Laporan" tab
   - Configure report parameters

2. **Select Options**
   - Choose report type (Summary/Detailed/Individual)
   - Set date range
   - Select format (PDF/CSV)

3. **Generate and Download**
   - Click "Generate Laporan"
   - File automatically downloads

## 🔍 Troubleshooting

### Common Issues

1. **Attendance Not Saving**
   ```typescript
   // Check network connectivity
   // Verify classroom and subject selection
   // Ensure date is not in future
   ```

2. **Export Not Working**
   ```typescript
   // Check date range validity
   // Verify classroom selection
   // Ensure sufficient data exists
   ```

3. **Edit Modal Not Opening**
   ```typescript
   // Check user permissions
   // Verify record exists
   // Check for JavaScript errors
   ```

### Debug Information
```typescript
// Enable debug logging
console.log('Attendance data:', attendance);
console.log('Selected filters:', { classroomId, subjectId, date });
console.log('API response:', result);
```

## 📚 Integration Points

### Dashboard Integration
- Quick action untuk "Catat Absensi"
- Statistics cards dengan attendance rates
- Recent activity feed

### Student Management Integration
- Student enrollment status affects attendance
- Bulk import students automatically available for attendance
- Student profile shows attendance history

### Classroom Management Integration
- Classroom subjects determine available attendance sessions
- Teacher assignments control access permissions
- Class schedules integrate with attendance dates

## 🎯 Future Enhancements

### Planned Features
1. **Automated Notifications**
   - Parent notifications untuk absent students
   - Teacher reminders untuk missing attendance
   - Weekly/monthly summary emails

2. **Advanced Analytics**
   - Predictive attendance modeling
   - Pattern recognition untuk chronic absenteeism
   - Comparative analysis across classes

3. **Mobile App Integration**
   - QR code scanning untuk quick attendance
   - Offline mode dengan sync capabilities
   - Push notifications

4. **Integration Enhancements**
   - Calendar integration untuk scheduled classes
   - SMS gateway untuk parent notifications
   - Biometric attendance devices

## 📋 Compliance & Standards

### Indonesian Education Standards
- Follows Indonesian attendance coding (H/I/S/A)
- Supports Indonesian date formats
- Complies with local reporting requirements

### Data Privacy
- Secure storage of student attendance data
- Audit trail untuk accountability
- Role-based access control

### Backup & Recovery
- Automated database backups
- Point-in-time recovery capabilities
- Data export untuk compliance

---

## 🎯 Next Steps

Setelah Task 8 selesai, attendance management system siap untuk:
1. **Mobile Responsiveness Enhancement** (Task 9)
2. **Data Export Integration** (Task 10)
3. **Testing Implementation** (Task 11)
4. **Production Deployment** (Task 12)

## 📚 Resources

- [Indonesian Education System](https://www.kemdikbud.go.id/)
- [Attendance Management Best Practices](https://www.edutopia.org/attendance-tracking)
- [React Performance Optimization](https://react.dev/learn/render-and-commit)
- [Database Audit Trail Design](https://www.sqlshack.com/database-audit-trail-implementation/)

---

**Status**: ✅ **Completed**  
**Duration**: ~12 hours  
**Complexity**: High  
**Dependencies**: Task 1-7 (All previous systems)