import { test, expect } from '@playwright/test';
import { 
  loginAsTeacher, 
  navigateToAttendance, 
  fillAttendanceForm,
  expectToastMessage,
  waitForTableToLoad,
  setupTestData
} from './utils/test-helpers';

test.describe('Attendance Management', () => {
  test.beforeEach(async ({ page }) => {
    await loginAsTeacher(page);
    await setupTestData(page);
    await navigateToAttendance(page);
  });

  test('should display attendance page', async ({ page }) => {
    await expect(page.locator('h1')).toContainText('Manajemen Absensi');
    await expect(page.locator('[data-testid="classroom-select"]')).toBeVisible();
    await expect(page.locator('[data-testid="subject-select"]')).toBeVisible();
    await expect(page.locator('[data-testid="date-picker"]')).toBeVisible();
  });

  test('should load students when classroom and subject are selected', async ({ page }) => {
    // Select classroom
    await page.click('[data-testid="classroom-select"]');
    await page.click('[data-testid="classroom-option-1"]');
    
    // Select subject
    await page.click('[data-testid="subject-select"]');
    await page.click('[data-testid="subject-option-1"]');
    
    // Wait for students to load
    await waitForTableToLoad(page);
    
    // Should display student list
    await expect(page.locator('[data-testid="student-list"]')).toBeVisible();
    await expect(page.locator('[data-testid="student-item"]')).toHaveCount(2); // We created 2 test students
  });

  test('should mark attendance for students', async ({ page }) => {
    // Setup: Select classroom and subject
    await page.click('[data-testid="classroom-select"]');
    await page.click('[data-testid="classroom-option-1"]');
    await page.click('[data-testid="subject-select"]');
    await page.click('[data-testid="subject-option-1"]');
    
    await waitForTableToLoad(page);
    
    // Mark attendance
    await fillAttendanceForm(page, {
      '1': 'H', // Student 1: Hadir
      '2': 'I', // Student 2: Izin
    });
    
    // Save attendance
    await page.click('[data-testid="save-attendance-button"]');
    
    // Should show success message
    await expectToastMessage(page, 'Absensi berhasil disimpan');
  });

  test('should use bulk attendance actions', async ({ page }) => {
    // Setup
    await page.click('[data-testid="classroom-select"]');
    await page.click('[data-testid="classroom-option-1"]');
    await page.click('[data-testid="subject-select"]');
    await page.click('[data-testid="subject-option-1"]');
    
    await waitForTableToLoad(page);
    
    // Use "Semua Hadir" bulk action
    await page.click('[data-testid="bulk-all-present"]');
    
    // All students should be marked as present
    const presentButtons = page.locator('[data-testid*="attendance"][data-testid*="-H"]');
    const count = await presentButtons.count();
    
    for (let i = 0; i < count; i++) {
      await expect(presentButtons.nth(i)).toHaveClass(/selected|active/);
    }
    
    // Reset attendance
    await page.click('[data-testid="bulk-reset"]');
    
    // All buttons should be unselected
    for (let i = 0; i < count; i++) {
      await expect(presentButtons.nth(i)).not.toHaveClass(/selected|active/);
    }
  });

  test('should validate attendance before saving', async ({ page }) => {
    // Setup
    await page.click('[data-testid="classroom-select"]');
    await page.click('[data-testid="classroom-option-1"]');
    await page.click('[data-testid="subject-select"]');
    await page.click('[data-testid="subject-option-1"]');
    
    await waitForTableToLoad(page);
    
    // Try to save without marking any attendance
    await page.click('[data-testid="save-attendance-button"]');
    
    // Should show validation error
    await expect(page.locator('text=Harap tandai kehadiran untuk semua siswa')).toBeVisible();
  });

  test('should load existing attendance data', async ({ page }) => {
    // Setup and mark attendance first
    await page.click('[data-testid="classroom-select"]');
    await page.click('[data-testid="classroom-option-1"]');
    await page.click('[data-testid="subject-select"]');
    await page.click('[data-testid="subject-option-1"]');
    
    await waitForTableToLoad(page);
    
    await fillAttendanceForm(page, {
      '1': 'H',
      '2': 'I',
    });
    
    await page.click('[data-testid="save-attendance-button"]');
    await expectToastMessage(page, 'Absensi berhasil disimpan');
    
    // Refresh the page
    await page.reload();
    
    // Select same classroom and subject
    await page.click('[data-testid="classroom-select"]');
    await page.click('[data-testid="classroom-option-1"]');
    await page.click('[data-testid="subject-select"]');
    await page.click('[data-testid="subject-option-1"]');
    
    await waitForTableToLoad(page);
    
    // Should load existing attendance data
    await expect(page.locator('[data-testid="attendance-1-H"]')).toHaveClass(/selected|active/);
    await expect(page.locator('[data-testid="attendance-2-I"]')).toHaveClass(/selected|active/);
  });

  test('should display attendance statistics', async ({ page }) => {
    // Setup and mark attendance
    await page.click('[data-testid="classroom-select"]');
    await page.click('[data-testid="classroom-option-1"]');
    await page.click('[data-testid="subject-select"]');
    await page.click('[data-testid="subject-option-1"]');
    
    await waitForTableToLoad(page);
    
    await fillAttendanceForm(page, {
      '1': 'H',
      '2': 'I',
    });
    
    await page.click('[data-testid="save-attendance-button"]');
    
    // Navigate to attendance statistics
    await page.click('[data-testid="attendance-stats-tab"]');
    
    // Should display statistics
    await expect(page.locator('[data-testid="total-students"]')).toContainText('2');
    await expect(page.locator('[data-testid="present-count"]')).toContainText('1');
    await expect(page.locator('[data-testid="excused-count"]')).toContainText('1');
    await expect(page.locator('[data-testid="attendance-rate"]')).toContainText('50%');
  });

  test('should export attendance data', async ({ page }) => {
    // Setup and mark attendance
    await page.click('[data-testid="classroom-select"]');
    await page.click('[data-testid="classroom-option-1"]');
    await page.click('[data-testid="subject-select"]');
    await page.click('[data-testid="subject-option-1"]');
    
    await waitForTableToLoad(page);
    
    await fillAttendanceForm(page, {
      '1': 'H',
      '2': 'I',
    });
    
    await page.click('[data-testid="save-attendance-button"]');
    
    // Test CSV export
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="export-csv-button"]');
    const download = await downloadPromise;
    
    expect(download.suggestedFilename()).toMatch(/\.csv$/);
    
    // Test PDF export
    const pdfDownloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="export-pdf-button"]');
    const pdfDownload = await pdfDownloadPromise;
    
    expect(pdfDownload.suggestedFilename()).toMatch(/\.pdf$/);
  });

  test('should handle date navigation', async ({ page }) => {
    // Setup
    await page.click('[data-testid="classroom-select"]');
    await page.click('[data-testid="classroom-option-1"]');
    await page.click('[data-testid="subject-select"]');
    await page.click('[data-testid="subject-option-1"]');
    
    // Change date
    await page.click('[data-testid="date-picker"]');
    await page.click('[data-testid="date-prev"]'); // Go to previous day
    
    await waitForTableToLoad(page);
    
    // Should load attendance for the selected date
    await expect(page.locator('[data-testid="student-list"]')).toBeVisible();
    
    // Navigate to next day
    await page.click('[data-testid="date-next"]');
    
    await waitForTableToLoad(page);
  });

  test('should work on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Should display mobile-optimized layout
    await expect(page.locator('[data-testid="mobile-attendance-grid"]')).toBeVisible();
    
    // Mobile navigation should work
    await page.click('[data-testid="mobile-menu-button"]');
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
    
    // Attendance marking should work on mobile
    await page.click('[data-testid="classroom-select"]');
    await page.click('[data-testid="classroom-option-1"]');
    await page.click('[data-testid="subject-select"]');
    await page.click('[data-testid="subject-option-1"]');
    
    await waitForTableToLoad(page);
    
    // Touch interactions should work
    await page.tap('[data-testid="attendance-1-H"]');
    await expect(page.locator('[data-testid="attendance-1-H"]')).toHaveClass(/selected|active/);
  });
});