import { test, expect } from '@playwright/test';
import { loginAsTeacher, loginAsStudent, logout, TEST_USERS } from './utils/test-helpers';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Start from the login page
    await page.goto('/login');
  });

  test('should display login form', async ({ page }) => {
    await expect(page.locator('h1')).toContainText('Masuk ke GuruFlow');
    await expect(page.locator('[data-testid="email-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="password-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="login-button"]')).toBeVisible();
  });

  test('should show validation errors for empty fields', async ({ page }) => {
    await page.click('[data-testid="login-button"]');
    
    await expect(page.locator('text=Email wajib diisi')).toBeVisible();
    await expect(page.locator('text=Password wajib diisi')).toBeVisible();
  });

  test('should show validation error for invalid email format', async ({ page }) => {
    await page.fill('[data-testid="email-input"]', 'invalid-email');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');
    
    await expect(page.locator('text=Format email tidak valid')).toBeVisible();
  });

  test('should show error for invalid credentials', async ({ page }) => {
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');
    await page.click('[data-testid="login-button"]');
    
    await expect(page.locator('text=Email atau password salah')).toBeVisible();
  });

  test('should login teacher successfully', async ({ page }) => {
    await loginAsTeacher(page);
    
    // Should be redirected to teacher dashboard
    await expect(page).toHaveURL('/guru');
    await expect(page.locator('h1')).toContainText('Dashboard Guru');
    await expect(page.locator(`text=${TEST_USERS.teacher.name}`)).toBeVisible();
  });

  test('should login student successfully', async ({ page }) => {
    await loginAsStudent(page);
    
    // Should be redirected to student dashboard
    await expect(page).toHaveURL('/siswa');
    await expect(page.locator('h1')).toContainText('Dashboard Siswa');
    await expect(page.locator(`text=${TEST_USERS.student.name}`)).toBeVisible();
  });

  test('should logout successfully', async ({ page }) => {
    await loginAsTeacher(page);
    await logout(page);
    
    // Should be redirected to login page
    await expect(page).toHaveURL('/login');
    await expect(page.locator('h1')).toContainText('Masuk ke GuruFlow');
  });

  test('should redirect unauthenticated users to login', async ({ page }) => {
    // Try to access protected route without authentication
    await page.goto('/guru');
    
    // Should be redirected to login
    await expect(page).toHaveURL('/login');
  });

  test('should prevent students from accessing teacher routes', async ({ page }) => {
    await loginAsStudent(page);
    
    // Try to access teacher route
    await page.goto('/guru');
    
    // Should be redirected or show error
    await expect(page).not.toHaveURL('/guru');
  });

  test('should prevent teachers from accessing student routes', async ({ page }) => {
    await loginAsTeacher(page);
    
    // Try to access student route
    await page.goto('/siswa');
    
    // Should be redirected or show error
    await expect(page).not.toHaveURL('/siswa');
  });

  test('should maintain session across page refreshes', async ({ page }) => {
    await loginAsTeacher(page);
    
    // Refresh the page
    await page.reload();
    
    // Should still be authenticated
    await expect(page).toHaveURL('/guru');
    await expect(page.locator(`text=${TEST_USERS.teacher.name}`)).toBeVisible();
  });

  test('should handle session expiration', async ({ page }) => {
    await loginAsTeacher(page);
    
    // Simulate session expiration by clearing cookies
    await page.context().clearCookies();
    
    // Try to access protected route
    await page.goto('/guru');
    
    // Should be redirected to login
    await expect(page).toHaveURL('/login');
  });
});