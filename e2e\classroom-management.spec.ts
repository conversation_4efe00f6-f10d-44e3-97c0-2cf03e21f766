import { test, expect } from '@playwright/test';
import { 
  loginAsTeacher, 
  navigateToClassrooms,
  createClassroom,
  expectToastMessage,
  waitForTableToLoad
} from './utils/test-helpers';

test.describe('Classroom Management', () => {
  test.beforeEach(async ({ page }) => {
    await loginAsTeacher(page);
    await navigateToClassrooms(page);
  });

  test('should display classroom management page', async ({ page }) => {
    await expect(page.locator('h1')).toContainText('Manajemen Kelas');
    await expect(page.locator('[data-testid="create-classroom-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="classroom-list"]')).toBeVisible();
  });

  test('should create new classroom', async ({ page }) => {
    const classroomData = {
      name: 'Kelas 6A Test',
      grade: '6',
      academicYear: '2024/2025',
    };

    await createClassroom(page, classroomData);
    
    // Should show success message
    await expectToastMessage(page, 'Kelas berhasil dibuat');
    
    // Should appear in classroom list
    await waitForTableToLoad(page);
    await expect(page.locator(`text=${classroomData.name}`)).toBeVisible();
  });

  test('should validate classroom form', async ({ page }) => {
    await page.click('[data-testid="create-classroom-button"]');
    
    // Try to save without filling required fields
    await page.click('[data-testid="save-classroom-button"]');
    
    // Should show validation errors
    await expect(page.locator('text=Nama kelas wajib diisi')).toBeVisible();
    await expect(page.locator('text=Tingkat wajib diisi')).toBeVisible();
  });

  test('should edit existing classroom', async ({ page }) => {
    // First create a classroom
    await createClassroom(page, {
      name: 'Kelas Test Edit',
      grade: '5',
      academicYear: '2024/2025',
    });

    await waitForTableToLoad(page);
    
    // Click edit button for the classroom
    await page.click('[data-testid="edit-classroom-1"]');
    
    // Update classroom name
    await page.fill('[data-testid="classroom-name-input"]', 'Kelas Test Edited');
    await page.click('[data-testid="save-classroom-button"]');
    
    // Should show success message
    await expectToastMessage(page, 'Kelas berhasil diperbarui');
    
    // Should show updated name
    await expect(page.locator('text=Kelas Test Edited')).toBeVisible();
  });

  test('should delete classroom', async ({ page }) => {
    // First create a classroom
    await createClassroom(page, {
      name: 'Kelas Test Delete',
      grade: '5',
      academicYear: '2024/2025',
    });

    await waitForTableToLoad(page);
    
    // Click delete button
    await page.click('[data-testid="delete-classroom-1"]');
    
    // Confirm deletion
    await page.click('[data-testid="confirm-delete"]');
    
    // Should show success message
    await expectToastMessage(page, 'Kelas berhasil dihapus');
    
    // Should not appear in list
    await expect(page.locator('text=Kelas Test Delete')).not.toBeVisible();
  });

  test('should view classroom details', async ({ page }) => {
    // Create a classroom first
    await createClassroom(page, {
      name: 'Kelas Detail Test',
      grade: '5',
      academicYear: '2024/2025',
    });

    await waitForTableToLoad(page);
    
    // Click on classroom name to view details
    await page.click('[data-testid="classroom-link-1"]');
    
    // Should navigate to classroom detail page
    await expect(page).toHaveURL(/\/guru\/kelas\/\d+/);
    await expect(page.locator('h1')).toContainText('Kelas Detail Test');
    
    // Should display classroom information
    await expect(page.locator('[data-testid="classroom-grade"]')).toContainText('5');
    await expect(page.locator('[data-testid="classroom-year"]')).toContainText('2024/2025');
  });

  test('should manage subjects in classroom', async ({ page }) => {
    // Create classroom and navigate to details
    await createClassroom(page, {
      name: 'Kelas Subject Test',
      grade: '5',
      academicYear: '2024/2025',
    });

    await waitForTableToLoad(page);
    await page.click('[data-testid="classroom-link-1"]');
    
    // Navigate to subjects tab
    await page.click('[data-testid="subjects-tab"]');
    
    // Add new subject
    await page.click('[data-testid="add-subject-button"]');
    await page.fill('[data-testid="subject-name-input"]', 'Matematika');
    await page.fill('[data-testid="subject-code-input"]', 'MTK');
    await page.click('[data-testid="save-subject-button"]');
    
    // Should show success message
    await expectToastMessage(page, 'Mata pelajaran berhasil ditambahkan');
    
    // Should appear in subjects list
    await expect(page.locator('text=Matematika')).toBeVisible();
    await expect(page.locator('text=MTK')).toBeVisible();
  });

  test('should manage students in classroom', async ({ page }) => {
    // Create classroom and navigate to details
    await createClassroom(page, {
      name: 'Kelas Student Test',
      grade: '5',
      academicYear: '2024/2025',
    });

    await waitForTableToLoad(page);
    await page.click('[data-testid="classroom-link-1"]');
    
    // Navigate to students tab
    await page.click('[data-testid="students-tab"]');
    
    // Add new student
    await page.click('[data-testid="add-student-button"]');
    await page.fill('[data-testid="student-name-input"]', 'Test Student');
    await page.fill('[data-testid="student-email-input"]', '<EMAIL>');
    await page.fill('[data-testid="student-id-input"]', 'NIS123');
    await page.click('[data-testid="save-student-button"]');
    
    // Should show success message
    await expectToastMessage(page, 'Siswa berhasil ditambahkan');
    
    // Should appear in students list
    await expect(page.locator('text=Test Student')).toBeVisible();
    await expect(page.locator('text=NIS123')).toBeVisible();
  });

  test('should search and filter classrooms', async ({ page }) => {
    // Create multiple classrooms
    await createClassroom(page, {
      name: 'Kelas 5A',
      grade: '5',
      academicYear: '2024/2025',
    });

    await createClassroom(page, {
      name: 'Kelas 6B',
      grade: '6',
      academicYear: '2024/2025',
    });

    await waitForTableToLoad(page);
    
    // Search for specific classroom
    await page.fill('[data-testid="search-input"]', '5A');
    
    // Should show only matching classroom
    await expect(page.locator('text=Kelas 5A')).toBeVisible();
    await expect(page.locator('text=Kelas 6B')).not.toBeVisible();
    
    // Clear search
    await page.fill('[data-testid="search-input"]', '');
    
    // Filter by grade
    await page.click('[data-testid="grade-filter"]');
    await page.click('[data-testid="grade-option-5"]');
    
    // Should show only grade 5 classrooms
    await expect(page.locator('text=Kelas 5A')).toBeVisible();
    await expect(page.locator('text=Kelas 6B')).not.toBeVisible();
  });

  test('should handle bulk operations', async ({ page }) => {
    // Create multiple classrooms
    await createClassroom(page, {
      name: 'Bulk Test 1',
      grade: '5',
      academicYear: '2024/2025',
    });

    await createClassroom(page, {
      name: 'Bulk Test 2',
      grade: '5',
      academicYear: '2024/2025',
    });

    await waitForTableToLoad(page);
    
    // Select multiple classrooms
    await page.check('[data-testid="select-classroom-1"]');
    await page.check('[data-testid="select-classroom-2"]');
    
    // Bulk delete
    await page.click('[data-testid="bulk-delete-button"]');
    await page.click('[data-testid="confirm-bulk-delete"]');
    
    // Should show success message
    await expectToastMessage(page, 'Kelas berhasil dihapus');
    
    // Should not appear in list
    await expect(page.locator('text=Bulk Test 1')).not.toBeVisible();
    await expect(page.locator('text=Bulk Test 2')).not.toBeVisible();
  });

  test('should export classroom data', async ({ page }) => {
    // Create a classroom
    await createClassroom(page, {
      name: 'Export Test',
      grade: '5',
      academicYear: '2024/2025',
    });

    await waitForTableToLoad(page);
    
    // Export as CSV
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="export-csv-button"]');
    const download = await downloadPromise;
    
    expect(download.suggestedFilename()).toMatch(/kelas.*\.csv$/);
  });

  test('should work responsively on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Should display mobile-optimized layout
    await expect(page.locator('[data-testid="mobile-classroom-list"]')).toBeVisible();
    
    // Create classroom on mobile
    await page.tap('[data-testid="mobile-create-button"]');
    
    // Mobile form should be displayed
    await expect(page.locator('[data-testid="mobile-classroom-form"]')).toBeVisible();
    
    // Fill and submit form
    await page.fill('[data-testid="classroom-name-input"]', 'Mobile Test');
    await page.fill('[data-testid="classroom-grade-input"]', '5');
    await page.fill('[data-testid="classroom-year-input"]', '2024/2025');
    await page.tap('[data-testid="save-classroom-button"]');
    
    // Should show success message
    await expectToastMessage(page, 'Kelas berhasil dibuat');
  });
});