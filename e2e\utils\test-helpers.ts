import { Page, expect } from '@playwright/test';

// Test user credentials
export const TEST_USERS = {
  teacher: {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test Teacher',
    role: 'teacher',
  },
  student: {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test Student',
    role: 'student',
  },
};

// Authentication helpers
export async function loginAsTeacher(page: Page) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', TEST_USERS.teacher.email);
  await page.fill('[data-testid="password-input"]', TEST_USERS.teacher.password);
  await page.click('[data-testid="login-button"]');
  
  // Wait for redirect to teacher dashboard
  await page.waitForURL('/guru');
  await expect(page.locator('h1')).toContainText('Dashboard Guru');
}

export async function loginAsStudent(page: Page) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', TEST_USERS.student.email);
  await page.fill('[data-testid="password-input"]', TEST_USERS.student.password);
  await page.click('[data-testid="login-button"]');
  
  // Wait for redirect to student dashboard
  await page.waitForURL('/siswa');
  await expect(page.locator('h1')).toContainText('Dashboard Siswa');
}

export async function logout(page: Page) {
  await page.click('[data-testid="logout-button"]');
  await page.waitForURL('/login');
}

// Navigation helpers
export async function navigateToAttendance(page: Page) {
  await page.click('[data-testid="nav-attendance"]');
  await page.waitForURL('/guru/absensi');
}

export async function navigateToClassrooms(page: Page) {
  await page.click('[data-testid="nav-classrooms"]');
  await page.waitForURL('/guru/kelas');
}

export async function navigateToStudents(page: Page) {
  await page.click('[data-testid="nav-students"]');
  await page.waitForURL('/guru/siswa');
}

// Form helpers
export async function fillAttendanceForm(page: Page, attendanceData: Record<string, string>) {
  for (const [studentId, status] of Object.entries(attendanceData)) {
    await page.click(`[data-testid="attendance-${studentId}-${status}"]`);
  }
}

export async function createClassroom(page: Page, classroomData: {
  name: string;
  grade: string;
  academicYear: string;
}) {
  await page.click('[data-testid="create-classroom-button"]');
  await page.fill('[data-testid="classroom-name-input"]', classroomData.name);
  await page.fill('[data-testid="classroom-grade-input"]', classroomData.grade);
  await page.fill('[data-testid="classroom-year-input"]', classroomData.academicYear);
  await page.click('[data-testid="save-classroom-button"]');
}

export async function createStudent(page: Page, studentData: {
  name: string;
  email: string;
  studentId: string;
}) {
  await page.click('[data-testid="create-student-button"]');
  await page.fill('[data-testid="student-name-input"]', studentData.name);
  await page.fill('[data-testid="student-email-input"]', studentData.email);
  await page.fill('[data-testid="student-id-input"]', studentData.studentId);
  await page.click('[data-testid="save-student-button"]');
}

// Assertion helpers
export async function expectToastMessage(page: Page, message: string) {
  await expect(page.locator('[data-testid="toast"]')).toContainText(message);
}

export async function expectErrorMessage(page: Page, message: string) {
  await expect(page.locator('[data-testid="error-message"]')).toContainText(message);
}

export async function expectSuccessMessage(page: Page, message: string) {
  await expect(page.locator('[data-testid="success-message"]')).toContainText(message);
}

// Wait helpers
export async function waitForLoadingToFinish(page: Page) {
  await page.waitForSelector('[data-testid="loading"]', { state: 'hidden' });
}

export async function waitForTableToLoad(page: Page) {
  await page.waitForSelector('[data-testid="data-table"]');
  await waitForLoadingToFinish(page);
}

// Mobile helpers
export async function openMobileMenu(page: Page) {
  await page.click('[data-testid="mobile-menu-button"]');
}

export async function closeMobileMenu(page: Page) {
  await page.click('[data-testid="mobile-menu-close"]');
}

// Database helpers (for test setup/teardown)
export async function setupTestData(page: Page) {
  // This would typically call API endpoints to set up test data
  // For now, we'll use the UI to create necessary test data
  
  // Create test classroom
  await navigateToClassrooms(page);
  await createClassroom(page, {
    name: 'Test Kelas 5A',
    grade: '5',
    academicYear: '2024/2025',
  });
  
  // Create test students
  await navigateToStudents(page);
  await createStudent(page, {
    name: 'Test Student 1',
    email: '<EMAIL>',
    studentId: 'NIS001',
  });
  
  await createStudent(page, {
    name: 'Test Student 2',
    email: '<EMAIL>',
    studentId: 'NIS002',
  });
}

export async function cleanupTestData(page: Page) {
  // This would typically call API endpoints to clean up test data
  // For E2E tests, we might want to reset the database to a clean state
}

// Screenshot helpers
export async function takeScreenshot(page: Page, name: string) {
  await page.screenshot({ path: `e2e/screenshots/${name}.png`, fullPage: true });
}

// Performance helpers
export async function measurePageLoadTime(page: Page, url: string) {
  const startTime = Date.now();
  await page.goto(url);
  await page.waitForLoadState('networkidle');
  const endTime = Date.now();
  return endTime - startTime;
}

// Accessibility helpers
export async function checkAccessibility(page: Page) {
  // This would integrate with axe-core or similar accessibility testing tools
  // For now, we'll do basic checks
  
  // Check for proper heading structure
  const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
  expect(headings.length).toBeGreaterThan(0);
  
  // Check for alt text on images
  const images = await page.locator('img').all();
  for (const img of images) {
    const alt = await img.getAttribute('alt');
    expect(alt).toBeTruthy();
  }
  
  // Check for form labels
  const inputs = await page.locator('input[type="text"], input[type="email"], input[type="password"]').all();
  for (const input of inputs) {
    const id = await input.getAttribute('id');
    if (id) {
      const label = page.locator(`label[for="${id}"]`);
      await expect(label).toBeVisible();
    }
  }
}

// Cross-browser compatibility helpers
export function getBrowserName(page: Page): string {
  return page.context().browser()?.browserType().name() || 'unknown';
}

export async function skipIfBrowser(page: Page, browserName: string, reason: string) {
  const currentBrowser = getBrowserName(page);
  if (currentBrowser === browserName) {
    console.log(`Skipping test on ${browserName}: ${reason}`);
    return true;
  }
  return false;
}