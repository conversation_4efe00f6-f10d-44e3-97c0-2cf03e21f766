# GuruFlow - Implementation Plan

## 🚧 Step-by-Step Action Plan

### Phase 1 – MVP (Core Platform)
1. **Design system & components** using Tailwind CSS and shadcn/ui
2. **Set up custom auth** with email/password and magic link option
3. **Build onboarding wizard** for school setup and teacher superadmin
4. **Implement teacher dashboard** with teaching schedule, shortcuts, and XP view
5. **Create student dashboard** showing tasks, progress, and gamification stats
6. **Develop classroom & student management** (CRUD, enroll via CSV)
7. **Launch attendance module** with daily input and PDF/CSV export
8. **Deploy initial platform** on Vercel + Railway + Turso Cloud

### Phase 2 – V1 (Expanded Functionality)
1. **Assignments & assessments** with task creation, submissions, grading rubrics
2. **Grades & report card system** (formative, summative, predikat, KKM global)
3. **Gamification engine** (XP, badges, leaderboard)
4. **Reports module** (nilai, absensi, XP) with export tools

### Phase 3 – V2 (AI & Advanced Tools)
1. **AI-powered bank soal** (prompt templates, review UI, publishing)
2. **Teacher journal** module (per-meeting notes)
3. **PDF rendering optimization** (react-pdf → Puppeteer if needed)

## ⏳ Timeline Estimation
- **MVP:** 4–6 weeks
- **V1:** +4 weeks
- **V2:** +3 weeks

## 👥 Team Recommendations
- **1 Frontend Dev** (Next.js + Tailwind)
- **1 Fullstack/Backend Dev** (Turso, Drizzle ORM, PDF rendering)
- **Optional:** 1 UX Designer or PM for flow mapping and onboarding UX

## 🧩 Optional Tasks or Integrations
- Notification service (email alerts, WhatsApp, push)
- Admin analytics dashboard
- Parent account view-only layer (future expansion)
- Mobile PWA wrapper for native-like experience
