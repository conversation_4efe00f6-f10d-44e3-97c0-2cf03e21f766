# GuruFlow - Masterplan

## 🧭 App Overview and Objectives
GuruFlow is a modern school administration web platform designed to simplify and digitize daily classroom operations. It helps teachers manage teaching schedules, attendance, assignments, and assessments, while empowering students to track their progress, view feedback, and stay motivated through gamification.

## 🎯 Target Audience
- **Teachers (Guru):** Both homeroom and subject teachers managing classroom tasks.
- **Students (Siswa):** Elementary to high school students accessing their academic dashboard.
- **Note:** Only two user roles exist: Teacher and Student.

## 🧩 Core Features and Functionality
- **Landing Page** with login and role-based redirection.
- **Onboarding Wizard** for initial school setup and first teacher (superadmin).
- **Teacher Dashboard** with teaching schedule, shortcuts to key actions, and XP leaderboard.
- **Student Dashboard** with active tasks, grades, and gamification stats.
- **Classroom Management:** CRUD for classes, subjects, and students.
- **Attendance Module:** Daily input (H/I/S/A), class-level recaps, exportable.
- **Assignment & Assessment:** Task creation, submissions, rubrik-based grading.
- **Grades & Report Cards:** Formative/summative scores, global KKM, and exportable report cards.
- **Teacher Journal:** Logged meeting notes per session.
- **AI-Powered Question Bank:** Generate, review, and publish question sets.
- **Gamification Engine:** XP, badges, and leaderboard.
- **Reports Module:** Recap of attendance, scores, and XP with export options.

## 🛠️ High-Level Technical Stack
- **Frontend:** Next.js 15 + TypeScript + Tailwind CSS v4 + shadcn/ui
- **Backend:** Turso/libSQL with Drizzle ORM
- **Auth:** Custom auth (email/password) with optional magic link
- **AI Integration:** OpenAI-compatible API for generating questions
- **Storage:** S3-compatible (Cloudflare R2 or MinIO)
- **PDF Generation:** react-pdf or Puppeteer
- **Deployment:** Vercel (frontend), Railway/Fly.io (backend), Turso Cloud (database)

## 🧱 Conceptual Data Model (Simplified)
- `School` (singleton)
- `Classroom` → has many `Students`, many `Subjects`
- `Teacher` → teaches many `Subjects`, owns `Assignments`
- `Student` → belongs to `Classroom`, has `Submissions`, `XP`, `Badges`
- `Assignment` → belongs to `Subject`, created by `Teacher`
- `Submission` → linked to `Assignment` and `Student`, contains score and feedback
- `AttendanceRecord` → linked to `Student`, marked daily by `Teacher`
- `Gradebook` → summarizes performance by subject and student
- `QuestionBank` → AI-generated, manually reviewed, linked to `Subjects`

## 🎨 User Interface Design Principles
- Clean, minimal, mobile-first layouts
- Clear navigation and action-focused dashboards
- Gamification visuals (badges, XP bars, leaderboard)
- Accessible color contrast and font sizing
- Consistent use of Inter font and school-themed color palette

## 🔐 Security Considerations
- Role-based access: Teacher vs. Student
- Magic link as optional secure login method
- Secure storage for student data and submissions
- Permission control for CRUD actions (e.g., only Teachers can grade or export)

## 🚀 Development Phases
1. **MVP:** Landing, onboarding, dashboards, attendance, assignment
2. **V1:** Grades & report cards, gamification, reports
3. **V2:** AI-powered bank soal, teacher journals

## ⚠️ Potential Challenges and Solutions
- **Complex class data structures:** Use relational schema with normalized tables
- **PDF exports & rendering:** Use react-pdf initially, consider Puppeteer for advanced layouts
- **Performance on mobile:** Optimize components and data fetching for mobile-first use

## 🔮 Future Expansion
- Parent accounts with read-only access
- Multi-school SaaS model
- Notifications and mobile push
- Integration with WhatsApp or SMS alerts
