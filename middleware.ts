import { NextRequest, NextResponse } from 'next/server';

// Define protected routes
const protectedRoutes = ['/guru', '/siswa'];
const authRoutes = ['/login'];
const onboardingRoute = '/onboarding';
const publicRoutes = ['/', '/api'];

export async function middleware(request: NextRequest) {
  try {
    const { pathname } = request.nextUrl;
    
    // Skip middleware for static files and most API routes
    if (
      pathname.startsWith('/_next') ||
      pathname.startsWith('/favicon.ico') ||
      (pathname.startsWith('/api') && 
       pathname !== '/api/auth/redirect' && 
       pathname !== '/api/onboarding')
    ) {
      return NextResponse.next();
    }
    
    // Check onboarding status for non-API routes
    if (!pathname.startsWith('/api')) {
      try {
        // Check if system is onboarded by making a request to our API
        const onboardingCheckUrl = new URL('/api/onboarding', request.url);
        const onboardingResponse = await fetch(onboardingCheckUrl.toString());
        
        if (onboardingResponse.ok) {
          const onboardingData = await onboardingResponse.json();
          const isOnboarded = onboardingData.data?.isOnboarded || false;
          
          // If not onboarded and not on onboarding page, redirect to onboarding
          if (!isOnboarded && pathname !== onboardingRoute) {
            const onboardingUrl = new URL(onboardingRoute, request.url);
            return NextResponse.redirect(onboardingUrl);
          }
          
          // If onboarded and on onboarding page, redirect to login
          if (isOnboarded && pathname === onboardingRoute) {
            const loginUrl = new URL('/login', request.url);
            return NextResponse.redirect(loginUrl);
          }
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        // If error checking onboarding, continue with normal flow
      }
    }
    
    // Get session cookie
    const sessionCookie = request.cookies.get('guruflow-session');
    const isAuthenticated = !!sessionCookie?.value;
    
    // Check if the current path is protected
    const isProtectedRoute = protectedRoutes.some(route => 
      pathname.startsWith(route)
    );
    
    // Check if the current path is an auth route
    const isAuthRoute = authRoutes.some(route => 
      pathname.startsWith(route)
    );
    
    // If accessing protected route without authentication
    if (isProtectedRoute && !isAuthenticated) {
      const loginUrl = new URL('/login', request.url);
      return NextResponse.redirect(loginUrl);
    }
    
    // If accessing auth routes while authenticated, redirect to appropriate dashboard
    if (isAuthRoute && isAuthenticated && pathname !== '/api/auth/redirect') {
      const dashboardUrl = new URL('/api/auth/redirect', request.url);
      return NextResponse.redirect(dashboardUrl);
    }
    
    return NextResponse.next();
  } catch (error) {
    console.error('Middleware error:', error);
    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};