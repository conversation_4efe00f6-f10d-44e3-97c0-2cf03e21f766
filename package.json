{"name": "guruflow-mvp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write .", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:seed": "tsx scripts/generate-migration.ts seed", "db:setup": "tsx src/lib/database-utils.ts", "db:create-test-user": "tsx scripts/create-test-user.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@libsql/client": "^0.4.0", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@react-pdf/renderer": "^4.3.0", "@tailwindcss/line-clamp": "^0.4.4", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^3.0.6", "dotenv": "^17.2.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.4", "lucide-react": "^0.539.0", "next": "^15.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.2.0", "typescript": "^5.4.2", "zod": "^3.22.4"}, "devDependencies": {"@playwright/test": "^1.54.2", "@tailwindcss/postcss": "^4.0.0-alpha.4", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^3.0.0", "@types/jest": "^30.0.0", "@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "^15.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "tailwindcss": "^4.0.0-alpha.4", "tsx": "^4.6.2"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}