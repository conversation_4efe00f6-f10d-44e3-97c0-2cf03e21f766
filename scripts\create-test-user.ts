import { config } from 'dotenv';
config({ path: '.env.local' });

import { db } from '../src/lib/db';
import { schools, users } from '../src/db/schema';
import { hashPassword } from '../src/lib/auth';
import { eq } from 'drizzle-orm';

async function createTestUser() {
  try {
    console.log('🔧 Creating test user...');

    // First, create a school if it doesn't exist
    const existingSchool = await db.select().from(schools).limit(1);
    
    if (existingSchool.length === 0) {
      await db.insert(schools).values({
        name: 'SD Test School',
        level: 'SD',
        logo: null,
        signature: null,
      });
      console.log('✅ Test school created');
    }

    // Check if test teacher already exists
    const existingTeacher = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);
    
    if (existingTeacher.length === 0) {
      const hashedPassword = await hashPassword('password123');
      
      await db.insert(users).values({
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        role: 'teacher',
        name: 'Guru Test',
        isActive: true,
        isSuperAdmin: true,
      });
      console.log('✅ Test teacher created: <EMAIL> / password123');
    } else {
      console.log('ℹ️  Test teacher already exists: <EMAIL> / password123');
    }

    // Check if test student already exists
    const existingStudent = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);
    
    if (existingStudent.length === 0) {
      const hashedPassword = await hashPassword('password123');
      
      await db.insert(users).values({
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        role: 'student',
        name: 'Siswa Test',
        isActive: true,
        isSuperAdmin: false,
      });
      console.log('✅ Test student created: <EMAIL> / password123');
    } else {
      console.log('ℹ️  Test student already exists: <EMAIL> / password123');
    }

    console.log('🎉 Test users setup completed!');
    console.log('');
    console.log('Login credentials:');
    console.log('Teacher: <EMAIL> / password123');
    console.log('Student: <EMAIL> / password123');

  } catch (error) {
    console.error('❌ Failed to create test user:', error);
    process.exit(1);
  }
}

createTestUser().then(() => {
  process.exit(0);
});