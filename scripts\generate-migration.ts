#!/usr/bin/env tsx

/**
 * Migration generation script
 * This script generates database migrations using Drizzle Kit
 * 
 * Usage:
 * - npm run db:generate (generates migration based on schema changes)
 * - npm run db:migrate (applies migrations to database)
 * - npm run db:push (pushes schema directly to database - development only)
 */

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function generateMigration() {
  try {
    console.log('🔄 Generating database migration...');
    
    // Generate migration using drizzle-kit
    const { stdout, stderr } = await execAsync('npx drizzle-kit generate:sqlite');
    
    if (stderr) {
      console.error('Migration generation warnings:', stderr);
    }
    
    console.log('Migration output:', stdout);
    console.log('✅ Migration generated successfully');
    
  } catch (error) {
    console.error('❌ Migration generation failed:', error);
    process.exit(1);
  }
}

async function applyMigrations() {
  try {
    console.log('🔄 Applying database migrations...');
    
    // Apply migrations using our migration utility
    const { runMigrations } = await import('../src/lib/migrate');
    await runMigrations();
    
    console.log('✅ Migrations applied successfully');
    
  } catch (error) {
    console.error('❌ Migration application failed:', error);
    process.exit(1);
  }
}

async function pushSchema() {
  try {
    console.log('🔄 Pushing schema to database...');
    
    // Push schema directly (development only)
    const { stdout, stderr } = await execAsync('npx drizzle-kit push:sqlite');
    
    if (stderr) {
      console.error('Schema push warnings:', stderr);
    }
    
    console.log('Schema push output:', stdout);
    console.log('✅ Schema pushed successfully');
    
  } catch (error) {
    console.error('❌ Schema push failed:', error);
    process.exit(1);
  }
}

// Handle command line arguments
const command = process.argv[2];

switch (command) {
  case 'generate':
    generateMigration();
    break;
  case 'migrate':
    applyMigrations();
    break;
  case 'push':
    pushSchema();
    break;
  default:
    console.log('Usage: tsx scripts/generate-migration.ts [generate|migrate|push]');
    process.exit(1);
}