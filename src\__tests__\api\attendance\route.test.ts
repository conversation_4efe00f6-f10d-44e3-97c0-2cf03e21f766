import { GET, POST } from '@/app/api/attendance/route'
import { NextRequest } from 'next/server'
import { generateMockAttendance, generateMockStudents } from '@/__tests__/utils/test-utils'

// Mock the auth module
jest.mock('@/lib/auth', () => ({
  getCurrentUser: jest.fn(),
}))

// Mock the database
jest.mock('@/lib/db', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    innerJoin: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    transaction: jest.fn(),
  },
}))

describe('/api/attendance', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET', () => {
    it('should get attendance records for a specific date', async () => {
      const { getCurrentUser } = require('@/lib/auth')
      const { db } = require('@/lib/db')

      // Mock authenticated teacher
      getCurrentUser.mockResolvedValue({
        id: 1,
        role: 'teacher',
        name: 'Test Teacher',
      })

      // Mock classroom subject lookup
      db.select.mockResolvedValueOnce([{
        id: 1,
        classroomId: 1,
        subjectId: 1,
      }])

      // Mock attendance records
      const mockAttendance = generateMockAttendance([1, 2, 3], '2024-12-16')
      db.select.mockResolvedValueOnce(mockAttendance.map(record => ({
        ...record,
        studentName: `Student ${record.studentId}`,
        studentNumber: `NIS${String(record.studentId).padStart(3, '0')}`,
      })))

      const url = new URL('http://localhost/api/attendance')
      url.searchParams.set('classroomId', '1')
      url.searchParams.set('subjectId', '1')
      url.searchParams.set('date', '2024-12-16')

      const request = new NextRequest(url)
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(3)
      expect(data.data[0]).toHaveProperty('studentName')
      expect(data.data[0]).toHaveProperty('status')
    })

    it('should require teacher authentication', async () => {
      const { getCurrentUser } = require('@/lib/auth')

      // Mock unauthenticated user
      getCurrentUser.mockResolvedValue(null)

      const url = new URL('http://localhost/api/attendance')
      url.searchParams.set('classroomId', '1')
      url.searchParams.set('subjectId', '1')
      url.searchParams.set('date', '2024-12-16')

      const request = new NextRequest(url)
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.error.code).toBe('UNAUTHORIZED')
    })

    it('should validate required parameters', async () => {
      const { getCurrentUser } = require('@/lib/auth')

      getCurrentUser.mockResolvedValue({
        id: 1,
        role: 'teacher',
      })

      const url = new URL('http://localhost/api/attendance')
      // Missing required parameters

      const request = new NextRequest(url)
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error.code).toBe('MISSING_PARAMS')
    })
  })

  describe('POST', () => {
    it('should save attendance records', async () => {
      const { getCurrentUser } = require('@/lib/auth')
      const { db } = require('@/lib/db')

      // Mock authenticated teacher
      getCurrentUser.mockResolvedValue({
        id: 1,
        role: 'teacher',
      })

      // Mock teacher lookup
      db.select.mockResolvedValueOnce([{ id: 1 }])

      // Mock classroom subject lookup
      db.select.mockResolvedValueOnce([{
        id: 1,
        classroomId: 1,
        subjectId: 1,
      }])

      // Mock transaction
      db.transaction.mockImplementation(async (callback) => {
        const mockTx = {
          delete: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          insert: jest.fn().mockReturnThis(),
          values: jest.fn().mockResolvedValue([]),
        }
        return callback(mockTx)
      })

      const attendanceRecords = [
        { studentId: 1, status: 'H', notes: null },
        { studentId: 2, status: 'I', notes: 'Sakit demam' },
        { studentId: 3, status: 'A', notes: null },
      ]

      const request = new NextRequest('http://localhost/api/attendance', {
        method: 'POST',
        body: JSON.stringify({
          classroomId: 1,
          subjectId: 1,
          date: '2024-12-16',
          records: attendanceRecords,
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toBe('Absensi berhasil disimpan')
      expect(db.transaction).toHaveBeenCalled()
    })

    it('should validate attendance records', async () => {
      const { getCurrentUser } = require('@/lib/auth')

      getCurrentUser.mockResolvedValue({
        id: 1,
        role: 'teacher',
      })

      const request = new NextRequest('http://localhost/api/attendance', {
        method: 'POST',
        body: JSON.stringify({
          classroomId: 1,
          subjectId: 1,
          date: '2024-12-16',
          records: 'invalid-records', // Should be array
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error.code).toBe('INVALID_DATA')
    })

    it('should handle database transaction errors', async () => {
      const { getCurrentUser } = require('@/lib/auth')
      const { db } = require('@/lib/db')

      getCurrentUser.mockResolvedValue({
        id: 1,
        role: 'teacher',
      })

      // Mock teacher lookup
      db.select.mockResolvedValueOnce([{ id: 1 }])

      // Mock classroom subject lookup
      db.select.mockResolvedValueOnce([{
        id: 1,
        classroomId: 1,
        subjectId: 1,
      }])

      // Mock transaction error
      db.transaction.mockRejectedValue(new Error('Transaction failed'))

      const request = new NextRequest('http://localhost/api/attendance', {
        method: 'POST',
        body: JSON.stringify({
          classroomId: 1,
          subjectId: 1,
          date: '2024-12-16',
          records: [{ studentId: 1, status: 'H' }],
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error.code).toBe('INTERNAL_ERROR')
    })
  })
})