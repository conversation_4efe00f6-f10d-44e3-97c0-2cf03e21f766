import { POST } from '@/app/api/auth/login/route'
import { NextRequest } from 'next/server'
import { mockApiResponse, mockFetch } from '@/__tests__/utils/test-utils'

// Mock the auth module
jest.mock('@/lib/auth', () => ({
  verifyPassword: jest.fn(),
  createSession: jest.fn(),
}))

// Mock the database
jest.mock('@/lib/db', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
  },
}))

describe('/api/auth/login', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should login with valid credentials', async () => {
    const { verifyPassword, createSession } = require('@/lib/auth')
    const { db } = require('@/lib/db')

    // Mock database response
    db.select.mockResolvedValue([{
      id: 1,
      email: '<EMAIL>',
      passwordHash: 'hashed-password',
      name: 'Test Teacher',
      role: 'teacher',
      isActive: true,
    }])

    // Mock password verification
    verifyPassword.mockResolvedValue(true)
    
    // Mock session creation
    createSession.mockResolvedValue('session-123')

    const request = new NextRequest('http://localhost/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
      }),
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.success).toBe(true)
    expect(data.data.user.email).toBe('<EMAIL>')
    expect(data.data.user.role).toBe('teacher')
    expect(verifyPassword).toHaveBeenCalledWith('password123', 'hashed-password')
    expect(createSession).toHaveBeenCalledWith(1)
  })

  it('should reject invalid email', async () => {
    const { db } = require('@/lib/db')

    // Mock database response - no user found
    db.select.mockResolvedValue([])

    const request = new NextRequest('http://localhost/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
      }),
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(401)
    expect(data.success).toBe(false)
    expect(data.error.code).toBe('INVALID_CREDENTIALS')
  })

  it('should reject invalid password', async () => {
    const { verifyPassword } = require('@/lib/auth')
    const { db } = require('@/lib/db')

    // Mock database response
    db.select.mockResolvedValue([{
      id: 1,
      email: '<EMAIL>',
      passwordHash: 'hashed-password',
      name: 'Test Teacher',
      role: 'teacher',
      isActive: true,
    }])

    // Mock password verification failure
    verifyPassword.mockResolvedValue(false)

    const request = new NextRequest('http://localhost/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'wrongpassword',
      }),
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(401)
    expect(data.success).toBe(false)
    expect(data.error.code).toBe('INVALID_CREDENTIALS')
  })

  it('should reject inactive user', async () => {
    const { verifyPassword } = require('@/lib/auth')
    const { db } = require('@/lib/db')

    // Mock database response - inactive user
    db.select.mockResolvedValue([{
      id: 1,
      email: '<EMAIL>',
      passwordHash: 'hashed-password',
      name: 'Test Teacher',
      role: 'teacher',
      isActive: false,
    }])

    verifyPassword.mockResolvedValue(true)

    const request = new NextRequest('http://localhost/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
      }),
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(401)
    expect(data.success).toBe(false)
    expect(data.error.code).toBe('ACCOUNT_INACTIVE')
  })

  it('should validate request body', async () => {
    const request = new NextRequest('http://localhost/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: 'invalid-email',
        password: '',
      }),
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.success).toBe(false)
    expect(data.error.code).toBe('VALIDATION_ERROR')
  })

  it('should handle database errors', async () => {
    const { db } = require('@/lib/db')

    // Mock database error
    db.select.mockRejectedValue(new Error('Database connection failed'))

    const request = new NextRequest('http://localhost/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
      }),
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(500)
    expect(data.success).toBe(false)
    expect(data.error.code).toBe('INTERNAL_ERROR')
  })
})