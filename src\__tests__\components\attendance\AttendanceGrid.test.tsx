import { render, screen, fireEvent, waitFor } from '@/__tests__/utils/test-utils'
import { AttendanceGrid } from '@/components/attendance/AttendanceGrid'
import { generateMockStudents, mockFetch } from '@/__tests__/utils/test-utils'

describe('AttendanceGrid', () => {
  const mockStudents = generateMockStudents(3)
  const mockProps = {
    students: mockStudents,
    classroomId: 1,
    subjectId: 1,
    date: '2024-12-16',
    onSave: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render students list', () => {
    render(<AttendanceGrid {...mockProps} />)
    
    mockStudents.forEach(student => {
      expect(screen.getByText(student.name)).toBeInTheDocument()
      expect(screen.getByText(`NIS: ${student.studentId}`)).toBeInTheDocument()
    })
  })

  it('should render attendance status buttons', () => {
    render(<AttendanceGrid {...mockProps} />)
    
    // Should have H, I, S, A buttons for each student
    const hadirButtons = screen.getAllByText('H')
    const izinButtons = screen.getAllByText('I')
    const sakitButtons = screen.getAllByText('S')
    const alpaButtons = screen.getAllByText('A')
    
    expect(hadirButtons).toHaveLength(mockStudents.length)
    expect(izinButtons).toHaveLength(mockStudents.length)
    expect(sakitButtons).toHaveLength(mockStudents.length)
    expect(alpaButtons).toHaveLength(mockStudents.length)
  })

  it('should select attendance status', () => {
    render(<AttendanceGrid {...mockProps} />)
    
    // Click on 'I' (Izin) button for first student
    const izinButtons = screen.getAllByText('I')
    fireEvent.click(izinButtons[0])
    
    // Button should be selected (have different styling)
    expect(izinButtons[0].closest('button')).toHaveClass('bg-yellow-500')
  })

  it('should save attendance data', async () => {
    mockFetch({
      success: true,
      message: 'Absensi berhasil disimpan',
    })

    render(<AttendanceGrid {...mockProps} />)
    
    // Set attendance for students
    const hadirButtons = screen.getAllByText('H')
    const izinButtons = screen.getAllByText('I')
    
    fireEvent.click(hadirButtons[0]) // Student 1: Hadir
    fireEvent.click(izinButtons[1])  // Student 2: Izin
    
    // Click save button
    const saveButton = screen.getByText(/simpan absensi/i)
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/attendance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          classroomId: 1,
          subjectId: 1,
          date: '2024-12-16',
          records: expect.arrayContaining([
            expect.objectContaining({
              studentId: mockStudents[0].id,
              status: 'H',
            }),
            expect.objectContaining({
              studentId: mockStudents[1].id,
              status: 'I',
            }),
          ]),
        }),
      })
    })

    expect(mockProps.onSave).toHaveBeenCalled()
  })

  it('should handle save errors', async () => {
    mockFetch({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Gagal menyimpan absensi',
      },
    }, false)

    render(<AttendanceGrid {...mockProps} />)
    
    // Set attendance and save
    const hadirButtons = screen.getAllByText('H')
    fireEvent.click(hadirButtons[0])
    
    const saveButton = screen.getByText(/simpan absensi/i)
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(screen.getByText(/gagal menyimpan absensi/i)).toBeInTheDocument()
    })
  })

  it('should show loading state during save', async () => {
    // Mock delayed response
    const mockResponse = new Promise(resolve => 
      setTimeout(() => resolve({
        ok: true,
        json: () => Promise.resolve({ success: true }),
      }), 100)
    )
    ;(global.fetch as jest.Mock).mockReturnValue(mockResponse)

    render(<AttendanceGrid {...mockProps} />)
    
    const hadirButtons = screen.getAllByText('H')
    fireEvent.click(hadirButtons[0])
    
    const saveButton = screen.getByText(/simpan absensi/i)
    fireEvent.click(saveButton)

    // Should show loading state
    expect(screen.getByText(/menyimpan/i)).toBeInTheDocument()
    expect(saveButton).toBeDisabled()

    // Wait for completion
    await waitFor(() => {
      expect(screen.getByText(/simpan absensi/i)).toBeInTheDocument()
    }, { timeout: 200 })
  })

  it('should load existing attendance data', () => {
    const existingAttendance = {
      [mockStudents[0].id]: { status: 'H', notes: null },
      [mockStudents[1].id]: { status: 'I', notes: 'Sakit demam' },
      [mockStudents[2].id]: { status: 'A', notes: null },
    }

    render(
      <AttendanceGrid 
        {...mockProps} 
        initialAttendance={existingAttendance}
      />
    )
    
    // Check that buttons are pre-selected
    const hadirButtons = screen.getAllByText('H')
    const izinButtons = screen.getAllByText('I')
    const alpaButtons = screen.getAllByText('A')
    
    expect(hadirButtons[0].closest('button')).toHaveClass('bg-green-500')
    expect(izinButtons[1].closest('button')).toHaveClass('bg-yellow-500')
    expect(alpaButtons[2].closest('button')).toHaveClass('bg-gray-500')
  })

  it('should handle bulk attendance marking', () => {
    render(<AttendanceGrid {...mockProps} />)
    
    // Should have bulk action buttons
    expect(screen.getByText(/semua hadir/i)).toBeInTheDocument()
    expect(screen.getByText(/reset/i)).toBeInTheDocument()
    
    // Click "Semua Hadir"
    const semuaHadirButton = screen.getByText(/semua hadir/i)
    fireEvent.click(semuaHadirButton)
    
    // All H buttons should be selected
    const hadirButtons = screen.getAllByText('H')
    hadirButtons.forEach(button => {
      expect(button.closest('button')).toHaveClass('bg-green-500')
    })
  })

  it('should reset attendance', () => {
    render(<AttendanceGrid {...mockProps} />)
    
    // Set some attendance
    const hadirButtons = screen.getAllByText('H')
    fireEvent.click(hadirButtons[0])
    
    // Reset
    const resetButton = screen.getByText(/reset/i)
    fireEvent.click(resetButton)
    
    // All buttons should be unselected
    hadirButtons.forEach(button => {
      expect(button.closest('button')).not.toHaveClass('bg-green-500')
    })
  })
})