import { render, screen, fireEvent, waitFor } from '@/__tests__/utils/test-utils'
import { LoginForm } from '@/components/forms/LoginForm'
import { mockFetch } from '@/__tests__/utils/test-utils'

// Mock next/navigation
const mockPush = jest.fn()
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

describe('LoginForm', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockPush.mockClear()
  })

  it('should render login form', () => {
    render(<LoginForm />)
    
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /masuk/i })).toBeInTheDocument()
  })

  it('should validate required fields', async () => {
    render(<LoginForm />)
    
    const submitButton = screen.getByRole('button', { name: /masuk/i })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/email wajib diisi/i)).toBeInTheDocument()
      expect(screen.getByText(/password wajib diisi/i)).toBeInTheDocument()
    })
  })

  it('should validate email format', async () => {
    render(<LoginForm />)
    
    const emailInput = screen.getByLabelText(/email/i)
    const submitButton = screen.getByRole('button', { name: /masuk/i })
    
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/format email tidak valid/i)).toBeInTheDocument()
    })
  })

  it('should submit form with valid data', async () => {
    mockFetch({
      success: true,
      data: {
        user: {
          id: 1,
          email: '<EMAIL>',
          name: 'Test Teacher',
          role: 'teacher',
        },
      },
    })

    render(<LoginForm />)
    
    const emailInput = screen.getByLabelText(/email/i)
    const passwordInput = screen.getByLabelText(/password/i)
    const submitButton = screen.getByRole('button', { name: /masuk/i })
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123',
        }),
      })
    })

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/guru')
    })
  })

  it('should handle login errors', async () => {
    mockFetch({
      success: false,
      error: {
        code: 'INVALID_CREDENTIALS',
        message: 'Email atau password salah',
      },
    }, false)

    render(<LoginForm />)
    
    const emailInput = screen.getByLabelText(/email/i)
    const passwordInput = screen.getByLabelText(/password/i)
    const submitButton = screen.getByRole('button', { name: /masuk/i })
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/email atau password salah/i)).toBeInTheDocument()
    })
  })

  it('should show loading state during submission', async () => {
    // Mock a delayed response
    const mockResponse = Promise.resolve({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: { user: { role: 'teacher' } },
      }),
    })
    ;(global.fetch as jest.Mock).mockReturnValue(mockResponse)

    render(<LoginForm />)
    
    const emailInput = screen.getByLabelText(/email/i)
    const passwordInput = screen.getByLabelText(/password/i)
    const submitButton = screen.getByRole('button', { name: /masuk/i })
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.click(submitButton)

    // Should show loading state
    expect(screen.getByText(/memproses/i)).toBeInTheDocument()
    expect(submitButton).toBeDisabled()

    // Wait for completion
    await waitFor(() => {
      expect(mockPush).toHaveBeenCalled()
    })
  })

  it('should redirect based on user role', async () => {
    // Test teacher redirect
    mockFetch({
      success: true,
      data: {
        user: { role: 'teacher' },
      },
    })

    render(<LoginForm />)
    
    const emailInput = screen.getByLabelText(/email/i)
    const passwordInput = screen.getByLabelText(/password/i)
    const submitButton = screen.getByRole('button', { name: /masuk/i })
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/guru')
    })

    // Test student redirect
    mockPush.mockClear()
    mockFetch({
      success: true,
      data: {
        user: { role: 'student' },
      },
    })

    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/siswa')
    })
  })
})