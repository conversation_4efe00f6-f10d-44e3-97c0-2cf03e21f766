import { hashPassword, verifyPassword, createSession, validateSession } from '@/lib/auth'
import { cookies } from 'next/headers'

// Mock next/headers
jest.mock('next/headers', () => ({
  cookies: jest.fn(),
}))

// Mock database
jest.mock('@/lib/db', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
  },
}))

describe('Authentication Utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('hashPassword', () => {
    it('should hash a password', async () => {
      const password = 'testpassword123'
      const hashedPassword = await hashPassword(password)
      
      expect(hashedPassword).toBeDefined()
      expect(hashedPassword).not.toBe(password)
      expect(hashedPassword.length).toBeGreaterThan(50) // bcrypt hashes are typically 60 chars
    })

    it('should generate different hashes for the same password', async () => {
      const password = 'testpassword123'
      const hash1 = await hashPassword(password)
      const hash2 = await hashPassword(password)
      
      expect(hash1).not.toBe(hash2)
    })
  })

  describe('verifyPassword', () => {
    it('should verify correct password', async () => {
      const password = 'testpassword123'
      const hashedPassword = await hashPassword(password)
      
      const isValid = await verifyPassword(password, hashedPassword)
      expect(isValid).toBe(true)
    })

    it('should reject incorrect password', async () => {
      const password = 'testpassword123'
      const wrongPassword = 'wrongpassword'
      const hashedPassword = await hashPassword(password)
      
      const isValid = await verifyPassword(wrongPassword, hashedPassword)
      expect(isValid).toBe(false)
    })

    it('should handle empty passwords', async () => {
      const hashedPassword = await hashPassword('test')
      
      const isValid = await verifyPassword('', hashedPassword)
      expect(isValid).toBe(false)
    })
  })

  describe('createSession', () => {
    it('should create a session with valid user ID', async () => {
      const mockCookies = {
        set: jest.fn(),
      }
      ;(cookies as jest.Mock).mockReturnValue(mockCookies)

      // Mock database insert
      const { db } = require('@/lib/db')
      db.insert.mockReturnValue({
        values: jest.fn().mockResolvedValue([{ id: 'session-123' }]),
      })

      const sessionId = await createSession(1)
      
      expect(sessionId).toBeDefined()
      expect(typeof sessionId).toBe('string')
      expect(sessionId.length).toBeGreaterThan(10)
      expect(mockCookies.set).toHaveBeenCalledWith(
        'session',
        sessionId,
        expect.objectContaining({
          httpOnly: true,
          secure: true,
          sameSite: 'lax',
        })
      )
    })

    it('should handle database errors gracefully', async () => {
      const mockCookies = {
        set: jest.fn(),
      }
      ;(cookies as jest.Mock).mockReturnValue(mockCookies)

      // Mock database error
      const { db } = require('@/lib/db')
      db.insert.mockReturnValue({
        values: jest.fn().mockRejectedValue(new Error('Database error')),
      })

      await expect(createSession(1)).rejects.toThrow('Database error')
    })
  })

  describe('validateSession', () => {
    it('should validate existing session', async () => {
      const mockCookies = {
        get: jest.fn().mockReturnValue({ value: 'valid-session-id' }),
      }
      ;(cookies as jest.Mock).mockReturnValue(mockCookies)

      // Mock database query
      const { db } = require('@/lib/db')
      db.select.mockReturnValue({
        from: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue([{
          id: 1,
          email: '<EMAIL>',
          name: 'Test User',
          role: 'teacher',
          isActive: true,
          isSuperAdmin: false,
        }]),
      })

      const user = await validateSession()
      
      expect(user).toBeDefined()
      expect(user?.email).toBe('<EMAIL>')
      expect(user?.role).toBe('teacher')
    })

    it('should return null for invalid session', async () => {
      const mockCookies = {
        get: jest.fn().mockReturnValue({ value: 'invalid-session-id' }),
      }
      ;(cookies as jest.Mock).mockReturnValue(mockCookies)

      // Mock database query returning no results
      const { db } = require('@/lib/db')
      db.select.mockReturnValue({
        from: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue([]),
      })

      const user = await validateSession()
      expect(user).toBeNull()
    })

    it('should return null when no session cookie exists', async () => {
      const mockCookies = {
        get: jest.fn().mockReturnValue(undefined),
      }
      ;(cookies as jest.Mock).mockReturnValue(mockCookies)

      const user = await validateSession()
      expect(user).toBeNull()
    })

    it('should handle expired sessions', async () => {
      const mockCookies = {
        get: jest.fn().mockReturnValue({ value: 'expired-session-id' }),
        delete: jest.fn(),
      }
      ;(cookies as jest.Mock).mockReturnValue(mockCookies)

      // Mock database query returning expired session
      const { db } = require('@/lib/db')
      const expiredDate = new Date(Date.now() - 1000 * 60 * 60) // 1 hour ago
      db.select.mockReturnValue({
        from: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue([{
          expiresAt: expiredDate.toISOString(),
        }]),
      })

      const user = await validateSession()
      expect(user).toBeNull()
    })
  })
})