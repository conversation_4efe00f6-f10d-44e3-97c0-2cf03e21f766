import { 
  calculateAttendanceRate, 
  getAttendanceStatusLabel, 
  formatDateForDB, 
  validateAttendanceStatus 
} from '@/lib/database-utils'

describe('Database Utilities', () => {
  describe('calculateAttendanceRate', () => {
    it('should calculate correct attendance rate', () => {
      const attendanceData = [
        { status: 'H' },
        { status: 'H' },
        { status: 'I' },
        { status: 'S' },
        { status: 'A' },
      ]
      
      const rate = calculateAttendanceRate(attendanceData as any)
      expect(rate).toBe(40) // 2 out of 5 = 40%
    })

    it('should return 0 for empty data', () => {
      const rate = calculateAttendanceRate([])
      expect(rate).toBe(0)
    })

    it('should handle all present', () => {
      const attendanceData = [
        { status: 'H' },
        { status: 'H' },
        { status: 'H' },
      ]
      
      const rate = calculateAttendanceRate(attendanceData as any)
      expect(rate).toBe(100)
    })

    it('should handle no present', () => {
      const attendanceData = [
        { status: 'I' },
        { status: 'S' },
        { status: 'A' },
      ]
      
      const rate = calculateAttendanceRate(attendanceData as any)
      expect(rate).toBe(0)
    })
  })

  describe('getAttendanceStatusLabel', () => {
    it('should return correct labels for all statuses', () => {
      expect(getAttendanceStatusLabel('H')).toBe('Hadir')
      expect(getAttendanceStatusLabel('I')).toBe('Izin')
      expect(getAttendanceStatusLabel('S')).toBe('Sakit')
      expect(getAttendanceStatusLabel('A')).toBe('Alpa')
    })

    it('should handle invalid status', () => {
      expect(getAttendanceStatusLabel('X' as any)).toBe('Tidak Diketahui')
    })
  })

  describe('formatDateForDB', () => {
    it('should format date correctly', () => {
      const date = new Date('2024-12-16T10:30:00Z')
      const formatted = formatDateForDB(date)
      expect(formatted).toBe('2024-12-16')
    })

    it('should handle string dates', () => {
      const formatted = formatDateForDB('2024-12-16')
      expect(formatted).toBe('2024-12-16')
    })

    it('should handle current date', () => {
      const now = new Date()
      const formatted = formatDateForDB(now)
      const expected = now.toISOString().split('T')[0]
      expect(formatted).toBe(expected)
    })
  })

  describe('validateAttendanceStatus', () => {
    it('should validate correct statuses', () => {
      expect(validateAttendanceStatus('H')).toBe(true)
      expect(validateAttendanceStatus('I')).toBe(true)
      expect(validateAttendanceStatus('S')).toBe(true)
      expect(validateAttendanceStatus('A')).toBe(true)
    })

    it('should reject invalid statuses', () => {
      expect(validateAttendanceStatus('X')).toBe(false)
      expect(validateAttendanceStatus('')).toBe(false)
      expect(validateAttendanceStatus('h')).toBe(false) // lowercase
      expect(validateAttendanceStatus('HADIR')).toBe(false) // full word
    })

    it('should handle null and undefined', () => {
      expect(validateAttendanceStatus(null as any)).toBe(false)
      expect(validateAttendanceStatus(undefined as any)).toBe(false)
    })
  })
})