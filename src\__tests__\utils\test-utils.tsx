import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { AuthenticatedUser } from '@/lib/auth'

// Mock auth context
const MockAuthProvider = ({ 
  children, 
  user = null 
}: { 
  children: React.ReactNode
  user?: AuthenticatedUser | null 
}) => {
  // Mock the auth context provider
  return <>{children}</>
}

// Custom render function with providers
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'> & {
    user?: AuthenticatedUser | null
  }
) => {
  const { user, ...renderOptions } = options || {}
  
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <MockAuthProvider user={user}>
      {children}
    </MockAuthProvider>
  )

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Mock user data
export const mockTeacher: AuthenticatedUser = {
  id: 1,
  email: '<EMAIL>',
  name: 'Test Teacher',
  role: 'teacher',
  isActive: true,
  isSuperAdmin: false,
}

export const mockStudent: AuthenticatedUser = {
  id: 2,
  email: '<EMAIL>',
  name: 'Test Student',
  role: 'student',
  isActive: true,
  isSuperAdmin: false,
}

// Mock API responses
export const mockApiResponse = (data: any, success = true) => ({
  success,
  data,
  error: success ? null : { code: 'TEST_ERROR', message: 'Test error' },
})

// Mock fetch responses
export const mockFetch = (response: any, ok = true) => {
  const mockResponse = {
    ok,
    status: ok ? 200 : 400,
    json: jest.fn().mockResolvedValue(response),
    blob: jest.fn().mockResolvedValue(new Blob()),
    headers: new Headers(),
  }
  
  ;(global.fetch as jest.Mock).mockResolvedValueOnce(mockResponse)
  return mockResponse
}

// Database test utilities
export const createMockDatabase = () => {
  const mockDb = {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    innerJoin: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    transaction: jest.fn(),
  }
  
  return mockDb
}

// Test data generators
export const generateMockStudents = (count = 5) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    name: `Student ${i + 1}`,
    studentId: `NIS${String(i + 1).padStart(3, '0')}`,
    email: `student${i + 1}@test.com`,
    classroomId: 1,
    userId: i + 10,
    isActive: true,
    createdAt: new Date().toISOString(),
  }))
}

export const generateMockAttendance = (studentIds: number[], date = '2024-12-16') => {
  return studentIds.map((studentId, i) => ({
    id: i + 1,
    studentId,
    classroomSubjectId: 1,
    date,
    status: ['H', 'I', 'S', 'A'][i % 4] as 'H' | 'I' | 'S' | 'A',
    notes: i % 2 === 0 ? `Note for student ${studentId}` : null,
    markedBy: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }))
}

export const generateMockClassrooms = (count = 3) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    name: `Kelas ${i + 1}A`,
    grade: `${i + 7}`,
    academicYear: '2024/2025',
    homeroomTeacherId: i + 1,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }))
}

// Wait for async operations
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Re-export everything from React Testing Library
export * from '@testing-library/react'
export { customRender as render }