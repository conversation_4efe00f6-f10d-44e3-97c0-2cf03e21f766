import { redirect } from 'next/navigation';
import { getCurrentUser } from '@/lib/auth';
import { LoginForm } from '@/components/forms/LoginForm';

export default async function LoginPage() {
  // Check if user is already authenticated
  const user = await getCurrentUser();
  
  if (user) {
    // Redirect to appropriate dashboard based on role
    if (user.role === 'teacher') {
      redirect('/guru');
    } else if (user.role === 'student') {
      redirect('/siswa');
    } else {
      redirect('/');
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            GuruFlow
          </h1>
          <p className="text-gray-600">
            Platform Administrasi Sekolah Modern
          </p>
        </div>
        <LoginForm />
      </div>
    </div>
  );
}