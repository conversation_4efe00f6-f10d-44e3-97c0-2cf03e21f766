'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { OnboardingWizard } from '@/components/onboarding/OnboardingWizard';

export default function OnboardingPage() {
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    async function checkOnboardingStatus() {
      try {
        // Check if user is already authenticated
        const authResponse = await fetch('/api/auth/me');
        if (authResponse.ok) {
          const { user } = await authResponse.json();
          if (user) {
            // User is already logged in, redirect to appropriate dashboard
            if (user.role === 'teacher') {
              router.push('/guru');
              return;
            } else if (user.role === 'student') {
              router.push('/siswa');
              return;
            } else {
              router.push('/');
              return;
            }
          }
        }

        // Check if onboarding has already been completed
        const schoolResponse = await fetch('/api/school/status');
        if (schoolResponse.ok) {
          const { data } = await schoolResponse.json();
          if (data && data.isSetup) {
            // Onboarding already completed, redirect to login
            router.push('/login');
            return;
          }
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        setIsLoading(false);
      }
    }

    checkOnboardingStatus();
  }, [router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return <OnboardingWizard />;
}