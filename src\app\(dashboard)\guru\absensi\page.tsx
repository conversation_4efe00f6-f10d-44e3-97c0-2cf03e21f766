import { requireTeacher } from '@/lib/auth-guards';
import { ResponsiveDashboardLayout } from '@/components/layout/ResponsiveDashboardLayout';
import { AttendanceManager } from '@/components/attendance/AttendanceManager';

export default async function AttendancePage() {
  const user = await requireTeacher();

  return (
    <ResponsiveDashboardLayout title="Manajemen Absensi">
      <div className="space-y-6">
        <div className="lg:hidden">
          <p className="text-gray-600">
            Catat dan kelola kehadiran siswa
          </p>
        </div>

        <AttendanceManager />
      </div>
    </ResponsiveDashboardLayout>
  );
}