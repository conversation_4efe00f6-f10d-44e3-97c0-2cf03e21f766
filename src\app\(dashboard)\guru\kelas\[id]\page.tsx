import { requireTeacher } from '@/lib/auth-guards';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { ClassroomDetail } from '@/components/classroom/ClassroomDetail';

interface ClassroomDetailPageProps {
  params: {
    id: string;
  };
}

export default async function ClassroomDetailPage({ params }: ClassroomDetailPageProps) {
  const user = await requireTeacher();
  const classroomId = parseInt(params.id);

  return (
    <DashboardLayout
      user={user}
      title="Detail Kelas"
      subtitle="Kelola informasi kelas, siswa, dan mata pelajaran"
    >
      <ClassroomDetail classroomId={params.id} />
    </DashboardLayout>
  );
}