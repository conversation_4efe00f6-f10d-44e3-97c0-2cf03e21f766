import { requireTeacher } from '@/lib/auth-guards';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { CreateClassroomForm } from '@/components/classroom/CreateClassroomForm';

export default async function CreateClassroomPage() {
  const user = await requireTeacher();

  return (
    <DashboardLayout
      user={user}
      title="Buat Kelas Baru"
      subtitle="Tambahkan kelas baru ke dalam sistem"
    >
      <CreateClassroomForm />
    </DashboardLayout>
  );
}