import { requireTeacher } from '@/lib/auth-guards';
import { ResponsiveDashboardLayout } from '@/components/layout/ResponsiveDashboardLayout';
import { ClassroomList } from '@/components/classroom/ClassroomList';

export default async function ClassroomManagementPage() {
  const user = await requireTeacher();

  return (
    <ResponsiveDashboardLayout title="Manajemen Kelas">
      <div className="space-y-6">
        <div className="lg:hidden">
          <p className="text-gray-600"><PERSON><PERSON><PERSON> kela<PERSON>, mata pelajaran, dan siswa</p>
        </div>
        <ClassroomList />
      </div>
    </ResponsiveDashboardLayout>
  );
}