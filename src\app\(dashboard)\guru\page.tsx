import { requireTeacher } from '@/lib/auth-guards';
import { ResponsiveDashboardLayout } from '@/components/layout/ResponsiveDashboardLayout';
import { StatsCards } from '@/components/dashboard/StatsCards';
import { TeachingSchedule } from '@/components/dashboard/TeachingSchedule';
import { QuickActions } from '@/components/dashboard/QuickActions';
import { XPLeaderboard } from '@/components/dashboard/XPLeaderboard';
import { ResponsiveGrid } from '@/components/ui/responsive-container';

export default async function TeacherDashboard() {
  const user = await requireTeacher();

  // Mock data - in real implementation, fetch from database
  const mockStats = {
    totalStudents: 125,
    totalClasses: 8,
    todayAttendance: 92,
    pendingAssignments: 15,
  };

  const mockSchedule = [
    {
      id: 1,
      subject: 'Matematika',
      className: 'Kelas 5A',
      time: '08:00 - 09:30',
      room: 'Ruang 101',
      studentCount: 28,
      status: 'upcoming' as const,
    },
    {
      id: 2,
      subject: 'IPA',
      className: 'Kelas 5B',
      time: '10:00 - 11:30',
      room: 'Lab IPA',
      studentCount: 25,
      status: 'ongoing' as const,
    },
    {
      id: 3,
      subject: 'Bahasa Indonesia',
      className: 'Kelas 5A',
      time: '13:00 - 14:30',
      room: 'Ruang 101',
      studentCount: 28,
      status: 'upcoming' as const,
    },
  ];

  const mockLeaderboard = [
    {
      id: 1,
      name: 'Ahmad Rizki',
      className: 'Kelas 5A',
      totalXP: 2450,
      level: 8,
      badges: 5,
      weeklyXP: 320,
      rank: 1,
    },
    {
      id: 2,
      name: 'Siti Nurhaliza',
      className: 'Kelas 5B',
      totalXP: 2380,
      level: 7,
      badges: 4,
      weeklyXP: 280,
      rank: 2,
    },
    {
      id: 3,
      name: 'Budi Santoso',
      className: 'Kelas 5A',
      totalXP: 2250,
      level: 7,
      badges: 3,
      weeklyXP: 250,
      rank: 3,
    },
    {
      id: 4,
      name: 'Dewi Lestari',
      className: 'Kelas 5C',
      totalXP: 2100,
      level: 6,
      badges: 4,
      weeklyXP: 200,
      rank: 4,
    },
    {
      id: 5,
      name: 'Andi Pratama',
      className: 'Kelas 5B',
      totalXP: 1950,
      level: 6,
      badges: 2,
      weeklyXP: 180,
      rank: 5,
    },
  ];

  return (
    <ResponsiveDashboardLayout title="Dashboard Guru">
      <div className="space-y-6">
        {/* Welcome Message - Mobile Only */}
        <div className="lg:hidden bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-blue-900 mb-1">
            Selamat datang, {user.name}
          </h2>
          <p className="text-sm text-blue-700">
            Kelola kelas dan siswa Anda dengan mudah
          </p>
        </div>

        {/* Stats Cards */}
        <StatsCards userRole="teacher" stats={mockStats} />

        {/* Main Content Grid */}
        <ResponsiveGrid 
          cols={{ default: 1, lg: 3 }} 
          gap="lg"
          className="items-start"
        >
          {/* Left Column - Schedule and Actions */}
          <div className="lg:col-span-2 space-y-6">
            <TeachingSchedule schedules={mockSchedule} />
            <QuickActions userRole="teacher" />
          </div>

          {/* Right Column - Leaderboard */}
          <div className="lg:col-span-1">
            <XPLeaderboard students={mockLeaderboard} />
          </div>
        </ResponsiveGrid>
      </div>
    </ResponsiveDashboardLayout>
  );
}