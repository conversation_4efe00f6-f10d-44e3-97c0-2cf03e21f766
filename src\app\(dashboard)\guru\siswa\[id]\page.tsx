import { requireTeacher } from '@/lib/auth-guards';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { StudentDetail } from '@/components/student/StudentDetail';

interface StudentDetailPageProps {
  params: {
    id: string;
  };
}

export default async function StudentDetailPage({ params }: StudentDetailPageProps) {
  const user = await requireTeacher();
  const studentId = parseInt(params.id);

  return (
    <DashboardLayout
      user={user}
      title="Profile Siswa"
      subtitle="Informasi lengkap dan riwayat akademik siswa"
    >
      <StudentDetail studentId={studentId} />
    </DashboardLayout>
  );
}