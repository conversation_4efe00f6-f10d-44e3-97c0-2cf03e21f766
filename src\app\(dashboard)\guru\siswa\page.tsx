import { requireTeacher } from '@/lib/auth-guards';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { StudentList } from '@/components/student/StudentList';

export default async function StudentManagementPage() {
  const user = await requireTeacher();

  return (
    <DashboardLayout
      user={user}
      title="Manajemen Siswa"
      subtitle="Kelola profil siswa dan enrollment kelas"
    >
      <StudentList />
    </DashboardLayout>
  );
}