import { requireTeacher } from '@/lib/auth-guards';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { CreateStudentForm } from '@/components/student/CreateStudentForm';

export default async function CreateStudentPage() {
  const user = await requireTeacher();

  return (
    <DashboardLayout
      user={user}
      title="Tambah Siswa Baru"
      subtitle="Daftarkan siswa baru ke dalam sistem"
    >
      <CreateStudentForm />
    </DashboardLayout>
  );
}