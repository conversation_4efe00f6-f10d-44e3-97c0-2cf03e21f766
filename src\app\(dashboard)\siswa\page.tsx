import { requireStudent } from '@/lib/auth-guards';
import { ResponsiveDashboardLayout } from '@/components/layout/ResponsiveDashboardLayout';
import { StatsCards } from '@/components/dashboard/StatsCards';
import { ActiveAssignments } from '@/components/dashboard/ActiveAssignments';
import { AcademicProgress } from '@/components/dashboard/AcademicProgress';
import { GamificationStats } from '@/components/dashboard/GamificationStats';
import { QuickActions } from '@/components/dashboard/QuickActions';
import { ResponsiveGrid } from '@/components/ui/responsive-container';

export default async function StudentDashboard() {
  const user = await requireStudent();

  // Mock data - in real implementation, fetch from database
  const mockStats = {
    totalXP: 2450,
    currentLevel: 8,
    attendanceRate: 92,
    completedAssignments: 15,
    pendingAssignments: 3,
    weeklyProgress: 12,
  };

  const mockAssignments = [
    {
      id: 1,
      title: 'Laporan Percobaan IPA',
      subject: 'IPA',
      teacher: '<PERSON><PERSON> Sari',
      dueDate: '2024-12-20T23:59:00',
      status: 'not_started' as const,
      priority: 'high' as const,
      description: 'Buat laporan hasil percobaan tentang sifat-sifat cahaya',
      maxScore: 100,
    },
    {
      id: 2,
      title: 'Soal Matematika Bab 5',
      subject: 'Matematika',
      teacher: 'Pak Budi',
      dueDate: '2024-12-22T15:00:00',
      status: 'in_progress' as const,
      priority: 'medium' as const,
      description: 'Kerjakan soal latihan halaman 85-90',
      maxScore: 100,
    },
    {
      id: 3,
      title: 'Essay Bahasa Indonesia',
      subject: 'Bahasa Indonesia',
      teacher: 'Bu Ani',
      dueDate: '2024-12-25T23:59:00',
      status: 'submitted' as const,
      priority: 'low' as const,
      description: 'Tulis essay tentang kebudayaan daerah',
      maxScore: 100,
    },
  ];

  const mockGrades = [
    {
      subject: 'Matematika',
      currentGrade: 88,
      maxGrade: 100,
      letterGrade: 'B+',
      trend: 'up' as const,
      assignmentCount: 8,
      lastUpdated: '2024-12-15',
    },
    {
      subject: 'IPA',
      currentGrade: 92,
      maxGrade: 100,
      letterGrade: 'A-',
      trend: 'stable' as const,
      assignmentCount: 6,
      lastUpdated: '2024-12-14',
    },
    {
      subject: 'Bahasa Indonesia',
      currentGrade: 85,
      maxGrade: 100,
      letterGrade: 'B',
      trend: 'up' as const,
      assignmentCount: 7,
      lastUpdated: '2024-12-13',
    },
    {
      subject: 'IPS',
      currentGrade: 78,
      maxGrade: 100,
      letterGrade: 'B-',
      trend: 'down' as const,
      assignmentCount: 5,
      lastUpdated: '2024-12-12',
    },
  ];

  const mockBadges = [
    {
      id: 1,
      name: 'Rajin Hadir',
      description: 'Hadir 30 hari berturut-turut',
      icon: 'attendance-streak',
      earnedAt: '2024-12-10',
      rarity: 'rare' as const,
    },
    {
      id: 2,
      name: 'Pengumpul Tugas',
      description: 'Mengumpulkan 10 tugas tepat waktu',
      icon: 'assignment-master',
      earnedAt: '2024-12-05',
      rarity: 'common' as const,
    },
  ];

  const mockGamification = {
    currentXP: 1250,
    currentLevel: 8,
    xpToNextLevel: 750,
    totalXP: 2450,
    classRank: 3,
    totalStudents: 28,
    badges: mockBadges,
    weeklyXP: 320,
    streak: 15,
  };

  const overallAverage = mockGrades.reduce((acc, grade) => acc + grade.currentGrade, 0) / mockGrades.length;

  return (
    <ResponsiveDashboardLayout title="Dashboard Siswa">
      <div className="space-y-6">
        {/* Welcome Message - Mobile Only */}
        <div className="lg:hidden bg-green-50 border border-green-200 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-green-900 mb-1">
            Selamat datang, {user.name}
          </h2>
          <p className="text-sm text-green-700">
            Pantau progress belajar dan kerjakan tugas Anda
          </p>
        </div>

        {/* Stats Cards */}
        <StatsCards userRole="student" stats={mockStats} />

        {/* Main Content Grid */}
        <ResponsiveGrid 
          cols={{ default: 1, lg: 3 }} 
          gap="lg"
          className="items-start"
        >
          {/* Left Column - Assignments and Actions */}
          <div className="lg:col-span-2 space-y-6">
            <ActiveAssignments assignments={mockAssignments} />
            <QuickActions userRole="student" />
          </div>

          {/* Right Column - Progress and Gamification */}
          <div className="lg:col-span-1 space-y-6">
            <AcademicProgress 
              grades={mockGrades} 
              overallAverage={overallAverage}
              semester="Ganjil 2024/2025"
            />
            <GamificationStats {...mockGamification} />
          </div>
        </ResponsiveGrid>
      </div>
    </ResponsiveDashboardLayout>
  );
}