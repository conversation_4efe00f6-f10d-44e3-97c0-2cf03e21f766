import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { attendanceRecords } from '@/db/schema';
import { eq } from 'drizzle-orm';

interface RouteParams {
  params: {
    id: string;
  };
}

// PUT - Update individual attendance record
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'A<PERSON><PERSON> ditolak' }
      }, { status: 401 });
    }

    const attendanceId = parseInt(params.id);
    const body = await request.json();
    const { status, notes } = body;

    if (!status || !['H', 'I', 'S', 'A'].includes(status)) {
      return NextResponse.json({
        success: false,
        error: { code: 'INVALID_STATUS', message: 'Status absensi tidak valid' }
      }, { status: 400 });
    }

    // Get current attendance record
    const currentAttendance = await db
      .select()
      .from(attendanceRecords)
      .where(eq(attendanceRecords.id, attendanceId))
      .limit(1);

    if (currentAttendance.length === 0) {
      return NextResponse.json({
        success: false,
        error: { code: 'NOT_FOUND', message: 'Data absensi tidak ditemukan' }
      }, { status: 404 });
    }

    // Update attendance record
    await db
      .update(attendanceRecords)
      .set({
        status,
        notes: notes || null,
        updatedAt: new Date().toISOString(),
      })
      .where(eq(attendanceRecords.id, attendanceId));

    return NextResponse.json({
      success: true,
      message: 'Absensi berhasil diperbarui',
    });
  } catch (error) {
    console.error('Update attendance error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal memperbarui absensi' }
    }, { status: 500 });
  }
}

// GET - Get individual attendance record
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    const attendanceId = parseInt(params.id);

    // Get attendance record
    const attendanceRecord = await db
      .select()
      .from(attendanceRecords)
      .where(eq(attendanceRecords.id, attendanceId))
      .limit(1);

    if (attendanceRecord.length === 0) {
      return NextResponse.json({
        success: false,
        error: { code: 'NOT_FOUND', message: 'Data absensi tidak ditemukan' }
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: {
        attendance: attendanceRecord[0],
      },
    });
  } catch (error) {
    console.error('Get attendance error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal mengambil data absensi' }
    }, { status: 500 });
  }
}