import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { 
  attendanceRecords, 
  students, 
  users, 
  classroomSubjects, 
  subjects, 
  classrooms,
  users as teachers
} from '@/db/schema';
import { eq, and, desc, sql } from 'drizzle-orm';

// GET - Get attendance history
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: '<PERSON><PERSON><PERSON> dito<PERSON>' }
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const classroomId = searchParams.get('classroomId');
    const subjectId = searchParams.get('subjectId');
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build query with filters
    const conditions = [];
    
    if (classroomId) {
      conditions.push(eq(attendanceRecords.classroomId, parseInt(classroomId)));
    }

    const query = db
      .select({
        id: attendanceRecords.id,
        date: attendanceRecords.date,
        studentName: users.name,
        studentId: students.studentId,
        status: attendanceRecords.status,
        notes: attendanceRecords.notes,
        classroomName: classrooms.name,
      })
      .from(attendanceRecords)
      .innerJoin(students, eq(attendanceRecords.studentId, students.id))
      .innerJoin(users, eq(students.userId, users.id))
      .innerJoin(classrooms, eq(attendanceRecords.classroomId, classrooms.id))
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    // Execute query with pagination
    const records = await query
      .orderBy(desc(attendanceRecords.date), users.name)
      .limit(limit)
      .offset(offset);

    return NextResponse.json({
      success: true,
      data: records,
      pagination: {
        limit,
        offset,
        total: records.length,
      },
    });
  } catch (error) {
    console.error('Get attendance history error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal mengambil riwayat absensi' }
    }, { status: 500 });
  }
}