import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { 
  attendanceRecords, 
  students, 
  users, 
  classroomSubjects, 
  subjects, 
  classrooms,
  schools
} from '@/db/schema';
import { eq, and, gte, lte, sql } from 'drizzle-orm';

// GET - Generate attendance reports
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: '<PERSON><PERSON><PERSON> dito<PERSON>' }
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const classroomId = searchParams.get('classroomId');
    const subjectId = searchParams.get('subjectId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const reportType = searchParams.get('reportType') || 'summary';
    const format = searchParams.get('format') || 'csv';

    if (!classroomId || !startDate || !endDate) {
      return NextResponse.json({
        success: false,
        error: { code: 'MISSING_PARAMS', message: 'Parameter tidak lengkap' }
      }, { status: 400 });
    }

    // Get school info for header
    const schoolInfo = await db.select().from(schools).limit(1);
    const school = schoolInfo[0];

    // Get classroom info
    const classroomInfo = await db
      .select()
      .from(classrooms)
      .where(eq(classrooms.id, parseInt(classroomId)))
      .limit(1);

    if (classroomInfo.length === 0) {
      return NextResponse.json({
        success: false,
        error: { code: 'NOT_FOUND', message: 'Kelas tidak ditemukan' }
      }, { status: 404 });
    }

    const classroom = classroomInfo[0];

    // Build query conditions
    const conditions = [
      gte(attendanceRecords.date, startDate),
      lte(attendanceRecords.date, endDate)
    ];

    // Get classroom subjects
    let classroomSubjectIds: number[] = [];
    
    if (subjectId) {
      const specificSubject = await db
        .select()
        .from(classroomSubjects)
        .where(and(
          eq(classroomSubjects.classroomId, parseInt(classroomId)),
          eq(classroomSubjects.subjectId, parseInt(subjectId))
        ));
      
      classroomSubjectIds = specificSubject.map(cs => cs.id);
    } else {
      const allSubjects = await db
        .select()
        .from(classroomSubjects)
        .where(eq(classroomSubjects.classroomId, parseInt(classroomId)));
      
      classroomSubjectIds = allSubjects.map(cs => cs.id);
    }

    // Note: attendanceRecords uses classroomId directly, not classroomSubjectId
    // The filtering is already done by classroomId above

    if (format === 'csv') {
      return generateCSVReport(conditions, reportType, classroom, school, startDate, endDate);
    } else {
      return generatePDFReport(conditions, reportType, classroom, school, startDate, endDate);
    }

  } catch (error) {
    console.error('Generate report error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal generate laporan' }
    }, { status: 500 });
  }
}

async function generateCSVReport(
  conditions: any[],
  reportType: string,
  classroom: any,
  school: any,
  startDate: string,
  endDate: string
) {
  let csvContent = '';
  
  // Header
  csvContent += `Laporan Absensi ${school?.name || 'Sekolah'}\n`;
  csvContent += `Kelas: ${classroom.name}\n`;
  csvContent += `Periode: ${new Date(startDate).toLocaleDateString('id-ID')} - ${new Date(endDate).toLocaleDateString('id-ID')}\n`;
  csvContent += `Tanggal Generate: ${new Date().toLocaleDateString('id-ID')}\n\n`;

  if (reportType === 'summary') {
    // Summary report
    const summaryData = await db
      .select({
        date: attendanceRecords.date,
        totalRecords: sql<number>`COUNT(*)`,
        presentCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'H' THEN 1 ELSE 0 END)`,
        izinCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'I' THEN 1 ELSE 0 END)`,
        sakitCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'S' THEN 1 ELSE 0 END)`,
        alpaCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'A' THEN 1 ELSE 0 END)`,
      })
      .from(attendanceRecords)
      .where(and(...conditions))
      .groupBy(attendanceRecords.date)
      .orderBy(attendanceRecords.date);

    csvContent += 'Tanggal,Total Siswa,Hadir,Izin,Sakit,Alpa,Persentase Kehadiran\n';
    
    summaryData.forEach(row => {
      const attendanceRate = row.totalRecords > 0 ? (row.presentCount / row.totalRecords * 100).toFixed(1) : '0';
      csvContent += `${new Date(row.date).toLocaleDateString('id-ID')},${row.totalRecords},${row.presentCount},${row.izinCount},${row.sakitCount},${row.alpaCount},${attendanceRate}%\n`;
    });

  } else if (reportType === 'detailed') {
    // Detailed report
    const detailedData = await db
      .select({
        date: attendanceRecords.date,
        studentName: users.name,
        studentId: students.studentId,
        status: attendanceRecords.status,
        notes: attendanceRecords.notes,
        classroomName: classrooms.name,
      })
      .from(attendanceRecords)
      .innerJoin(students, eq(attendanceRecords.studentId, students.id))
      .innerJoin(users, eq(students.userId, users.id))
      .innerJoin(classrooms, eq(attendanceRecords.classroomId, classrooms.id))
      .where(and(...conditions))
      .orderBy(attendanceRecords.date, users.name);

    csvContent += 'Tanggal,Nama Siswa,NIS,Mata Pelajaran,Status,Keterangan\n';
    
    detailedData.forEach(row => {
      const statusText = {
        'H': 'Hadir',
        'I': 'Izin', 
        'S': 'Sakit',
        'A': 'Alpa'
      }[row.status] || row.status;
      
      csvContent += `${new Date(row.date).toLocaleDateString('id-ID')},"${row.studentName}",${row.studentId},"${row.classroomName}",${statusText},"${row.notes || ''}"\n`;
    });

  } else if (reportType === 'individual') {
    // Individual report
    const individualData = await db
      .select({
        studentName: users.name,
        studentId: students.studentId,
        totalRecords: sql<number>`COUNT(*)`,
        presentCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'H' THEN 1 ELSE 0 END)`,
        izinCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'I' THEN 1 ELSE 0 END)`,
        sakitCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'S' THEN 1 ELSE 0 END)`,
        alpaCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'A' THEN 1 ELSE 0 END)`,
      })
      .from(attendanceRecords)
      .innerJoin(students, eq(attendanceRecords.studentId, students.id))
      .innerJoin(users, eq(students.userId, users.id))
      .where(and(...conditions))
      .groupBy(students.id, users.name, students.studentId)
      .orderBy(users.name);

    csvContent += 'Nama Siswa,NIS,Total Hari,Hadir,Izin,Sakit,Alpa,Persentase Kehadiran\n';
    
    individualData.forEach(row => {
      const attendanceRate = row.totalRecords > 0 ? (row.presentCount / row.totalRecords * 100).toFixed(1) : '0';
      csvContent += `"${row.studentName}",${row.studentId},${row.totalRecords},${row.presentCount},${row.izinCount},${row.sakitCount},${row.alpaCount},${attendanceRate}%\n`;
    });
  }

  // Return CSV response
  const blob = new Blob([csvContent], { type: 'text/csv; charset=utf-8' });
  
  return new NextResponse(blob, {
    headers: {
      'Content-Type': 'text/csv; charset=utf-8',
      'Content-Disposition': `attachment; filename="laporan_absensi_${reportType}_${startDate}_${endDate}.csv"`,
    },
  });
}

async function generatePDFReport(
  conditions: any[],
  reportType: string,
  classroom: any,
  school: any,
  startDate: string,
  endDate: string
) {
  // For now, return a simple HTML that can be converted to PDF
  // In a real implementation, you would use a library like puppeteer or react-pdf
  
  let htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Laporan Absensi</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .info { margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .text-center { text-align: center; }
        .text-right { text-align: right; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>${school?.name || 'Sekolah'}</h1>
        <h2>Laporan Absensi</h2>
      </div>
      
      <div class="info">
        <p><strong>Kelas:</strong> ${classroom.name}</p>
        <p><strong>Periode:</strong> ${new Date(startDate).toLocaleDateString('id-ID')} - ${new Date(endDate).toLocaleDateString('id-ID')}</p>
        <p><strong>Tanggal Generate:</strong> ${new Date().toLocaleDateString('id-ID')}</p>
      </div>
  `;

  if (reportType === 'summary') {
    const summaryData = await db
      .select({
        date: attendanceRecords.date,
        totalRecords: sql<number>`COUNT(*)`,
        presentCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'H' THEN 1 ELSE 0 END)`,
        izinCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'I' THEN 1 ELSE 0 END)`,
        sakitCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'S' THEN 1 ELSE 0 END)`,
        alpaCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'A' THEN 1 ELSE 0 END)`,
      })
      .from(attendanceRecords)
      .where(and(...conditions))
      .groupBy(attendanceRecords.date)
      .orderBy(attendanceRecords.date);

    htmlContent += `
      <table>
        <thead>
          <tr>
            <th>Tanggal</th>
            <th class="text-center">Total Siswa</th>
            <th class="text-center">Hadir</th>
            <th class="text-center">Izin</th>
            <th class="text-center">Sakit</th>
            <th class="text-center">Alpa</th>
            <th class="text-center">Persentase</th>
          </tr>
        </thead>
        <tbody>
    `;

    summaryData.forEach(row => {
      const attendanceRate = row.totalRecords > 0 ? (row.presentCount / row.totalRecords * 100).toFixed(1) : '0';
      htmlContent += `
        <tr>
          <td>${new Date(row.date).toLocaleDateString('id-ID')}</td>
          <td class="text-center">${row.totalRecords}</td>
          <td class="text-center">${row.presentCount}</td>
          <td class="text-center">${row.izinCount}</td>
          <td class="text-center">${row.sakitCount}</td>
          <td class="text-center">${row.alpaCount}</td>
          <td class="text-center">${attendanceRate}%</td>
        </tr>
      `;
    });

    htmlContent += '</tbody></table>';
  }

  htmlContent += '</body></html>';

  // Return HTML response (in production, convert this to PDF)
  return new NextResponse(htmlContent, {
    headers: {
      'Content-Type': 'text/html; charset=utf-8',
      'Content-Disposition': `attachment; filename="laporan_absensi_${reportType}_${startDate}_${endDate}.html"`,
    },
  });
}