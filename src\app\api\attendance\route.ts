import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { 
  attendanceRecords, 
  students, 
  users, 
  classroomSubjects, 
  subjects, 
  classrooms
} from '@/db/schema';
import { eq, and, desc, gte, lte, sql } from 'drizzle-orm';

// GET - Get attendance records with filtering and statistics
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: '<PERSON><PERSON><PERSON> ditolak' }
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const classroomId = searchParams.get('classroomId');
    const subjectId = searchParams.get('subjectId');
    const date = searchParams.get('date');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const studentId = searchParams.get('studentId');
    const includeStats = searchParams.get('includeStats') === 'true';

    // Single date query
    if (classroomId && subjectId && date) {
      // Get classroom subject ID
      const classroomSubjectResult = await db
        .select()
        .from(classroomSubjects)
        .where(and(
          eq(classroomSubjects.classroomId, parseInt(classroomId)),
          eq(classroomSubjects.subjectId, parseInt(subjectId))
        ))
        .limit(1);

      // Note: We're now using classroomId directly instead of classroomSubjectId
      // The classroomSubject lookup is kept for validation that the subject exists in this classroom

      // Get attendance records for the date
      const records = await db
        .select({
          id: attendanceRecords.id,
          studentId: attendanceRecords.studentId,
          status: attendanceRecords.status,
          notes: attendanceRecords.notes,
          studentName: users.name,
          studentNumber: students.studentId,
          markedAt: attendanceRecords.createdAt,
          recordedBy: attendanceRecords.recordedBy,
        })
        .from(attendanceRecords)
        .innerJoin(students, eq(attendanceRecords.studentId, students.id))
        .innerJoin(users, eq(students.userId, users.id))
        .where(and(
          eq(attendanceRecords.classroomId, parseInt(classroomId)),
          eq(attendanceRecords.date, date)
        ))
        .orderBy(users.name);

      return NextResponse.json({
        success: true,
        data: records,
      });
    }

    // Date range query for history
    if (startDate && endDate) {
      let query = db
        .select({
          id: attendanceRecords.id,
          studentId: attendanceRecords.studentId,
          studentName: users.name,
          studentNumber: students.studentId,
          classroomName: classrooms.name,
          subjectName: subjects.name,
          date: attendanceRecords.date,
          status: attendanceRecords.status,
          notes: attendanceRecords.notes,
          markedAt: attendanceRecords.createdAt,
        })
        .from(attendanceRecords)
        .innerJoin(students, eq(attendanceRecords.studentId, students.id))
        .innerJoin(users, eq(students.userId, users.id))
        .innerJoin(classrooms, eq(attendanceRecords.classroomId, classrooms.id))
        .where(and(
          gte(attendanceRecords.date, startDate),
          lte(attendanceRecords.date, endDate),
          studentId ? eq(attendanceRecords.studentId, parseInt(studentId)) : undefined,
          classroomId ? eq(classrooms.id, parseInt(classroomId)) : undefined
        ))
        .orderBy(desc(attendanceRecords.date), users.name);

      const records = await query;

      let stats = null;
      if (includeStats) {
        // Calculate attendance statistics
        const statsQuery = await db
          .select({
            status: attendanceRecords.status,
            count: sql<number>`count(*)`.as('count'),
          })
          .from(attendanceRecords)
          .innerJoin(classrooms, eq(attendanceRecords.classroomId, classrooms.id))
          .where(and(
            gte(attendanceRecords.date, startDate),
            lte(attendanceRecords.date, endDate),
            studentId ? eq(attendanceRecords.studentId, parseInt(studentId)) : undefined,
            classroomId ? eq(classrooms.id, parseInt(classroomId)) : undefined
          ))
          .groupBy(attendanceRecords.status);

        stats = {
          total: records.length,
          byStatus: statsQuery.reduce((acc, item) => {
            acc[item.status] = item.count;
            return acc;
          }, {} as Record<string, number>),
        };
      }

      return NextResponse.json({
        success: true,
        data: records,
        stats,
      });
    }

    return NextResponse.json({
      success: false,
      error: { code: 'MISSING_PARAMS', message: 'Parameter tidak lengkap' }
    }, { status: 400 });
  } catch (error) {
    console.error('Get attendance error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal mengambil data absensi' }
    }, { status: 500 });
  }
}

// POST - Save attendance records
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    const body = await request.json();
    const { classroomId, subjectId, date, records } = body;

    if (!classroomId || !subjectId || !date || !records || !Array.isArray(records)) {
      return NextResponse.json({
        success: false,
        error: { code: 'INVALID_DATA', message: 'Data tidak valid' }
      }, { status: 400 });
    }

    // Teachers are users with role 'teacher'
    const teacherId = user.id;

    // Get classroom subject ID
    const classroomSubjectResult = await db
      .select()
      .from(classroomSubjects)
      .where(and(
        eq(classroomSubjects.classroomId, classroomId),
        eq(classroomSubjects.subjectId, subjectId)
      ))
      .limit(1);

    if (classroomSubjectResult.length === 0) {
      return NextResponse.json({
        success: false,
        error: { code: 'NOT_FOUND', message: 'Mata pelajaran tidak ditemukan di kelas ini' }
      }, { status: 404 });
    }

    // Save attendance records in transaction
    await db.transaction(async (tx) => {
      // Delete existing records for this date
      await tx
        .delete(attendanceRecords)
        .where(and(
          eq(attendanceRecords.classroomId, parseInt(classroomId)),
          eq(attendanceRecords.date, date)
        ));

      // Insert new records
      const attendanceData = records.map((record: any) => ({
        studentId: record.studentId,
        classroomId: parseInt(classroomId),
        date,
        status: record.status,
        notes: record.notes || null,
        recordedBy: teacherId,
      }));

      if (attendanceData.length > 0) {
        await tx.insert(attendanceRecords).values(attendanceData);
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Absensi berhasil disimpan',
    });
  } catch (error) {
    console.error('Save attendance error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal menyimpan absensi' }
    }, { status: 500 });
  }
}

// PUT - Update attendance records (bulk update)
export async function PUT(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    const body = await request.json();
    const { updates } = body;

    if (!updates || !Array.isArray(updates)) {
      return NextResponse.json({
        success: false,
        error: { code: 'INVALID_DATA', message: 'Data tidak valid' }
      }, { status: 400 });
    }

    // Teachers are users with role 'teacher'
    const teacherId = user.id;

    // Update attendance records in transaction
    await db.transaction(async (tx) => {
      for (const update of updates) {
        const { id, status, notes } = update;
        
        if (!id || !status) continue;

        await tx
          .update(attendanceRecords)
          .set({
            status,
            notes: notes || null,
            recordedBy: teacherId,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(attendanceRecords.id, id));
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Absensi berhasil diperbarui',
    });
  } catch (error) {
    console.error('Update attendance error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal memperbarui absensi' }
    }, { status: 500 });
  }
}

// DELETE - Delete attendance records
export async function DELETE(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const attendanceIds = searchParams.get('ids')?.split(',').map(id => parseInt(id));
    const classroomId = searchParams.get('classroomId');
    const subjectId = searchParams.get('subjectId');
    const date = searchParams.get('date');

    if (attendanceIds && attendanceIds.length > 0) {
      // Delete specific attendance records
      await db.transaction(async (tx) => {
        for (const id of attendanceIds) {
          await tx
            .delete(attendanceRecords)
            .where(eq(attendanceRecords.id, id));
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Absensi berhasil dihapus',
      });
    }

    if (classroomId && subjectId && date) {
      // Delete all attendance for a specific date
      const classroomSubjectResult = await db
        .select()
        .from(classroomSubjects)
        .where(and(
          eq(classroomSubjects.classroomId, parseInt(classroomId)),
          eq(classroomSubjects.subjectId, parseInt(subjectId))
        ))
        .limit(1);

      if (classroomSubjectResult.length === 0) {
        return NextResponse.json({
          success: false,
          error: { code: 'NOT_FOUND', message: 'Mata pelajaran tidak ditemukan di kelas ini' }
        }, { status: 404 });
      }

      await db
        .delete(attendanceRecords)
        .where(and(
          eq(attendanceRecords.classroomId, parseInt(classroomId)),
          eq(attendanceRecords.date, date)
        ));

      return NextResponse.json({
        success: true,
        message: 'Absensi berhasil dihapus',
      });
    }

    return NextResponse.json({
      success: false,
      error: { code: 'MISSING_PARAMS', message: 'Parameter tidak lengkap' }
    }, { status: 400 });
  } catch (error) {
    console.error('Delete attendance error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal menghapus absensi' }
    }, { status: 500 });
  }
}