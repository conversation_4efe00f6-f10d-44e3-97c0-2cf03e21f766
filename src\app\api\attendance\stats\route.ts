import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { 
  attendanceRecords, 
  students, 
  users, 
  classroomSubjects, 
  subjects, 
  classrooms
} from '@/db/schema';
import { eq, and, gte, sql, count } from 'drizzle-orm';

// GET - Get attendance statistics
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: '<PERSON><PERSON><PERSON> dito<PERSON>' }
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const classroomId = searchParams.get('classroomId');
    const subjectId = searchParams.get('subjectId');
    const days = parseInt(searchParams.get('days') || '30');

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];

    // Build base query conditions
    const conditions = [
      gte(attendanceRecords.date, startDateStr!)
    ];

    if (classroomId) {
      conditions.push(eq(attendanceRecords.classroomId, parseInt(classroomId)));
    }

    // Get overall statistics
    const overallStats = await db
      .select({
        totalRecords: count(),
        presentCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'H' THEN 1 ELSE 0 END)`,
        izinCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'I' THEN 1 ELSE 0 END)`,
        sakitCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'S' THEN 1 ELSE 0 END)`,
        alpaCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'A' THEN 1 ELSE 0 END)`,
      })
      .from(attendanceRecords)
      .where(and(...conditions));

    const stats = overallStats[0];
    const totalRecords = stats?.totalRecords || 0;
    const presentCount = stats?.presentCount || 0;
    const overallAttendanceRate = totalRecords > 0 ? (presentCount / totalRecords) * 100 : 0;

    // Get unique students count
    const uniqueStudents = await db
      .select({
        count: sql<number>`COUNT(DISTINCT ${attendanceRecords.studentId})`,
      })
      .from(attendanceRecords)
      .where(and(...conditions));

    const totalStudents = uniqueStudents[0]?.count || 0;

    // Get unique dates count
    const uniqueDates = await db
      .select({
        count: sql<number>`COUNT(DISTINCT ${attendanceRecords.date})`,
      })
      .from(attendanceRecords)
      .where(and(...conditions));

    const totalDays = uniqueDates[0]?.count || 0;

    // Calculate monthly trends (last 2 months for comparison)
    const twoMonthsAgo = new Date();
    twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 2);
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    const thisMonthStats = await db
      .select({
        totalRecords: count(),
        presentCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'H' THEN 1 ELSE 0 END)`,
      })
      .from(attendanceRecords)
      .where(and(
        ...conditions,
        gte(attendanceRecords.date, oneMonthAgo.toISOString().split('T')[0]!)
      ));

    const lastMonthStats = await db
      .select({
        totalRecords: count(),
        presentCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'H' THEN 1 ELSE 0 END)`,
      })
      .from(attendanceRecords)
      .where(and(
        ...conditions,
        gte(attendanceRecords.date, twoMonthsAgo.toISOString().split('T')[0]!),
        sql`${attendanceRecords.date} < ${oneMonthAgo.toISOString().split('T')[0]!}`
      ));

    const thisMonthRate = thisMonthStats[0]?.totalRecords && thisMonthStats[0]?.presentCount
      ? (thisMonthStats[0].presentCount / thisMonthStats[0].totalRecords) * 100 
      : 0;
    
    const lastMonthRate = lastMonthStats[0]?.totalRecords && lastMonthStats[0]?.presentCount
      ? (lastMonthStats[0].presentCount / lastMonthStats[0].totalRecords) * 100 
      : 0;

    const trendChange = thisMonthRate - lastMonthRate;

    // Get monthly breakdown for the last few months
    const monthlyStats = [];
    for (let i = 2; i >= 0; i--) {
      const monthStart = new Date();
      monthStart.setMonth(monthStart.getMonth() - i);
      monthStart.setDate(1);
      
      const monthEnd = new Date(monthStart);
      monthEnd.setMonth(monthEnd.getMonth() + 1);
      monthEnd.setDate(0);

      const monthlyData = await db
        .select({
          totalRecords: count(),
          presentCount: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'H' THEN 1 ELSE 0 END)`,
        })
        .from(attendanceRecords)
        .where(and(
          ...conditions,
          gte(attendanceRecords.date, monthStart.toISOString().split('T')[0]!),
          sql`${attendanceRecords.date} <= ${monthEnd.toISOString().split('T')[0]!}`
        ));

      const monthData = monthlyData[0];
      const attendanceRate = monthData?.totalRecords && monthData?.presentCount
        ? (monthData.presentCount / monthData.totalRecords) * 100 
        : 0;

      monthlyStats.push({
        month: monthStart.toLocaleDateString('id-ID', { month: 'long', year: 'numeric' }),
        attendanceRate,
        totalRecords: monthData?.totalRecords || 0,
        presentCount: monthData?.presentCount || 0,
      });
    }

    const responseData = {
      totalStudents,
      totalDays,
      overallAttendanceRate,
      statusBreakdown: {
        H: stats?.presentCount || 0,
        I: stats?.izinCount || 0,
        S: stats?.sakitCount || 0,
        A: stats?.alpaCount || 0,
      },
      trends: {
        thisMonth: thisMonthRate,
        lastMonth: lastMonthRate,
        change: trendChange,
      },
      monthlyStats,
    };

    return NextResponse.json({
      success: true,
      data: responseData,
    });
  } catch (error) {
    console.error('Get attendance stats error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal mengambil statistik absensi' }
    }, { status: 500 });
  }
}