import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser, deleteSession, deleteSessionCookie, getSessionCookie } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Get current session
    const sessionId = await getSessionCookie();
    
    if (sessionId) {
      // Delete session from database
      await deleteSession(sessionId);
    }
    
    // Delete session cookie
    await deleteSessionCookie();
    
    return NextResponse.json({
      success: true,
      message: 'Logout berhasil',
    });
  } catch (error) {
    console.error('Logout error:', error);
    
    // Even if there's an error, we should still clear the cookie
    await deleteSessionCookie();
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'LOGOUT_ERROR',
          message: '<PERSON><PERSON><PERSON><PERSON> kesalahan saat logout',
        },
      },
      { status: 500 }
    );
  }
}