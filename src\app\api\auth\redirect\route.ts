import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      // No valid session, redirect to login
      return NextResponse.redirect(new URL('/login', request.url));
    }
    
    // Redirect based on user role
    if (user.role === 'teacher') {
      return NextResponse.redirect(new URL('/guru', request.url));
    } else if (user.role === 'student') {
      return NextResponse.redirect(new URL('/siswa', request.url));
    } else {
      // Unknown role, redirect to login
      return NextResponse.redirect(new URL('/login', request.url));
    }
  } catch (error) {
    console.error('Redirect error:', error);
    return NextResponse.redirect(new URL('/login', request.url));
  }
}