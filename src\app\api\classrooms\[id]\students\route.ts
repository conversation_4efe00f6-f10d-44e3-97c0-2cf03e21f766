import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { students, users } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

// GET - Get students in a classroom
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: '<PERSON><PERSON><PERSON> dito<PERSON>' }
      }, { status: 401 });
    }

    const classroomId = parseInt(params.id);

    // Get students enrolled in this classroom
    const studentsResult = await db
      .select({
        id: students.id,
        name: users.name,
        studentId: students.studentId,
        enrolledAt: students.enrollmentDate,
      })
      .from(students)
      .innerJoin(users, eq(students.userId, users.id))
      .where(and(
        eq(students.classroomId, classroomId),
        eq(students.isActive, true),
        eq(users.isActive, true)
      ))
      .orderBy(users.name);

    return NextResponse.json({
      success: true,
      data: studentsResult,
    });
  } catch (error) {
    console.error('Get classroom students error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal mengambil data siswa kelas' }
    }, { status: 500 });
  }
}