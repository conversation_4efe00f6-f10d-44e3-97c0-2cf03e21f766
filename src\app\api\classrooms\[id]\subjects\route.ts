import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { classroomSubjects, subjects, users } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

// GET - Get subjects for a classroom
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: '<PERSON><PERSON><PERSON>to<PERSON>' }
      }, { status: 401 });
    }

    const classroomId = parseInt(params.id);

    // Get subjects assigned to this classroom
    const classroomSubjectsResult = await db
      .select({
        id: classroomSubjects.id,
        name: subjects.name,
        code: subjects.code,
        teacher: {
          id: users.id,
          name: users.name,
        },
        // Note: schedule field not available in current schema
      })
      .from(classroomSubjects)
      .innerJoin(subjects, eq(classroomSubjects.subjectId, subjects.id))
      .leftJoin(users, eq(classroomSubjects.teacherId, users.id))
      .where(and(
        eq(classroomSubjects.classroomId, classroomId),
        // Note: isActive field not available in current schema
      ))
      .orderBy(subjects.name);

    return NextResponse.json({
      success: true,
      data: classroomSubjectsResult.map(cs => ({
        id: cs.id,
        name: cs.name,
        code: cs.code,
        teacher: cs.teacher,
        // Note: schedule field not available in current schema
      })),
    });
  } catch (error) {
    console.error('Get classroom subjects error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal mengambil mata pelajaran kelas' }
    }, { status: 500 });
  }
}