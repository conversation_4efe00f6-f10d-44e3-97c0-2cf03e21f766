import { NextRequest } from 'next/server';
import { withTeacher, createSuccessResponse, createErrorResponse } from '@/lib/api-auth';

// GET /api/classrooms - Get all classrooms (teacher only)
export const GET = withTeacher(async (request: NextRequest, user) => {
  try {
    // TODO: Implement classroom fetching logic
    // For now, return mock data
    const classrooms = [
      {
        id: 1,
        name: 'Kelas 5A',
        grade: '5',
        academicYear: '2024/2025',
        homeroomTeacherId: user.id,
        isActive: true,
        studentCount: 25,
      },
    ];

    return createSuccessResponse({
      classrooms,
      total: classrooms.length,
    });
  } catch (error) {
    console.error('Get classrooms error:', error);
    return createErrorResponse(
      'FETCH_ERROR',
      'Failed to fetch classrooms',
      500
    );
  }
});

// POST /api/classrooms - Create new classroom (teacher only)
export const POST = withTeacher(async (request: NextRequest, user) => {
  try {
    const body = await request.json();
    
    // TODO: Implement classroom creation logic
    // For now, return mock response
    const newClassroom = {
      id: Date.now(),
      ...body,
      homeroomTeacherId: user.id,
      isActive: true,
      createdAt: new Date().toISOString(),
    };

    return createSuccessResponse(newClassroom, 201);
  } catch (error) {
    console.error('Create classroom error:', error);
    return createErrorResponse(
      'CREATE_ERROR',
      'Failed to create classroom',
      500
    );
  }
});