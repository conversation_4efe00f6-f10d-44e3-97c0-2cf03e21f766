import { NextRequest, NextResponse } from 'next/server';
import { eq } from 'drizzle-orm';
import { db } from '@/lib/db';
import { schools, users } from '@/db/schema';
import { hashPassword, createSession, setSessionCookie } from '@/lib/auth';
import { schoolOnboardingSchema } from '@/lib/validations';

export async function POST(request: NextRequest) {
  try {
    // Check if onboarding has already been completed
    const existingSchools = await db.select().from(schools).limit(1);
    
    if (existingSchools.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'ALREADY_ONBOARDED',
            message: 'Onboarding sudah pernah dilakukan sebelumnya',
          },
        },
        { status: 400 }
      );
    }

    // Parse form data
    const formData = await request.formData();
    
    const onboardingData = {
      schoolName: formData.get('schoolName') as string,
      schoolLevel: formData.get('schoolLevel') as string,
      teacherName: formData.get('teacherName') as string,
      teacherEmail: formData.get('teacherEmail') as string,
      teacherPassword: formData.get('teacherPassword') as string,
    };

    // Get uploaded files
    const logoFile = formData.get('logo') as File | null;
    const signatureFile = formData.get('signature') as File | null;

    // Validate the data
    const validationResult = schoolOnboardingSchema.safeParse(onboardingData);
    if (!validationResult.success) {
      const firstError = validationResult.error.errors[0];
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: firstError?.message || 'Data tidak valid',
            details: validationResult.error.errors,
          },
        },
        { status: 400 }
      );
    }

    const { schoolName, schoolLevel, teacherName, teacherEmail, teacherPassword } = validationResult.data;

    // Check if teacher email already exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.email, teacherEmail.toLowerCase()))
      .limit(1);

    if (existingUser.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'EMAIL_EXISTS',
            message: 'Email sudah terdaftar dalam sistem',
          },
        },
        { status: 400 }
      );
    }

    // TODO: Handle file uploads (logo and signature)
    // For now, we'll store null values
    let logoUrl: string | null = null;
    let signatureUrl: string | null = null;

    // In a real implementation, you would:
    // 1. Validate file types and sizes
    // 2. Upload files to storage (S3, Cloudflare R2, etc.)
    // 3. Get the URLs of uploaded files
    
    if (logoFile && logoFile.size > 0) {
      // TODO: Implement file upload logic
      console.log('Logo file received:', logoFile.name, logoFile.size);
    }
    
    if (signatureFile && signatureFile.size > 0) {
      // TODO: Implement file upload logic
      console.log('Signature file received:', signatureFile.name, signatureFile.size);
    }

    // Start database transaction
    const result = await db.transaction(async (tx) => {
      // Create school record
      const schoolResult = await tx.insert(schools).values({
        name: schoolName,
        level: schoolLevel as 'SD' | 'SMP' | 'SMA' | 'SMK',
        logo: logoUrl,
        signature: signatureUrl,
      }).returning();

      if (schoolResult.length === 0) {
        throw new Error('Failed to create school record');
      }

      const school = schoolResult[0];

      // Hash password
      const passwordHash = await hashPassword(teacherPassword);

      // Create teacher user with superadmin privileges
      const userResult = await tx.insert(users).values({
        email: teacherEmail.toLowerCase(),
        passwordHash,
        role: 'teacher',
        name: teacherName,
        isActive: true,
        isSuperAdmin: true,
      }).returning();

      if (userResult.length === 0) {
        throw new Error('Failed to create teacher account');
      }

      const teacher = userResult[0];

      return { school, teacher };
    });

    // Create session for the new teacher
    if (!result.teacher) {
      throw new Error('Teacher account not created');
    }
    if (!result.school) {
      throw new Error('School not created');
    }
    const sessionId = await createSession(result.teacher.id);
    await setSessionCookie(sessionId);

    return NextResponse.json({
      success: true,
      data: {
        school: {
          id: result.school.id,
          name: result.school.name,
          level: result.school.level,
        },
        teacher: {
          id: result.teacher.id,
          name: result.teacher.name,
          email: result.teacher.email,
          role: result.teacher.role,
          isSuperAdmin: result.teacher.isSuperAdmin,
        },
        message: 'Onboarding berhasil diselesaikan',
      },
    });

  } catch (error) {
    console.error('Onboarding error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Terjadi kesalahan saat proses onboarding',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      },
      { status: 500 }
    );
  }
}

// GET /api/onboarding - Check onboarding status
export async function GET(request: NextRequest) {
  try {
    const existingSchools = await db.select().from(schools).limit(1);
    
    return NextResponse.json({
      success: true,
      data: {
        isOnboarded: existingSchools.length > 0,
        schoolCount: existingSchools.length,
      },
    });
  } catch (error) {
    console.error('Check onboarding status error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Gagal memeriksa status onboarding',
        },
      },
      { status: 500 }
    );
  }
}