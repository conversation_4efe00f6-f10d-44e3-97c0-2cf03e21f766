import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { 
  attendanceRecords, 
  students, 
  users, 
  classroomSubjects, 
  subjects, 
  classrooms
} from '@/db/schema';
import { eq, and, gte, lte, sql } from 'drizzle-orm';
import { 
  generateCSVResponse,
  attendanceCSVColumns,
  studentListCSVColumns,
  attendanceSummaryCSVColumns,
  classroomCSVColumns,
  formatDateForCSV
} from '@/lib/csv-export';

// GET - Generate CSV exports
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const exportType = searchParams.get('type'); // 'attendance' | 'students' | 'attendance-summary' | 'classrooms'
    const classroomId = searchParams.get('classroomId');
    const subjectId = searchParams.get('subjectId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (!exportType) {
      return NextResponse.json({
        success: false,
        error: { code: 'MISSING_PARAMS', message: 'Tipe export tidak ditentukan' }
      }, { status: 400 });
    }

    switch (exportType) {
      case 'attendance':
        return await exportAttendanceData({
          classroomId: classroomId ? parseInt(classroomId) : undefined,
          subjectId: subjectId ? parseInt(subjectId) : undefined,
          startDate: startDate || undefined,
          endDate: endDate || undefined,
        });

      case 'students':
        return await exportStudentData({
          classroomId: classroomId ? parseInt(classroomId) : undefined,
        });

      case 'attendance-summary':
        return await exportAttendanceSummaryData({
          classroomId: classroomId ? parseInt(classroomId) : undefined,
          subjectId: subjectId ? parseInt(subjectId) : undefined,
          startDate: startDate || undefined,
          endDate: endDate || undefined,
        });

      case 'classrooms':
        return await exportClassroomData();

      default:
        return NextResponse.json({
          success: false,
          error: { code: 'INVALID_TYPE', message: 'Tipe export tidak valid' }
        }, { status: 400 });
    }

  } catch (error) {
    console.error('CSV Export error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal membuat export CSV' }
    }, { status: 500 });
  }
}

async function exportAttendanceData({
  classroomId,
  subjectId,
  startDate,
  endDate,
}: {
  classroomId?: number;
  subjectId?: number;
  startDate?: string;
  endDate?: string;
}) {
  // Build query with filters
  const conditions = [];
  if (classroomId) {
    conditions.push(eq(classrooms.id, classroomId));
  }
  if (startDate) {
    conditions.push(gte(attendanceRecords.date, startDate));
  }
  if (endDate) {
    conditions.push(lte(attendanceRecords.date, endDate));
  }

  const query = db
    .select({
      date: attendanceRecords.date,
      studentName: users.name,
      studentId: students.studentId,
      className: classrooms.name,
      status: attendanceRecords.status,
      notes: attendanceRecords.notes,
      markedAt: attendanceRecords.createdAt,
    })
    .from(attendanceRecords)
    .innerJoin(students, eq(attendanceRecords.studentId, students.id))
    .innerJoin(users, eq(students.userId, users.id))
    .innerJoin(classrooms, eq(attendanceRecords.classroomId, classrooms.id))
    .where(conditions.length > 0 ? and(...conditions) : undefined);

  const data = await query;

  // Generate filename
  let filename = 'Data_Absensi';
  if (classroomId && subjectId) {
    const classInfo = await db
      .select({ className: classrooms.name, subjectName: subjects.name })
      .from(classrooms)
      .innerJoin(classroomSubjects, eq(classrooms.id, classroomSubjects.classroomId))
      .innerJoin(subjects, eq(classroomSubjects.subjectId, subjects.id))
      .where(and(eq(classrooms.id, classroomId), eq(subjects.id, subjectId)))
      .limit(1);
    
        if (classInfo.length > 0) {    
      const info = classInfo[0];
      if (info?.className) {
        filename += `_${info.className}`;
      }
    }
  }
  
  if (startDate && endDate) {
    filename += `_${startDate}_${endDate}`;
  }
  
  filename += `_${formatDateForCSV(new Date()).replace(/\//g, '-')}.csv`;

  return generateCSVResponse({
    filename,
    columns: attendanceCSVColumns,
    data,
    includeTimestamp: true,
  });
}

async function exportStudentData({
  classroomId,
}: {
  classroomId?: number;
}) {
  const conditions = [];
  if (classroomId) {
    conditions.push(eq(classrooms.id, classroomId));
  }

  const query = db
    .select({
      studentId: students.studentId,
      name: users.name,
      email: users.email,
      className: classrooms.name,
      grade: classrooms.grade,
      isActive: users.isActive,
      createdAt: users.createdAt,
    })
    .from(students)
    .innerJoin(users, eq(students.userId, users.id))
    .innerJoin(classrooms, eq(students.classroomId, classrooms.id))
    .where(conditions.length > 0 ? and(...conditions) : undefined);

  const data = await query;

  // Generate filename
  let filename = 'Daftar_Siswa';
  if (classroomId) {
    const classInfo = await db
      .select({ name: classrooms.name })
      .from(classrooms)
      .where(eq(classrooms.id, classroomId))
      .limit(1);
    
    if (classInfo.length > 0) {
      const info = classInfo[0];
      if (info?.name) {
        filename += `_${info.name}`;
      }
    }
  }
  
  filename += `_${formatDateForCSV(new Date()).replace(/\//g, '-')}.csv`;

  return generateCSVResponse({
    filename,
    columns: studentListCSVColumns,
    data,
    includeTimestamp: true,
  });
}

async function exportAttendanceSummaryData({
  classroomId,
  subjectId,
  startDate,
  endDate,
}: {
  classroomId?: number;
  subjectId?: number;
  startDate?: string;
  endDate?: string;
}) {
  // Get students
  const conditions = [];
  if (classroomId) {
    conditions.push(eq(classrooms.id, classroomId));
  }

  const studentsQuery = db
    .select({
      id: students.id,
      studentName: users.name,
      studentId: students.studentId,
      className: classrooms.name,
    })
    .from(students)
    .innerJoin(users, eq(students.userId, users.id))
    .innerJoin(classrooms, eq(students.classroomId, classrooms.id))
    .where(conditions.length > 0 ? and(...conditions) : undefined);

  const studentsData = await studentsQuery;

  // Get attendance summary for each student
  const summaryData = await Promise.all(
    studentsData.map(async (student) => {
      let attendanceQuery = db
        .select({
          total: sql<number>`count(*)`,
          present: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'H' THEN 1 ELSE 0 END)`,
          excused: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'I' THEN 1 ELSE 0 END)`,
          sick: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'S' THEN 1 ELSE 0 END)`,
          absent: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'A' THEN 1 ELSE 0 END)`,
        })
        .from(attendanceRecords)
        .innerJoin(classrooms, eq(attendanceRecords.classroomId, classrooms.id));

      const conditions = [eq(attendanceRecords.studentId, student.id)];
      
      if (subjectId) {
        conditions.push(eq(classroomSubjects.subjectId, subjectId));
      }
      if (startDate) {
        conditions.push(gte(attendanceRecords.date, startDate));
      }
      if (endDate) {
        conditions.push(lte(attendanceRecords.date, endDate));
      }

      const summary = await attendanceQuery.where(and(...conditions));
            const stats = summary[0];     
      
      const attendanceRate = stats?.total && stats?.present
        ? (stats.present / stats.total) * 100 
        : 0;

      return {
        studentName: student.studentName,
        studentId: student.studentId,
        className: student.className,
        totalDays: stats?.total || 0,
        presentDays: stats?.present || 0,
        excusedDays: stats?.excused || 0,
        sickDays: stats?.sick || 0,
        absentDays: stats?.absent || 0,
        attendanceRate,
      };
    })
  );

  // Generate filename
  let filename = 'Ringkasan_Absensi';
  if (classroomId) {
    const classInfo = await db
      .select({ name: classrooms.name })
      .from(classrooms)
      .where(eq(classrooms.id, classroomId))
      .limit(1);
    
    if (classInfo.length > 0) {
      const info = classInfo[0];
      if (info?.name) {
        filename += `_${info.name}`;
      }
    }
  }
  
  if (startDate && endDate) {
    filename += `_${startDate}_${endDate}`;
  }
  
  filename += `_${formatDateForCSV(new Date()).replace(/\//g, '-')}.csv`;

  return generateCSVResponse({
    filename,
    columns: attendanceSummaryCSVColumns,
    data: summaryData,
    includeTimestamp: true,
  });
}

async function exportClassroomData() {
  const data = await db
    .select({
      name: classrooms.name,
      grade: classrooms.grade,
      academicYear: classrooms.academicYear,
      homeroomTeacher: sql<string>`COALESCE(${users.name}, 'Belum ditentukan')`,
      studentCount: sql<number>`(
        SELECT COUNT(*) 
        FROM ${students} 
        WHERE ${students.classroomId} = ${classrooms.id}
      )`,
      isActive: classrooms.isActive,
      createdAt: classrooms.createdAt,
    })
    .from(classrooms)
    .leftJoin(users, eq(classrooms.homeroomTeacherId, users.id));

  const filename = `Daftar_Kelas_${formatDateForCSV(new Date()).replace(/\//g, '-')}.csv`;

  return generateCSVResponse({
    filename,
    columns: classroomCSVColumns,
    data,
    includeTimestamp: true,
  });
}