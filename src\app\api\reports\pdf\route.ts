import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { db } from '@/lib/db';
import { 
  attendanceRecords, 
  students, 
  users, 
  classroomSubjects, 
  subjects, 
  classrooms,
  schools
} from '@/db/schema';
import { eq, and, gte, lte, sql } from 'drizzle-orm';
import { renderToBuffer } from '@react-pdf/renderer';
import React from 'react';
import { AttendanceReportPDF, StudentReportPDF } from '@/lib/pdf-templates';

// GET - Generate PDF reports
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== 'teacher') {
      return NextResponse.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Akses ditolak' }
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const reportType = searchParams.get('type'); // 'attendance' | 'student'
    const classroomId = searchParams.get('classroomId');
    const subjectId = searchParams.get('subjectId');
    const studentId = searchParams.get('studentId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (!reportType) {
      return NextResponse.json({
        success: false,
        error: { code: 'MISSING_PARAMS', message: 'Tipe laporan tidak ditentukan' }
      }, { status: 400 });
    }

    // Get school information
    const schoolInfo = await db.select().from(schools).limit(1);
    const school = schoolInfo[0] || { name: 'Sekolah', logo: null, signature: null };

    // Get teacher information (teachers are users with role 'teacher')
    const teacher = {
      id: user.id,
      name: user.name,
    };

    if (reportType === 'attendance') {
      return await generateAttendanceReport({
        classroomId: parseInt(classroomId!),
        subjectId: parseInt(subjectId!),
        startDate: startDate!,
        endDate: endDate!,
        school,
        teacher,
      });
    }

    if (reportType === 'student') {
      return await generateStudentReport({
        studentId: parseInt(studentId!),
        school,
      });
    }

    return NextResponse.json({
      success: false,
      error: { code: 'INVALID_TYPE', message: 'Tipe laporan tidak valid' }
    }, { status: 400 });

  } catch (error) {
    console.error('Generate PDF error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal membuat laporan PDF' }
    }, { status: 500 });
  }
}

async function generateAttendanceReport({
  classroomId,
  subjectId,
  startDate,
  endDate,
  school,
  teacher,
}: {
  classroomId: number;
  subjectId: number;
  startDate: string;
  endDate: string;
  school: any;
  teacher: any;
}) {
  // Get classroom and subject info
  const classroomSubjectInfo = await db
    .select({
      classroomSubjectId: classroomSubjects.id,
      classroomName: classrooms.name,
      classroomGrade: classrooms.grade,
      subjectName: subjects.name,
    })
    .from(classroomSubjects)
    .innerJoin(classrooms, eq(classroomSubjects.classroomId, classrooms.id))
    .innerJoin(subjects, eq(classroomSubjects.subjectId, subjects.id))
    .where(and(
      eq(classroomSubjects.classroomId, classroomId),
      eq(classroomSubjects.subjectId, subjectId)
    ))
    .limit(1);

  if (classroomSubjectInfo.length === 0) {
    throw new Error('Kelas atau mata pelajaran tidak ditemukan');
  }

  const classroomSubject = classroomSubjectInfo[0];
  if (!classroomSubject) {
    throw new Error('Kelas atau mata pelajaran tidak ditemukan');
  }

  // Get all students in the classroom
  const studentsInClass = await db
    .select({
      id: students.id,
      name: users.name,
      studentId: students.studentId,
    })
    .from(students)
    .innerJoin(users, eq(students.userId, users.id))
    .where(eq(students.classroomId, classroomId));

  // Get attendance records for the period
  const attendanceData = await db
    .select({
      studentId: attendanceRecords.studentId,
      date: attendanceRecords.date,
      status: attendanceRecords.status,
      notes: attendanceRecords.notes,
    })
    .from(attendanceRecords)
    .where(and(
      eq(attendanceRecords.classroomId, classroomId),
      gte(attendanceRecords.date, startDate),
      lte(attendanceRecords.date, endDate)
    ));

  // Process data for PDF
  const studentsWithAttendance = studentsInClass.map(student => {
    const studentAttendance = attendanceData.filter(record => record.studentId === student.id);
    
    const summary = {
      H: studentAttendance.filter(r => r.status === 'H').length,
      I: studentAttendance.filter(r => r.status === 'I').length,
      S: studentAttendance.filter(r => r.status === 'S').length,
      A: studentAttendance.filter(r => r.status === 'A').length,
      total: studentAttendance.length,
      percentage: 0,
    };
    
    summary.percentage = summary.total > 0 ? (summary.H / summary.total) * 100 : 0;

    return {
      id: student.id,
      name: student.name,
      studentId: student.studentId,
      attendanceRecords: studentAttendance.map(record => ({
        date: record.date,
        status: record.status as 'H' | 'I' | 'S' | 'A',
        notes: record.notes || undefined,
      })),
      summary,
    };
  });

  // Calculate overall statistics
  const totalRecords = attendanceData.length;
  const statusBreakdown = {
    H: attendanceData.filter(r => r.status === 'H').length,
    I: attendanceData.filter(r => r.status === 'I').length,
    S: attendanceData.filter(r => r.status === 'S').length,
    A: attendanceData.filter(r => r.status === 'A').length,
  };

  const overallAttendanceRate = totalRecords > 0 ? (statusBreakdown.H / totalRecords) * 100 : 0;

  // Get unique dates
  const uniqueDates = Array.from(new Set(attendanceData.map(r => r.date))).sort();

  const reportData = {
    school: {
      name: school.name,
      logo: school.logo,
      signature: school.signature,
    },
    classroom: {
      name: classroomSubject.classroomName,
      grade: classroomSubject.classroomGrade,
    },
    subject: {
      name: classroomSubject.subjectName,
    },
    teacher: {
      name: teacher.name,
    },
    period: {
      startDate,
      endDate,
    },
    students: studentsWithAttendance,
    summary: {
      totalStudents: studentsInClass.length,
      totalDays: uniqueDates.length,
      overallAttendanceRate,
      statusBreakdown,
    },
  };

  // Generate PDF
  const pdfBuffer = await renderToBuffer(React.createElement(AttendanceReportPDF, { data: reportData }) as any);

  // Return PDF response
  const filename = `Laporan_Absensi_${classroomSubject.classroomName}_${classroomSubject.subjectName}_${startDate}_${endDate}.pdf`;
  
  return new NextResponse(pdfBuffer as any, {
    headers: {
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${encodeURIComponent(filename)}"`,
      'Content-Length': pdfBuffer.length.toString(),
    },
  });
}

async function generateStudentReport({
  studentId,
  school,
}: {
  studentId: number;
  school: any;
}) {
  // Get student information
  const studentInfo = await db
    .select({
      id: students.id,
      name: users.name,
      studentId: students.studentId,
      classroomName: classrooms.name,
      classroomGrade: classrooms.grade,
    })
    .from(students)
    .innerJoin(users, eq(students.userId, users.id))
    .innerJoin(classrooms, eq(students.classroomId, classrooms.id))
    .where(eq(students.id, studentId))
    .limit(1);

  if (studentInfo.length === 0) {
    throw new Error('Data siswa tidak ditemukan');
  }

  const student = studentInfo[0];
  if (!student) {
    throw new Error('Data siswa tidak ditemukan');
  }

  // Get student's attendance summary
  const attendanceSummary = await db
    .select({
      total: sql<number>`count(*)`,
      present: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'H' THEN 1 ELSE 0 END)`,
      excused: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'I' THEN 1 ELSE 0 END)`,
      sick: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'S' THEN 1 ELSE 0 END)`,
      absent: sql<number>`SUM(CASE WHEN ${attendanceRecords.status} = 'A' THEN 1 ELSE 0 END)`,
    })
    .from(attendanceRecords)
    .where(eq(attendanceRecords.studentId, studentId));

  const attendance = attendanceSummary[0];
  if (!attendance) {
    throw new Error('Data absensi tidak ditemukan');
  }
  const attendanceRate = attendance.total > 0 ? (attendance.present / attendance.total) * 100 : 0;

  // Mock data for subjects and gamification (in real implementation, get from database)
  const mockSubjects = [
    {
      name: 'Matematika',
      teacher: 'Pak Budi',
      grades: [],
      finalGrade: 85,
      letterGrade: 'B',
      attendanceRate: 92,
    },
    {
      name: 'IPA',
      teacher: 'Bu Sari',
      grades: [],
      finalGrade: 88,
      letterGrade: 'B+',
      attendanceRate: 95,
    },
  ];

  const mockGamification = {
    totalXP: 2450,
    level: 8,
    badges: [
      {
        name: 'Rajin Hadir',
        description: 'Hadir 30 hari berturut-turut',
        earnedAt: new Date().toISOString(),
      },
    ],
  };

  const reportData = {
    school: {
      name: school.name,
      logo: school.logo,
    },
    student: {
      name: student.name,
      studentId: student.studentId,
      className: student.classroomName,
      grade: student.classroomGrade,
    },
    period: {
      semester: 'Ganjil',
      academicYear: '2024/2025',
    },
    subjects: mockSubjects,
    attendance: {
      totalDays: attendance.total,
      present: attendance.present,
      excused: attendance.excused,
      sick: attendance.sick,
      absent: attendance.absent,
      rate: attendanceRate,
    },
    gamification: mockGamification,
  };

  // Generate PDF
  const pdfBuffer = await renderToBuffer(React.createElement(StudentReportPDF, { data: reportData }) as any);

  // Return PDF response
  const filename = `Rapor_${student.name}_${student.studentId}.pdf`;
  
  return new NextResponse(pdfBuffer as any, {
    headers: {
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${encodeURIComponent(filename)}"`,
      'Content-Length': pdfBuffer.length.toString(),
    },
  });
}