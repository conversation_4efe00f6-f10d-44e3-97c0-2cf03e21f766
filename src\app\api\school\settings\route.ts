import { NextRequest } from 'next/server';
import { withSuperAdmin, createSuccessResponse, createErrorResponse } from '@/lib/api-auth';

// GET /api/school/settings - Get school settings (superadmin only)
export const GET = withSuperAdmin(async (request: NextRequest, user) => {
  try {
    // TODO: Implement school settings fetching logic
    // For now, return mock data
    const schoolSettings = {
      id: 1,
      name: 'SD Test School',
      level: 'SD',
      logo: null,
      signature: null,
      address: 'Jl. Pendidikan No. 123',
      phone: '021-12345678',
      email: '<EMAIL>',
      academicYear: '2024/2025',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: new Date().toISOString(),
    };

    return createSuccessResponse(schoolSettings);
  } catch (error) {
    console.error('Get school settings error:', error);
    return createErrorResponse(
      'FETCH_ERROR',
      'Failed to fetch school settings',
      500
    );
  }
});

// PUT /api/school/settings - Update school settings (superadmin only)
export const PUT = withSuperAdmin(async (request: NextRequest, user) => {
  try {
    const body = await request.json();
    
    // TODO: Implement school settings update logic
    // For now, return mock response
    const updatedSettings = {
      id: 1,
      ...body,
      updatedAt: new Date().toISOString(),
      updatedBy: user.id,
    };

    return createSuccessResponse(updatedSettings);
  } catch (error) {
    console.error('Update school settings error:', error);
    return createErrorResponse(
      'UPDATE_ERROR',
      'Failed to update school settings',
      500
    );
  }
});