import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { schools } from '@/db/schema';

// GET /api/school/status - Check if school setup is complete (no auth required)
export async function GET() {
  try {
    const existingSchools = await db.select().from(schools).limit(1);
    
    return NextResponse.json({
      success: true,
      data: {
        isSetup: existingSchools.length > 0,
        school: existingSchools.length > 0 ? existingSchools[0] : null
      }
    });
  } catch (error) {
    console.error('Check school status error:', error);
    return NextResponse.json({
      success: false,
      error: { code: 'INTERNAL_ERROR', message: 'Gagal memeriksa status sekolah' }
    }, { status: 500 });
  }
}