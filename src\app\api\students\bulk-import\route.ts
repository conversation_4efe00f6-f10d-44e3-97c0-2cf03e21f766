import { NextRequest, NextResponse } from 'next/server';
import { withT<PERSON><PERSON>, createSuccessResponse, createErrorResponse } from '@/lib/api-auth';

// POST /api/students/bulk-import - Bulk import students from CSV (teacher only)
export const POST = withTeacher(async (request: NextRequest, user) => {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return createErrorResponse(
        'NO_FILE',
        'File CSV tidak ditemukan',
        400
      );
    }
    
    if (file.type !== 'text/csv') {
      return createErrorResponse(
        'INVALID_FILE_TYPE',
        'File harus berformat CSV',
        400
      );
    }
    
    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      return createErrorResponse(
        'FILE_TOO_LARGE',
        'Ukuran file maksimal 5MB',
        400
      );
    }
    
    // Read and parse CSV content
    const csvContent = await file.text();
    const lines = csvContent.split('\n').filter(line => line.trim());
    
    if (lines.length < 2) {
      return createErrorResponse(
        'EMPTY_FILE',
        'File CSV kosong atau tidak memiliki data',
        400
      );
    }
    
    // Parse header
    if (!lines[0]) {
      return createErrorResponse(
        'EMPTY_FILE',
        'File CSV kosong atau tidak memiliki header',
        400
      );
    }
    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    const requiredHeaders = ['nama', 'nis', 'email', 'tanggal_lahir', 'jenis_kelamin'];
    
    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
    if (missingHeaders.length > 0) {
      return createErrorResponse(
        'MISSING_HEADERS',
        `Header yang diperlukan tidak ditemukan: ${missingHeaders.join(', ')}`,
        400
      );
    }
    
    // Process data rows
    const results = {
      success: 0,
      failed: 0,
      errors: [] as Array<{
        row: number;
        error: string;
        data: any;
      }>,
    };
    
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i];
      if (!line) continue;
      const rowData = line.split(',').map(cell => cell.trim());
      const studentData: any = {};
      
      // Map CSV columns to student data
      headers.forEach((header, index) => {
        studentData[header] = rowData[index] || '';
      });
      
      try {
        // Validate required fields
        if (!studentData.nama || studentData.nama.length < 2) {
          throw new Error('Nama harus minimal 2 karakter');
        }
        
        if (!studentData.nis || studentData.nis.length < 6) {
          throw new Error('NIS harus minimal 6 digit');
        }
        
        if (!studentData.email || !studentData.email.includes('@')) {
          throw new Error('Format email tidak valid');
        }
        
        if (!studentData.tanggal_lahir || !isValidDate(studentData.tanggal_lahir)) {
          throw new Error('Format tanggal lahir tidak valid (gunakan YYYY-MM-DD)');
        }
        
        if (!['male', 'female'].includes(studentData.jenis_kelamin)) {
          throw new Error('Jenis kelamin harus "male" atau "female"');
        }
        
        // TODO: Check for duplicate email/NIS in database
        // TODO: Validate classroom ID if provided
        // TODO: Create student record in database
        
        // For now, simulate success
        results.success++;
        
      } catch (error) {
        results.failed++;
        results.errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Error tidak diketahui',
          data: studentData,
        });
      }
    }
    
    return createSuccessResponse({
      message: `Import selesai: ${results.success} berhasil, ${results.failed} gagal`,
      results,
    });
    
  } catch (error) {
    console.error('Bulk import error:', error);
    return createErrorResponse(
      'IMPORT_ERROR',
      'Terjadi kesalahan saat memproses file CSV',
      500
    );
  }
});

function isValidDate(dateString: string): boolean {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime()) && !!dateString.match(/^\d{4}-\d{2}-\d{2}$/);
}