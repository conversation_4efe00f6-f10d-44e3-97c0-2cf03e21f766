import { NextRequest, NextResponse } from 'next/server';
import { withTeacher, createSuccessResponse, createErrorResponse } from '@/lib/api-auth';

// GET /api/students - Get all students (teacher only)
export const GET = withTeacher(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const classroomId = searchParams.get('classroomId');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    // TODO: Implement student fetching logic with filters
    // For now, return mock data
    const students = [
      {
        id: 1,
        name: '<PERSON>',
        studentId: '2024001',
        email: '<EMAIL>',
        phone: '081234567890',
        dateOfBirth: '2012-05-15',
        gender: 'male',
        enrollmentDate: '2024-07-20',
        isActive: true,
        classrooms: [
          { id: 1, name: 'Kelas 5A', grade: '5' }
        ],
        totalXP: 2450,
        level: 8,
        attendanceRate: 95,
      },
    ];

    return createSuccessResponse({
      students,
      total: students.length,
      page,
      limit,
    });
  } catch (error) {
    console.error('Get students error:', error);
    return createErrorResponse(
      'FETCH_ERROR',
      'Failed to fetch students',
      500
    );
  }
});

// POST /api/students - Create new student (teacher only)
export const POST = withTeacher(async (request: NextRequest, user) => {
  try {
    const body = await request.json();
    
    // TODO: Validate student data using Zod schema
    // TODO: Check for duplicate email/studentId
    // TODO: Implement student creation logic
    
    // For now, return mock response
    const newStudent = {
      id: Date.now(),
      ...body,
      isActive: true,
      totalXP: 0,
      level: 1,
      attendanceRate: 0,
      createdAt: new Date().toISOString(),
    };

    return createSuccessResponse(newStudent, 201);
  } catch (error) {
    console.error('Create student error:', error);
    return createErrorResponse(
      'CREATE_ERROR',
      'Failed to create student',
      500
    );
  }
});