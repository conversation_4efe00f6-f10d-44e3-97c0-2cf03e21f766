@import "tailwindcss";

@theme {
  --color-background: hsl(0 0% 100%);
  --color-foreground: hsl(222.2 84% 4.9%);
  --color-card: hsl(0 0% 100%);
  --color-card-foreground: hsl(222.2 84% 4.9%);
  --color-popover: hsl(0 0% 100%);
  --color-popover-foreground: hsl(222.2 84% 4.9%);
  --color-primary: hsl(221.2 83.2% 53.3%);
  --color-primary-foreground: hsl(210 40% 98%);
  --color-secondary: hsl(210 40% 96%);
  --color-secondary-foreground: hsl(222.2 84% 4.9%);
  --color-muted: hsl(210 40% 96%);
  --color-muted-foreground: hsl(215.4 16.3% 46.9%);
  --color-accent: hsl(210 40% 96%);
  --color-accent-foreground: hsl(222.2 84% 4.9%);
  --color-destructive: hsl(0 84.2% 60.2%);
  --color-destructive-foreground: hsl(210 40% 98%);
  --color-border: hsl(214.3 31.8% 91.4%);
  --color-input: hsl(214.3 31.8% 91.4%);
  --color-ring: hsl(221.2 83.2% 53.3%);
  --radius: 0.5rem;
}

@layer base {
  * {
    border-color: var(--color-border);
  }
  
  body {
    background-color: var(--color-background);
    color: var(--color-foreground);
  }
}

/* Custom scrollbar styles */
@utility scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: var(--color-muted-foreground) var(--color-muted);
}

@utility scrollbar-thin {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--color-muted);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-muted-foreground);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--color-foreground);
  }
}

/* Indonesian typography improvements */
@layer base {
  html {
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Better text spacing for Indonesian content */
  p, li, td, th {
    text-align: justify;
    hyphens: auto;
    word-break: break-word;
  }

  /* Improved focus styles for accessibility */
  *:focus-visible {
    outline: 2px solid var(--color-ring);
    outline-offset: 2px;
  }
}

/* Animation utilities */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

@utility animate-in {
  animation: fadeIn 0.3s ease-out;
}

@utility animate-out {
  animation: fadeOut 0.3s ease-out;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-break-before {
    page-break-before: always;
  }

  .print-break-after {
    page-break-after: always;
  }

  .print-break-inside-avoid {
    page-break-inside: avoid;
  }
}/* R
esponsive Design Utilities */
@layer components {
  /* Touch-friendly interactive elements */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* Responsive text sizes */
  .text-responsive-xs {
    font-size: 0.75rem;
  }
  
  @media (min-width: 640px) {
    .text-responsive-xs {
      font-size: 0.875rem;
    }
  }
  
  .text-responsive-sm {
    font-size: 0.875rem;
  }
  
  @media (min-width: 640px) {
    .text-responsive-sm {
      font-size: 1rem;
    }
  }
  
  /* Mobile-first containers */
  .container-mobile {
    width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  @media (min-width: 640px) {
    .container-mobile {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }
  
  @media (min-width: 1024px) {
    .container-mobile {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
  
  /* Prevent zoom on iOS inputs */
  .no-zoom {
    font-size: 16px;
  }
  
  @media (min-width: 640px) {
    .no-zoom {
      font-size: 0.875rem;
    }
  }
  
  /* Mobile navigation styles */
  .mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    transition: color 0.2s;
    min-height: 44px;
  }
  
  /* Responsive grid patterns */
  .grid-responsive-1 {
    display: grid;
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .grid-responsive-2 {
    display: grid;
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  @media (min-width: 640px) {
    .grid-responsive-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  
  .grid-responsive-3 {
    display: grid;
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  @media (min-width: 640px) {
    .grid-responsive-3 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  
  @media (min-width: 1024px) {
    .grid-responsive-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
}

@layer utilities {
  /* Responsive visibility utilities */
  .mobile-only {
    display: block;
  }
  
  @media (min-width: 640px) {
    .mobile-only {
      display: none;
    }
  }
  
  .desktop-only {
    display: none;
  }
  
  @media (min-width: 1024px) {
    .desktop-only {
      display: block;
    }
  }
  
  .mobile-tablet-only {
    display: block;
  }
  
  @media (min-width: 1024px) {
    .mobile-tablet-only {
      display: none;
    }
  }
  
  .tablet-desktop-only {
    display: none;
  }
  
  @media (min-width: 640px) {
    .tablet-desktop-only {
      display: block;
    }
  }
}