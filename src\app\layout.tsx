import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { ErrorBoundary } from '@/components/ErrorBoundary';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'GuruFlow - Sistem Administrasi Sekolah Modern',
  description: 'Platform administrasi sekolah digital untuk mengelola kelas, absensi, dan tugas dengan mudah',
  keywords: ['sekolah', 'administrasi', 'absensi', 'guru', 'siswa', 'indonesia'],
  authors: [{ name: 'GuruFlow Team' }],
  creator: '<PERSON><PERSON><PERSON>',
  publisher: 'GuruFlow',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXTAUTH_URL || 'http://localhost:3000'),
  icons: {
    icon: '/favicon.svg',
  },
  openGraph: {
    title: 'GuruFlow - Sistem Administrasi Sekolah Modern',
    description: 'Platform administrasi sekolah digital untuk mengelola kelas, absensi, dan tugas dengan mudah',
    url: '/',
    siteName: 'GuruFlow',
    locale: 'id_ID',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'GuruFlow - Sistem Administrasi Sekolah Modern',
    description: 'Platform administrasi sekolah digital untuk mengelola kelas, absensi, dan tugas dengan mudah',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="id" suppressHydrationWarning>
      <body className={`${inter.className} antialiased`}>
        <ErrorBoundary>
          <div id="root">
            {children}
          </div>
        </ErrorBoundary>
      </body>
    </html>
  );
}