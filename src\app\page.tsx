import Link from 'next/link';
import { redirect } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  BookOpen, 
  Users, 
  Calendar, 
  BarChart3, 
  Shield, 
  Smartphone,
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import { getCurrentUser } from '@/lib/auth';
import { isSystemOnboarded } from '@/lib/onboarding';

export default async function HomePage() {
  // Check if user is authenticated
  const user = await getCurrentUser();
  
  if (user) {
    // User is logged in, redirect to appropriate dashboard
    if (user.role === 'teacher') {
      redirect('/guru');
    } else if (user.role === 'student') {
      redirect('/siswa');
    }
  }
  
  // Check if system needs onboarding
  const isOnboarded = await isSystemOnboarded();
  
  if (!isOnboarded) {
    redirect('/onboarding');
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-8 w-8 text-primary-600" />
              <span className="text-2xl font-bold text-gray-900">GuruFlow</span>
            </div>
            <nav className="hidden md:flex items-center space-x-6">
              <Link href="#fitur" className="text-gray-600 hover:text-primary-600 transition-colors">
                Fitur
              </Link>
              <Link href="#tentang" className="text-gray-600 hover:text-primary-600 transition-colors">
                Tentang
              </Link>
              <Link href="/login" className="text-gray-600 hover:text-primary-600 transition-colors">
                Masuk
              </Link>
              <Button asChild>
                <Link href="/onboarding">Mulai Sekarang</Link>
              </Button>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Sistem Administrasi Sekolah
            <span className="text-primary-600 block">Modern & Mudah</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            GuruFlow membantu guru dan siswa mengelola kelas, absensi, tugas, dan penilaian 
            dengan platform digital yang mudah digunakan dan sesuai standar Indonesia.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/onboarding">
                Mulai Gratis <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/login">Masuk ke Akun</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="fitur" className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Fitur Lengkap untuk Sekolah Indonesia
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Semua yang Anda butuhkan untuk mengelola administrasi sekolah dalam satu platform
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Calendar className="h-12 w-12 text-primary-600 mb-4" />
                <CardTitle>Absensi Digital</CardTitle>
                <CardDescription>
                  Sistem absensi dengan kode Indonesia (H/I/S/A) dan laporan otomatis
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Users className="h-12 w-12 text-primary-600 mb-4" />
                <CardTitle>Manajemen Kelas</CardTitle>
                <CardDescription>
                  Kelola kelas, mata pelajaran, dan siswa dengan mudah dan terorganisir
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <BookOpen className="h-12 w-12 text-primary-600 mb-4" />
                <CardTitle>Tugas & Penilaian</CardTitle>
                <CardDescription>
                  Buat tugas, terima pengumpulan, dan berikan nilai dengan sistem yang terintegrasi
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <BarChart3 className="h-12 w-12 text-primary-600 mb-4" />
                <CardTitle>Laporan & Analitik</CardTitle>
                <CardDescription>
                  Export laporan PDF dan CSV untuk kebutuhan administrasi sekolah
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Shield className="h-12 w-12 text-primary-600 mb-4" />
                <CardTitle>Keamanan Data</CardTitle>
                <CardDescription>
                  Data sekolah aman dengan enkripsi dan backup otomatis
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Smartphone className="h-12 w-12 text-primary-600 mb-4" />
                <CardTitle>Mobile Friendly</CardTitle>
                <CardDescription>
                  Akses dari smartphone, tablet, atau komputer dengan tampilan responsif
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4 bg-primary-50">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Mengapa Memilih GuruFlow?
              </h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-success-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900">Sesuai Standar Indonesia</h3>
                    <p className="text-gray-600">Menggunakan kode absensi dan sistem penilaian yang sesuai dengan standar pendidikan Indonesia</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-success-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900">Mudah Digunakan</h3>
                    <p className="text-gray-600">Interface yang intuitif dan mudah dipahami oleh guru dan siswa</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-success-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900">Hemat Waktu</h3>
                    <p className="text-gray-600">Otomatisasi tugas administratif sehingga guru bisa fokus mengajar</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-success-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900">Gamifikasi</h3>
                    <p className="text-gray-600">Sistem poin dan badge untuk meningkatkan motivasi belajar siswa</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-white rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Mulai Hari Ini</h3>
              <p className="text-gray-600 mb-6">
                Daftar sekarang dan rasakan kemudahan mengelola administrasi sekolah dengan GuruFlow
              </p>
              <Button size="lg" className="w-full" asChild>
                <Link href="/onboarding">
                  Daftar Gratis Sekarang
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <BookOpen className="h-6 w-6" />
                <span className="text-xl font-bold">GuruFlow</span>
              </div>
              <p className="text-gray-400">
                Platform administrasi sekolah modern untuk Indonesia
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Produk</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#" className="hover:text-white transition-colors">Fitur</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Harga</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Demo</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Dukungan</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#" className="hover:text-white transition-colors">Bantuan</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Dokumentasi</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Kontak</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Perusahaan</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#" className="hover:text-white transition-colors">Tentang Kami</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Blog</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Karir</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 GuruFlow. Semua hak dilindungi.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}