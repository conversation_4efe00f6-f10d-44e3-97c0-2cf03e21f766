'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Save, 
  RotateCcw, 
  Users, 
  CheckCircle, 
  AlertCircle,
  Clock,
  UserCheck,
  UserX
} from 'lucide-react';

interface Student {
  id: number;
  name: string;
  studentId: string;
}

interface AttendanceRecord {
  studentId: number;
  status: 'H' | 'I' | 'S' | 'A';
  notes?: string;
}

interface AttendanceGridProps {
  classroomId: number;
  subjectId: number;
  date: string;
}

const ATTENDANCE_STATUS = {
  H: { label: 'Hadir', color: 'bg-green-100 text-green-800 hover:bg-green-200', icon: UserCheck },
  I: { label: 'Izin', color: 'bg-blue-100 text-blue-800 hover:bg-blue-200', icon: Clock },
  S: { label: 'Sakit', color: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200', icon: AlertCircle },
  A: { label: 'Alpa', color: 'bg-red-100 text-red-800 hover:bg-red-200', icon: UserX },
} as const;

export function AttendanceGrid({ classroomId, subjectId, date }: AttendanceGridProps) {
  const [students, setStudents] = useState<Student[]>([]);
  const [attendance, setAttendance] = useState<Record<number, AttendanceRecord>>({});
  const [existingAttendance, setExistingAttendance] = useState<Record<number, AttendanceRecord>>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    fetchStudentsAndAttendance();
  }, [classroomId, subjectId, date]);

  useEffect(() => {
    // Check if there are changes
    const hasChanges = Object.keys(attendance).some(studentId => {
      const current = attendance[parseInt(studentId)];
      const existing = existingAttendance[parseInt(studentId)];
      return current?.status !== existing?.status || current?.notes !== existing?.notes;
    });
    setHasChanges(hasChanges);
  }, [attendance, existingAttendance]);

  const fetchStudentsAndAttendance = async () => {
    setLoading(true);
    try {
      // Fetch students in classroom
      const studentsResponse = await fetch(`/api/classrooms/${classroomId}/students`);
      const studentsData = await studentsResponse.json();
      
      if (studentsData.success) {
        setStudents(studentsData.data);
        
        // Initialize attendance with default 'H' status
        const defaultAttendance: Record<number, AttendanceRecord> = {};
        studentsData.data.forEach((student: Student) => {
          defaultAttendance[student.id] = {
            studentId: student.id,
            status: 'H',
            notes: '',
          };
        });
        
        // Fetch existing attendance for this date
        const attendanceResponse = await fetch(
          `/api/attendance?classroomId=${classroomId}&subjectId=${subjectId}&date=${date}`
        );
        const attendanceData = await attendanceResponse.json();
        
        if (attendanceData.success && attendanceData.data.length > 0) {
          // Update with existing attendance
          const existingRecords: Record<number, AttendanceRecord> = {};
          attendanceData.data.forEach((record: any) => {
            if (!record.studentId) return;
            const studentId = record.studentId;
            existingRecords[studentId] = {
              studentId,
              status: record.status,
              notes: record.notes || '',
            };
            defaultAttendance[studentId] = { ...existingRecords[studentId] };
          });
          setExistingAttendance(existingRecords);
        }
        
        setAttendance(defaultAttendance);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateAttendance = (studentId: number, status: 'H' | 'I' | 'S' | 'A') => {
    setAttendance(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        studentId,
        status,
      },
    }));
  };

  const updateNotes = (studentId: number, notes: string) => {
    setAttendance(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        studentId,
        status: prev[studentId]?.status || 'H',
        notes,
      },
    }));
  };

  const markAllPresent = () => {
    const updatedAttendance = { ...attendance };
    students.forEach(student => {
      updatedAttendance[student.id] = {
        ...updatedAttendance[student.id],
        studentId: student.id,
        status: 'H',
        notes: updatedAttendance[student.id]?.notes || '',
      };
    });
    setAttendance(updatedAttendance);
  };

  const resetChanges = () => {
    setAttendance({ ...existingAttendance });
  };

  const saveAttendance = async () => {
    setSaving(true);
    try {
      const attendanceRecords = Object.values(attendance);
      
      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          classroomId,
          subjectId,
          date,
          records: attendanceRecords,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setExistingAttendance({ ...attendance });
        // Show success message
        alert('Absensi berhasil disimpan!');
      } else {
        throw new Error(result.error?.message || 'Gagal menyimpan absensi');
      }
    } catch (error) {
      console.error('Error saving attendance:', error);
      alert('Gagal menyimpan absensi. Silakan coba lagi.');
    } finally {
      setSaving(false);
    }
  };

  const getAttendanceStats = () => {
    const records = Object.values(attendance);
    const stats = {
      H: records.filter(r => r.status === 'H').length,
      I: records.filter(r => r.status === 'I').length,
      S: records.filter(r => r.status === 'S').length,
      A: records.filter(r => r.status === 'A').length,
    };
    const total = records.length;
    const presentPercentage = total > 0 ? Math.round((stats.H / total) * 100) : 0;
    
    return { ...stats, total, presentPercentage };
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-12 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const stats = getAttendanceStats();

  return (
    <div className="space-y-6">
      {/* Stats and Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Absensi - {new Date(date).toLocaleDateString('id-ID')}</span>
            </CardTitle>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={markAllPresent}
                disabled={saving}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Semua Hadir
              </Button>
              {hasChanges && (
                <Button
                  variant="outline"
                  onClick={resetChanges}
                  disabled={saving}
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset
                </Button>
              )}
              <Button
                onClick={saveAttendance}
                disabled={saving || !hasChanges}
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Simpan Absensi
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              <p className="text-sm text-gray-600">Total Siswa</p>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">{stats.H}</p>
              <p className="text-sm text-gray-600">Hadir</p>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">{stats.I}</p>
              <p className="text-sm text-gray-600">Izin</p>
            </div>
            <div className="text-center p-3 bg-yellow-50 rounded-lg">
              <p className="text-2xl font-bold text-yellow-600">{stats.S}</p>
              <p className="text-sm text-gray-600">Sakit</p>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <p className="text-2xl font-bold text-red-600">{stats.A}</p>
              <p className="text-sm text-gray-600">Alpa</p>
            </div>
          </div>

          <div className="text-center p-3 bg-blue-600 text-white rounded-lg">
            <p className="text-2xl font-bold">{stats.presentPercentage}%</p>
            <p className="text-sm opacity-90">Tingkat Kehadiran</p>
          </div>
        </CardContent>
      </Card>

      {/* Attendance Grid */}
      <Card>
        <CardHeader>
          <CardTitle>Daftar Kehadiran Siswa</CardTitle>
          {hasChanges && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Ada perubahan yang belum disimpan. Jangan lupa untuk menyimpan absensi.
              </AlertDescription>
            </Alert>
          )}
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {students.map((student) => {
              const studentAttendance = attendance[student.id];
              const currentStatus = studentAttendance?.status || 'H';
              
              return (
                <div
                  key={student.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{student.name}</h4>
                    <p className="text-sm text-gray-600">NIS: {student.studentId}</p>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {Object.entries(ATTENDANCE_STATUS).map(([status, config]) => {
                      const Icon = config.icon;
                      const isSelected = currentStatus === status;
                      
                      return (
                        <Button
                          key={status}
                          variant={isSelected ? "default" : "outline"}
                          size="sm"
                          onClick={() => updateAttendance(student.id, status as 'H' | 'I' | 'S' | 'A')}
                          className={isSelected ? config.color : 'hover:bg-gray-100'}
                        >
                          <Icon className="h-4 w-4 mr-1" />
                          {status}
                        </Button>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>

          {students.length === 0 && (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Tidak ada siswa di kelas ini</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}