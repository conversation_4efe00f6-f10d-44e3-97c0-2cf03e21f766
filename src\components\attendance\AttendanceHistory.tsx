'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  Download, 
  Search, 
  Filter,
  Users,
  TrendingUp,
  FileText,
  Edit,
  Eye
} from 'lucide-react';
import { EditAttendanceModal } from './EditAttendanceModal';

interface AttendanceHistoryRecord {
  id: number;
  date: string;
  studentName: string;
  studentId: string;
  status: 'H' | 'I' | 'S' | 'A';
  subjectName: string;
  classroomName: string;
  notes?: string;
}

interface AttendanceHistoryProps {
  classroomId?: number;
  subjectId?: number;
}

const ATTENDANCE_STATUS = {
  H: { label: 'Hadir', color: 'bg-green-100 text-green-800' },
  I: { label: 'Izin', color: 'bg-blue-100 text-blue-800' },
  S: { label: 'Sakit', color: 'bg-yellow-100 text-yellow-800' },
  A: { label: 'Alpa', color: 'bg-red-100 text-red-800' },
} as const;

export function AttendanceHistory({ classroomId, subjectId }: AttendanceHistoryProps) {
  const [records, setRecords] = useState<AttendanceHistoryRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<AttendanceHistoryRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [editingAttendance, setEditingAttendance] = useState<AttendanceHistoryRecord | null>(null);

  useEffect(() => {
    fetchAttendanceHistory();
  }, [classroomId, subjectId]);

  useEffect(() => {
    filterRecords();
  }, [records, searchTerm, dateFilter, statusFilter]);

  const fetchAttendanceHistory = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (classroomId) params.append('classroomId', classroomId.toString());
      if (subjectId) params.append('subjectId', subjectId.toString());
      params.append('limit', '100'); // Limit to recent 100 records
      
      const response = await fetch(`/api/attendance/history?${params}`);
      const data = await response.json();
      
      if (data.success) {
        setRecords(data.data);
      }
    } catch (error) {
      console.error('Error fetching attendance history:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterRecords = () => {
    let filtered = [...records];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(record =>
        record.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.studentId.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Date filter
    if (dateFilter) {
      filtered = filtered.filter(record => record.date === dateFilter);
    }

    // Status filter
    if (statusFilter) {
      filtered = filtered.filter(record => record.status === statusFilter);
    }

    setFilteredRecords(filtered);
  };

  const exportToCSV = () => {
    const csvContent = [
      ['Tanggal', 'Nama Siswa', 'NIS', 'Kelas', 'Mata Pelajaran', 'Status', 'Keterangan'],
      ...filteredRecords.map(record => [
        new Date(record.date).toLocaleDateString('id-ID'),
        record.studentName,
        record.studentId,
        record.classroomName,
        record.subjectName,
        ATTENDANCE_STATUS[record.status].label,
        record.notes || ''
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `riwayat_absensi_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const handleEditAttendance = async (data: { status: 'H' | 'I' | 'S' | 'A'; notes: string; reason: string }) => {
    if (!editingAttendance) return;

    try {
      const response = await fetch(`/api/attendance/${editingAttendance.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error?.message || 'Gagal memperbarui absensi');
      }

      // Refresh data
      await fetchAttendanceHistory();
      setEditingAttendance(null);
    } catch (error) {
      throw error;
    }
  };

  const getAttendanceStats = () => {
    const stats = {
      H: filteredRecords.filter(r => r.status === 'H').length,
      I: filteredRecords.filter(r => r.status === 'I').length,
      S: filteredRecords.filter(r => r.status === 'S').length,
      A: filteredRecords.filter(r => r.status === 'A').length,
    };
    const total = filteredRecords.length;
    const presentPercentage = total > 0 ? Math.round((stats.H / total) * 100) : 0;
    
    return { ...stats, total, presentPercentage };
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-12 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const stats = getAttendanceStats();

  return (
    <div className="space-y-6">
      {/* Filters and Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>Riwayat Absensi</span>
            </CardTitle>
            <Button onClick={exportToCSV} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <p className="text-xl font-bold text-gray-900">{stats.total}</p>
              <p className="text-xs text-gray-600">Total Record</p>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <p className="text-xl font-bold text-green-600">{stats.H}</p>
              <p className="text-xs text-gray-600">Hadir</p>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <p className="text-xl font-bold text-blue-600">{stats.I}</p>
              <p className="text-xs text-gray-600">Izin</p>
            </div>
            <div className="text-center p-3 bg-yellow-50 rounded-lg">
              <p className="text-xl font-bold text-yellow-600">{stats.S}</p>
              <p className="text-xs text-gray-600">Sakit</p>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <p className="text-xl font-bold text-red-600">{stats.A}</p>
              <p className="text-xs text-gray-600">Alpa</p>
            </div>
            <div className="text-center p-3 bg-blue-600 text-white rounded-lg">
              <p className="text-xl font-bold">{stats.presentPercentage}%</p>
              <p className="text-xs opacity-90">Kehadiran</p>
            </div>
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Cari nama atau NIS..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Input
              type="date"
              placeholder="Filter tanggal"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
            />
            
            <select
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="">Semua Status</option>
              <option value="H">Hadir</option>
              <option value="I">Izin</option>
              <option value="S">Sakit</option>
              <option value="A">Alpa</option>
            </select>

            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm('');
                setDateFilter('');
                setStatusFilter('');
              }}
            >
              <Filter className="h-4 w-4 mr-2" />
              Reset Filter
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Records Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            Data Absensi ({filteredRecords.length} record)
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredRecords.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Tidak ada data absensi
              </h3>
              <p className="text-gray-600">
                {records.length === 0 
                  ? 'Belum ada data absensi yang tercatat'
                  : 'Tidak ada data yang sesuai dengan filter'
                }
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Tanggal
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Siswa
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Kelas
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Mata Pelajaran
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Status
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Keterangan
                    </th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">
                      Aksi
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {filteredRecords.map((record) => (
                    <tr key={record.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm text-gray-900">
                        {new Date(record.date).toLocaleDateString('id-ID')}
                      </td>
                      <td className="px-4 py-3">
                        <div>
                          <p className="font-medium text-gray-900">
                            {record.studentName}
                          </p>
                          <p className="text-sm text-gray-600">
                            NIS: {record.studentId}
                          </p>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900">
                        {record.classroomName}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900">
                        {record.subjectName}
                      </td>
                      <td className="px-4 py-3">
                        <Badge className={ATTENDANCE_STATUS[record.status].color}>
                          {record.status} - {ATTENDANCE_STATUS[record.status].label}
                        </Badge>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600">
                        {record.notes || '-'}
                      </td>
                      <td className="px-4 py-3 text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingAttendance(record)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Attendance Modal */}
      {editingAttendance && (
        <EditAttendanceModal
          isOpen={!!editingAttendance}
          onClose={() => setEditingAttendance(null)}
          onSave={handleEditAttendance}
          attendance={editingAttendance}
        />
      )}
    </div>
  );
}