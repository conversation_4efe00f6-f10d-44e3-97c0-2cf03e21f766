'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AttendanceGrid } from './AttendanceGrid';
import { AttendanceHistory } from './AttendanceHistory';
import { AttendanceStats } from './AttendanceStats';
import { AttendanceReports } from './AttendanceReports';
import { Calendar, Users, Save, RotateCcw } from 'lucide-react';

interface Classroom {
  id: number;
  name: string;
  grade: string;
  studentCount: number;
}

interface Subject {
  id: number;
  name: string;
  code: string;
}

export function AttendanceManager() {
  const [classrooms, setClassrooms] = useState<Classroom[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [selectedClassroom, setSelectedClassroom] = useState<string>('');
  const [selectedSubject, setSelectedSubject] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split('T')[0]!
  );
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'input' | 'history' | 'stats' | 'reports'>('input');

  useEffect(() => {
    fetchClassrooms();
  }, []);

  useEffect(() => {
    if (selectedClassroom) {
      fetchSubjects(parseInt(selectedClassroom));
    } else {
      setSubjects([]);
      setSelectedSubject('');
    }
  }, [selectedClassroom]);

  const fetchClassrooms = async () => {
    try {
      const response = await fetch('/api/classrooms');
      const data = await response.json();
      
      if (data.success) {
        setClassrooms(data.data);
      }
    } catch (error) {
      console.error('Error fetching classrooms:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSubjects = async (classroomId: number) => {
    try {
      const response = await fetch(`/api/classrooms/${classroomId}/subjects`);
      const data = await response.json();
      
      if (data.success) {
        setSubjects(data.data);
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  const canShowAttendanceGrid = selectedClassroom && selectedSubject && selectedDate;

  return (
    <div className="space-y-6">
      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Pilih Kelas dan Mata Pelajaran</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="classroom">Kelas</Label>
              <Select
                value={selectedClassroom}
                onValueChange={setSelectedClassroom}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih kelas" />
                </SelectTrigger>
                <SelectContent>
                  {classrooms.map((classroom) => (
                    <SelectItem key={classroom.id} value={classroom.id.toString()}>
                      {classroom.name} ({classroom.studentCount} siswa)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="subject">Mata Pelajaran</Label>
              <Select
                value={selectedSubject}
                onValueChange={setSelectedSubject}
                disabled={!selectedClassroom || subjects.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih mata pelajaran" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.id.toString()}>
                      {subject.name} ({subject.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="date">Tanggal</Label>
              <Input
                id="date"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                max={new Date().toISOString().split('T')[0]}
              />
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-2 border-b">
            <Button
              variant={activeTab === 'input' ? 'default' : 'ghost'}
              onClick={() => setActiveTab('input')}
              className="rounded-b-none"
            >
              Input Absensi
            </Button>
            <Button
              variant={activeTab === 'history' ? 'default' : 'ghost'}
              onClick={() => setActiveTab('history')}
              className="rounded-b-none"
            >
              Riwayat Absensi
            </Button>
            <Button
              variant={activeTab === 'stats' ? 'default' : 'ghost'}
              onClick={() => setActiveTab('stats')}
              className="rounded-b-none"
            >
              Statistik
            </Button>
            <Button
              variant={activeTab === 'reports' ? 'default' : 'ghost'}
              onClick={() => setActiveTab('reports')}
              className="rounded-b-none"
            >
              Laporan
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Content */}
      {activeTab === 'input' && (
        <>
          {!canShowAttendanceGrid ? (
            <Card>
              <CardContent className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Pilih Kelas dan Mata Pelajaran
                </h3>
                <p className="text-gray-600">
                  Silakan pilih kelas, mata pelajaran, dan tanggal untuk mulai mencatat absensi
                </p>
              </CardContent>
            </Card>
          ) : (
            <AttendanceGrid
              classroomId={parseInt(selectedClassroom)}
              subjectId={parseInt(selectedSubject)}
              date={selectedDate}
            />
          )}
        </>
      )}

      {activeTab === 'history' && (
        <AttendanceHistory
          classroomId={selectedClassroom ? parseInt(selectedClassroom) : undefined}
          subjectId={selectedSubject ? parseInt(selectedSubject) : undefined}
        />
      )}

      {activeTab === 'stats' && (
        <AttendanceStats
          classroomId={selectedClassroom ? parseInt(selectedClassroom) : undefined}
          subjectId={selectedSubject ? parseInt(selectedSubject) : undefined}
        />
      )}

      {activeTab === 'reports' && (
        <AttendanceReports />
      )}
    </div>
  );
}