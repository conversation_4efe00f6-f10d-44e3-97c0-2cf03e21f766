'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  FileText, 
  Download, 
  Calendar, 
  Filter,
  Users,
  BarChart3,
  Loader2,
  CheckCircle
} from 'lucide-react';

interface Classroom {
  id: number;
  name: string;
  grade: string;
}

interface Subject {
  id: number;
  name: string;
  code: string;
}

interface ReportFilters {
  classroomId: string;
  subjectId: string;
  startDate: string;
  endDate: string;
  reportType: 'summary' | 'detailed' | 'individual';
  format: 'pdf' | 'csv';
}

export function AttendanceReports() {
  const [classrooms, setClassrooms] = useState<Classroom[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [filters, setFilters] = useState<ReportFilters>({
    classroomId: '',
    subjectId: '',
    startDate: '',
    endDate: '',
    reportType: 'summary',
    format: 'pdf',
  });
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    fetchClassrooms();
    
    // Set default date range (last 30 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);
    
    setFilters(prev => ({
      ...prev,
      startDate: startDate.toISOString().split('T')[0]!,
      endDate: endDate.toISOString().split('T')[0]!,
    }));
  }, []);

  useEffect(() => {
    if (filters.classroomId) {
      fetchSubjects(parseInt(filters.classroomId));
    } else {
      setSubjects([]);
      setFilters(prev => ({ ...prev, subjectId: '' }));
    }
  }, [filters.classroomId]);

  const fetchClassrooms = async () => {
    try {
      const response = await fetch('/api/classrooms');
      const data = await response.json();
      
      if (data.success) {
        setClassrooms(data.data);
      }
    } catch (error) {
      console.error('Error fetching classrooms:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSubjects = async (classroomId: number) => {
    try {
      const response = await fetch(`/api/classrooms/${classroomId}/subjects`);
      const data = await response.json();
      
      if (data.success) {
        setSubjects(data.data);
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  const validateFilters = (): boolean => {
    setError('');

    if (!filters.classroomId) {
      setError('Kelas wajib dipilih');
      return false;
    }

    if (!filters.startDate || !filters.endDate) {
      setError('Tanggal mulai dan selesai wajib diisi');
      return false;
    }

    if (new Date(filters.startDate) > new Date(filters.endDate)) {
      setError('Tanggal mulai tidak boleh lebih besar dari tanggal selesai');
      return false;
    }

    const daysDiff = Math.ceil((new Date(filters.endDate).getTime() - new Date(filters.startDate).getTime()) / (1000 * 60 * 60 * 24));
    if (daysDiff > 365) {
      setError('Rentang tanggal maksimal 1 tahun');
      return false;
    }

    return true;
  };

  const generateReport = async () => {
    if (!validateFilters()) return;

    setGenerating(true);
    setError('');

    try {
      const params = new URLSearchParams({
        classroomId: filters.classroomId,
        startDate: filters.startDate,
        endDate: filters.endDate,
        reportType: filters.reportType,
        format: filters.format,
      });

      if (filters.subjectId) {
        params.append('subjectId', filters.subjectId);
      }

      const response = await fetch(`/api/attendance/reports?${params}`, {
        method: 'GET',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Gagal generate laporan');
      }

      // Handle file download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      
      const filename = `laporan_absensi_${filters.reportType}_${filters.startDate}_${filters.endDate}.${filters.format}`;
      a.download = filename;
      
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Terjadi kesalahan saat generate laporan');
    } finally {
      setGenerating(false);
    }
  };

  const getReportDescription = () => {
    switch (filters.reportType) {
      case 'summary':
        return 'Ringkasan statistik kehadiran per kelas dan mata pelajaran';
      case 'detailed':
        return 'Laporan detail kehadiran semua siswa dengan breakdown harian';
      case 'individual':
        return 'Laporan kehadiran individual per siswa';
      default:
        return '';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-2">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-10 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Report Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Generate Laporan Absensi</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="classroom">Kelas *</Label>
              <Select
                value={filters.classroomId}
                onValueChange={(value) => setFilters(prev => ({ ...prev, classroomId: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih kelas" />
                </SelectTrigger>
                <SelectContent>
                  {classrooms.map((classroom) => (
                    <SelectItem key={classroom.id} value={classroom.id.toString()}>
                      {classroom.name} - {classroom.grade}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="subject">Mata Pelajaran</Label>
              <Select
                value={filters.subjectId}
                onValueChange={(value) => setFilters(prev => ({ ...prev, subjectId: value }))}
                disabled={!filters.classroomId || subjects.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Semua mata pelajaran" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua mata pelajaran</SelectItem>
                  {subjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.id.toString()}>
                      {subject.name} ({subject.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="startDate">Tanggal Mulai *</Label>
              <Input
                id="startDate"
                type="date"
                value={filters.startDate}
                onChange={(e) => setFilters(prev => ({ ...prev, startDate: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">Tanggal Selesai *</Label>
              <Input
                id="endDate"
                type="date"
                value={filters.endDate}
                onChange={(e) => setFilters(prev => ({ ...prev, endDate: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="reportType">Jenis Laporan</Label>
              <Select
                value={filters.reportType}
                onValueChange={(value) => setFilters(prev => ({ ...prev, reportType: value as 'summary' | 'detailed' | 'individual' }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="summary">Ringkasan</SelectItem>
                  <SelectItem value="detailed">Detail</SelectItem>
                  <SelectItem value="individual">Individual</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="format">Format</Label>
              <Select
                value={filters.format}
                onValueChange={(value) => setFilters(prev => ({ ...prev, format: value as 'pdf' | 'csv' }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pdf">PDF</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Report Description */}
          <div className="p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Deskripsi:</strong> {getReportDescription()}
            </p>
          </div>

          {/* Generate Button */}
          <div className="flex justify-end">
            <Button
              onClick={generateReport}
              disabled={generating || !filters.classroomId}
              className="min-w-[150px]"
            >
              {generating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Generate Laporan
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick Reports */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Laporan Cepat</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-start space-y-2"
              onClick={() => {
                const today = new Date().toISOString().split('T')[0];
                setFilters(prev => ({
                  ...prev,
                  startDate: today,
                  endDate: today,
                  reportType: 'detailed',
                  format: 'pdf',
                }));
              }}
            >
              <Calendar className="h-6 w-6 text-blue-600" />
              <div className="text-left">
                <h4 className="font-medium">Absensi Hari Ini</h4>
                <p className="text-sm text-gray-600">Laporan kehadiran hari ini</p>
              </div>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-start space-y-2"
              onClick={() => {
                const endDate = new Date();
                const startDate = new Date();
                startDate.setDate(startDate.getDate() - 7);
                setFilters(prev => ({
                  ...prev,
                  startDate: startDate.toISOString().split('T')[0],
                  endDate: endDate.toISOString().split('T')[0],
                  reportType: 'summary',
                  format: 'pdf',
                }));
              }}
            >
              <Users className="h-6 w-6 text-green-600" />
              <div className="text-left">
                <h4 className="font-medium">Minggu Ini</h4>
                <p className="text-sm text-gray-600">Ringkasan 7 hari terakhir</p>
              </div>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-start space-y-2"
              onClick={() => {
                const endDate = new Date();
                const startDate = new Date();
                startDate.setMonth(startDate.getMonth() - 1);
                setFilters(prev => ({
                  ...prev,
                  startDate: startDate.toISOString().split('T')[0],
                  endDate: endDate.toISOString().split('T')[0],
                  reportType: 'individual',
                  format: 'csv',
                }));
              }}
            >
              <FileText className="h-6 w-6 text-purple-600" />
              <div className="text-left">
                <h4 className="font-medium">Bulan Lalu</h4>
                <p className="text-sm text-gray-600">Data individual siswa</p>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}