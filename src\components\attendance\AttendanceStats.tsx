'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Calendar,
  BarChart3,
  Clock
} from 'lucide-react';

interface AttendanceStatsData {
  totalStudents: number;
  totalDays: number;
  overallAttendanceRate: number;
  monthlyStats: Array<{
    month: string;
    attendanceRate: number;
    totalRecords: number;
    presentCount: number;
  }>;
  statusBreakdown: {
    H: number;
    I: number;
    S: number;
    A: number;
  };
  trends: {
    thisMonth: number;
    lastMonth: number;
    change: number;
  };
}

interface AttendanceStatsProps {
  classroomId?: number;
  subjectId?: number;
}

export function AttendanceStats({ classroomId, subjectId }: AttendanceStatsProps) {
  const [stats, setStats] = useState<AttendanceStatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30'); // days

  useEffect(() => {
    fetchStats();
  }, [classroomId, subjectId, timeRange]);

  const fetchStats = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (classroomId) params.append('classroomId', classroomId.toString());
      if (subjectId) params.append('subjectId', subjectId.toString());
      params.append('days', timeRange);
      
      const response = await fetch(`/api/attendance/stats?${params}`);
      const data = await response.json();
      
      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching attendance stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Tidak ada data statistik</p>
        </CardContent>
      </Card>
    );
  }

  const getTrendIcon = () => {
    if (stats.trends.change > 0) {
      return <TrendingUp className="h-4 w-4 text-green-600" />;
    } else if (stats.trends.change < 0) {
      return <TrendingDown className="h-4 w-4 text-red-600" />;
    }
    return <BarChart3 className="h-4 w-4 text-gray-600" />;
  };

  const getTrendColor = () => {
    if (stats.trends.change > 0) return 'text-green-600';
    if (stats.trends.change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  return (
    <div className="space-y-6">
      {/* Header with Time Range Selector */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Statistik Kehadiran</span>
            </CardTitle>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">7 Hari Terakhir</SelectItem>
                <SelectItem value="30">30 Hari Terakhir</SelectItem>
                <SelectItem value="90">3 Bulan Terakhir</SelectItem>
                <SelectItem value="365">1 Tahun Terakhir</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          {/* Main Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <Users className="h-6 w-6 text-blue-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-blue-600">{stats.totalStudents}</p>
              <p className="text-sm text-gray-600">Total Siswa</p>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <Calendar className="h-6 w-6 text-green-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-green-600">{stats.totalDays}</p>
              <p className="text-sm text-gray-600">Hari Aktif</p>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <Clock className="h-6 w-6 text-purple-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-purple-600">
                {stats.overallAttendanceRate.toFixed(1)}%
              </p>
              <p className="text-sm text-gray-600">Tingkat Kehadiran</p>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-center mb-2">
                {getTrendIcon()}
              </div>
              <p className={`text-2xl font-bold ${getTrendColor()}`}>
                {stats.trends.change > 0 ? '+' : ''}{stats.trends.change.toFixed(1)}%
              </p>
              <p className="text-sm text-gray-600">Perubahan</p>
            </div>
          </div>

          {/* Attendance Rate Progress */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">
                Tingkat Kehadiran Keseluruhan
              </span>
              <span className="text-sm text-gray-500">
                {stats.overallAttendanceRate.toFixed(1)}%
              </span>
            </div>
            <Progress value={stats.overallAttendanceRate} className="h-3" />
          </div>

          {/* Status Breakdown */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Breakdown Status</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 border rounded-lg">
                <div className="w-4 h-4 bg-green-500 rounded mx-auto mb-2"></div>
                <p className="text-lg font-bold text-gray-900">{stats.statusBreakdown.H}</p>
                <p className="text-xs text-gray-600">Hadir</p>
              </div>
              <div className="text-center p-3 border rounded-lg">
                <div className="w-4 h-4 bg-blue-500 rounded mx-auto mb-2"></div>
                <p className="text-lg font-bold text-gray-900">{stats.statusBreakdown.I}</p>
                <p className="text-xs text-gray-600">Izin</p>
              </div>
              <div className="text-center p-3 border rounded-lg">
                <div className="w-4 h-4 bg-yellow-500 rounded mx-auto mb-2"></div>
                <p className="text-lg font-bold text-gray-900">{stats.statusBreakdown.S}</p>
                <p className="text-xs text-gray-600">Sakit</p>
              </div>
              <div className="text-center p-3 border rounded-lg">
                <div className="w-4 h-4 bg-red-500 rounded mx-auto mb-2"></div>
                <p className="text-lg font-bold text-gray-900">{stats.statusBreakdown.A}</p>
                <p className="text-xs text-gray-600">Alpa</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Monthly Trends */}
      {stats.monthlyStats.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Tren Bulanan</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.monthlyStats.map((monthStat, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-gray-700">
                        {monthStat.month}
                      </span>
                      <span className="text-sm text-gray-500">
                        {monthStat.attendanceRate.toFixed(1)}% ({monthStat.presentCount}/{monthStat.totalRecords})
                      </span>
                    </div>
                    <Progress value={monthStat.attendanceRate} className="h-2" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}