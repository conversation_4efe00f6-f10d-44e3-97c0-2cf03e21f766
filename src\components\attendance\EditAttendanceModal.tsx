'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  UserCheck,
  UserX,
  Clock,
  AlertCircle,
  Save,
  X
} from 'lucide-react';

interface EditAttendanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: AttendanceEditData) => Promise<void>;
  attendance: {
    id: number;
    studentName: string;
    studentId: string;
    date: string;
    status: 'H' | 'I' | 'S' | 'A';
    notes?: string;
    subjectName: string;
    classroomName: string;
  };
}

interface AttendanceEditData {
  status: 'H' | 'I' | 'S' | 'A';
  notes: string;
  reason: string;
}

const ATTENDANCE_STATUS = {
  H: { label: 'Hadir', color: 'bg-green-100 text-green-800', icon: UserCheck },
  I: { label: 'Izin', color: 'bg-blue-100 text-blue-800', icon: Clock },
  S: { label: 'Sakit', color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle },
  A: { label: 'Alpa', color: 'bg-red-100 text-red-800', icon: UserX },
} as const;

export function EditAttendanceModal({ 
  isOpen, 
  onClose, 
  onSave, 
  attendance 
}: EditAttendanceModalProps) {
  const [formData, setFormData] = useState<AttendanceEditData>({
    status: attendance.status,
    notes: attendance.notes || '',
    reason: '',
  });
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string>('');

  const handleSave = async () => {
    if (!formData.reason.trim()) {
      setError('Alasan perubahan wajib diisi');
      return;
    }

    setSaving(true);
    setError('');

    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Gagal menyimpan perubahan');
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    setFormData({
      status: attendance.status,
      notes: attendance.notes || '',
      reason: '',
    });
    setError('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Absensi</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Student Info */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900">{attendance.studentName}</h4>
            <p className="text-sm text-gray-600">NIS: {attendance.studentId}</p>
            <p className="text-sm text-gray-600">
              {attendance.classroomName} • {attendance.subjectName}
            </p>
            <p className="text-sm text-gray-600">
              {new Date(attendance.date).toLocaleDateString('id-ID')}
            </p>
          </div>

          {/* Status Selection */}
          <div className="space-y-2">
            <Label>Status Kehadiran</Label>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(ATTENDANCE_STATUS).map(([status, config]) => {
                const Icon = config.icon;
                const isSelected = formData.status === status;
                
                return (
                  <Button
                    key={status}
                    variant={isSelected ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFormData(prev => ({ ...prev, status: status as 'H' | 'I' | 'S' | 'A' }))}
                    className={`justify-start ${isSelected ? config.color : 'hover:bg-gray-100'}`}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {status} - {config.label}
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Keterangan</Label>
            <Textarea
              id="notes"
              placeholder="Keterangan tambahan (opsional)"
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
            />
          </div>

          {/* Reason for Change */}
          <div className="space-y-2">
            <Label htmlFor="reason">Alasan Perubahan *</Label>
            <Textarea
              id="reason"
              placeholder="Jelaskan alasan perubahan absensi ini..."
              value={formData.reason}
              onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
              rows={2}
              required
            />
            <p className="text-xs text-gray-500">
              Alasan ini akan dicatat dalam audit trail
            </p>
          </div>

          {/* Actions */}
          <div className="flex space-x-3 pt-4">
            <Button
              onClick={handleSave}
              disabled={saving}
              className="flex-1"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Menyimpan...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Simpan Perubahan
                </>
              )}
            </Button>
            <Button variant="outline" onClick={handleClose} disabled={saving}>
              <X className="h-4 w-4 mr-2" />
              Batal
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}