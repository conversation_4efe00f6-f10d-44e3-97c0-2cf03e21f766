'use client';

import { useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Loader2 } from 'lucide-react';

interface AuthGuardProps {
  children: React.ReactNode;
  allowedRoles?: Array<'teacher' | 'student'>;
  requireSuperAdmin?: boolean;
  fallback?: React.ReactNode;
}

/**
 * Client-side authentication guard component
 */
export function AuthGuard({ 
  children, 
  allowedRoles, 
  requireSuperAdmin = false,
  fallback 
}: AuthGuardProps) {
  const { user, loading, requireAuth, requireRole } = useAuth();

  useEffect(() => {
    if (!loading) {
      if (allowedRoles) {
        requireRole(allowedRoles);
      } else {
        requireAuth();
      }
    }
  }, [user, loading, allowedRoles, requireAuth, requireRole]);

  // Show loading state
  if (loading) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Memuat...</p>
        </div>
      </div>
    );
  }

  // Check authentication
  if (!user) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // Check role permissions
  if (allowedRoles && !allowedRoles.includes(user.role)) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Redirecting...</p>
        </div>
      </div>
    );
  }

  // Check superadmin requirement
  if (requireSuperAdmin && !user.isSuperAdmin) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600">Access denied. Superadmin privileges required.</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

/**
 * Teacher-only guard component
 */
export function TeacherGuard({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <AuthGuard allowedRoles={['teacher']} fallback={fallback}>
      {children}
    </AuthGuard>
  );
}

/**
 * Student-only guard component
 */
export function StudentGuard({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <AuthGuard allowedRoles={['student']} fallback={fallback}>
      {children}
    </AuthGuard>
  );
}

/**
 * Superadmin-only guard component
 */
export function SuperAdminGuard({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <AuthGuard allowedRoles={['teacher']} requireSuperAdmin={true} fallback={fallback}>
      {children}
    </AuthGuard>
  );
}