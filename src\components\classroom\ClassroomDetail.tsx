'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  BookOpen, 
  Plus, 
  Edit, 
  Trash2, 
  UserPlus,
  GraduationCap
} from 'lucide-react';

interface ClassroomDetailProps {
  classroomId: string;
}

interface Student {
  id: number;
  name: string;
  studentId: string;
  enrolledAt: string;
}

interface Subject {
  id: number;
  name: string;
  code: string;
  teacherName?: string;
}

interface Classroom {
  id: number;
  name: string;
  grade: string;
  academicYear: string;
  homeroomTeacherName?: string;
  studentCount: number;
}

export function ClassroomDetail({ classroomId }: ClassroomDetailProps) {
  const [classroom, setClassroom] = useState<Classroom | null>(null);
  const [students, setStudents] = useState<Student[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchClassroomData();
  }, [classroomId]);

  const fetchClassroomData = async () => {
    try {
      setLoading(true);
      
      // Fetch classroom info
      const classroomResponse = await fetch(`/api/classrooms/${classroomId}`);
      const classroomData = await classroomResponse.json();
      
      if (classroomData.success) {
        setClassroom(classroomData.data);
      }

      // Fetch students
      const studentsResponse = await fetch(`/api/classrooms/${classroomId}/students`);
      const studentsData = await studentsResponse.json();
      
      if (studentsData.success) {
        setStudents(studentsData.data);
      }

      // Fetch subjects
      const subjectsResponse = await fetch(`/api/classrooms/${classroomId}/subjects`);
      const subjectsData = await subjectsResponse.json();
      
      if (subjectsData.success) {
        setSubjects(subjectsData.data);
      }
    } catch (error) {
      console.error('Error fetching classroom data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!classroom) {
    return (
      <div className="p-8 text-center">
        <p className="text-gray-600">Kelas tidak ditemukan</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <GraduationCap className="h-6 w-6 text-blue-600" />
                <span>{classroom.name}</span>
              </CardTitle>
              <p className="text-gray-600 mt-1">
                Kelas {classroom.grade} • Tahun Ajaran {classroom.academicYear}
              </p>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="outline" size="sm">
                <UserPlus className="h-4 w-4 mr-2" />
                Tambah Siswa
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{students.length}</div>
              <div className="text-sm text-gray-600">Total Siswa</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{subjects.length}</div>
              <div className="text-sm text-gray-600">Mata Pelajaran</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {classroom.homeroomTeacherName || '-'}
              </div>
              <div className="text-sm text-gray-600">Wali Kelas</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="students">Siswa ({students.length})</TabsTrigger>
          <TabsTrigger value="subjects">Mata Pelajaran ({subjects.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Informasi Kelas</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Nama Kelas</label>
                  <p className="text-gray-900">{classroom.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Tingkat</label>
                  <p className="text-gray-900">{classroom.grade}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Tahun Ajaran</label>
                  <p className="text-gray-900">{classroom.academicYear}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Wali Kelas</label>
                  <p className="text-gray-900">{classroom.homeroomTeacherName || 'Belum ditentukan'}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="students" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <Users className="h-5 w-5" />
                  <span>Daftar Siswa</span>
                </CardTitle>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Siswa
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {students.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Belum ada siswa di kelas ini</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {students.map((student) => (
                    <div
                      key={student.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-medium text-sm">
                            {student.name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium">{student.name}</p>
                          <p className="text-sm text-gray-600">NIS: {student.studentId}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary">
                          {new Date(student.enrolledAt).toLocaleDateString('id-ID')}
                        </Badge>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subjects" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <BookOpen className="h-5 w-5" />
                  <span>Mata Pelajaran</span>
                </CardTitle>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Mata Pelajaran
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {subjects.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <BookOpen className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Belum ada mata pelajaran di kelas ini</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {subjects.map((subject) => (
                    <div
                      key={subject.id}
                      className="p-4 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{subject.name}</h4>
                        <Badge variant="outline">{subject.code}</Badge>
                      </div>
                      {subject.teacherName && (
                        <p className="text-sm text-gray-600">
                          Guru: {subject.teacherName}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
