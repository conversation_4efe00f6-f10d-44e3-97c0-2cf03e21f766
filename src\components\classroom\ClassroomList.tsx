'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Search, 
  Users, 
  BookOpen, 
  Calendar,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  UserPlus
} from 'lucide-react';
import Link from 'next/link';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface Classroom {
  id: number;
  name: string;
  grade: string;
  academicYear: string;
  homeroomTeacher: string;
  studentCount: number;
  subjectCount: number;
  isActive: boolean;
  createdAt: string;
}

export function ClassroomList() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGrade, setSelectedGrade] = useState<string>('all');

  // Mock data - in real implementation, fetch from API
  const mockClassrooms: Classroom[] = [
    {
      id: 1,
      name: 'Kelas 5A',
      grade: '5',
      academicYear: '2024/2025',
      homeroomTeacher: 'Bu Sari Dewi',
      studentCount: 28,
      subjectCount: 8,
      isActive: true,
      createdAt: '2024-07-15',
    },
    {
      id: 2,
      name: 'Kelas 5B',
      grade: '5',
      academicYear: '2024/2025',
      homeroomTeacher: 'Pak Budi Santoso',
      studentCount: 25,
      subjectCount: 8,
      isActive: true,
      createdAt: '2024-07-15',
    },
    {
      id: 3,
      name: 'Kelas 6A',
      grade: '6',
      academicYear: '2024/2025',
      homeroomTeacher: 'Bu Ani Lestari',
      studentCount: 30,
      subjectCount: 9,
      isActive: true,
      createdAt: '2024-07-15',
    },
    {
      id: 4,
      name: 'Kelas 4A',
      grade: '4',
      academicYear: '2024/2025',
      homeroomTeacher: 'Pak Dedi Kurniawan',
      studentCount: 22,
      subjectCount: 7,
      isActive: false,
      createdAt: '2024-07-15',
    },
  ];

  const filteredClassrooms = mockClassrooms.filter(classroom => {
    const matchesSearch = classroom.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         classroom.homeroomTeacher.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesGrade = selectedGrade === 'all' || classroom.grade === selectedGrade;
    return matchesSearch && matchesGrade;
  });

  const grades = ['all', ...Array.from(new Set(mockClassrooms.map(c => c.grade)))];

  const handleDeleteClassroom = (classroomId: number) => {
    // In real implementation, show confirmation dialog and call API
    console.log('Delete classroom:', classroomId);
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Daftar Kelas</h2>
          <p className="text-gray-600">
            Kelola semua kelas dalam tahun ajaran {mockClassrooms[0]?.academicYear}
          </p>
        </div>
        
        <Button asChild>
          <Link href="/guru/kelas/buat">
            <Plus className="h-4 w-4 mr-2" />
            Buat Kelas Baru
          </Link>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cari kelas atau nama guru..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="sm:w-48">
              <select
                value={selectedGrade}
                onChange={(e) => setSelectedGrade(e.target.value)}
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">Semua Tingkat</option>
                {grades.filter(g => g !== 'all').map(grade => (
                  <option key={grade} value={grade}>
                    Kelas {grade}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Classroom Grid */}
      {filteredClassrooms.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm || selectedGrade !== 'all' ? 'Tidak ada kelas yang ditemukan' : 'Belum ada kelas'}
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedGrade !== 'all' 
                ? 'Coba ubah filter pencarian Anda'
                : 'Mulai dengan membuat kelas pertama untuk sekolah Anda'
              }
            </p>
            {!searchTerm && selectedGrade === 'all' && (
              <Button asChild>
                <Link href="/guru/kelas/buat">
                  <Plus className="h-4 w-4 mr-2" />
                  Buat Kelas Pertama
                </Link>
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredClassrooms.map((classroom) => (
            <Card key={classroom.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{classroom.name}</CardTitle>
                    <CardDescription>
                      Tahun Ajaran {classroom.academicYear}
                    </CardDescription>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge variant={classroom.isActive ? 'default' : 'secondary'}>
                      {classroom.isActive ? 'Aktif' : 'Nonaktif'}
                    </Badge>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/guru/kelas/${classroom.id}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            Lihat Detail
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/guru/kelas/${classroom.id}/edit`}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Kelas
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/guru/kelas/${classroom.id}/siswa`}>
                            <UserPlus className="h-4 w-4 mr-2" />
                            Kelola Siswa
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeleteClassroom(classroom.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Hapus Kelas
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <Users className="h-4 w-4 mr-2" />
                    <span>Wali Kelas: {classroom.homeroomTeacher}</span>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center text-sm">
                      <Users className="h-4 w-4 mr-2 text-blue-500" />
                      <div>
                        <div className="font-medium">{classroom.studentCount}</div>
                        <div className="text-gray-500">Siswa</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center text-sm">
                      <BookOpen className="h-4 w-4 mr-2 text-green-500" />
                      <div>
                        <div className="font-medium">{classroom.subjectCount}</div>
                        <div className="text-gray-500">Mata Pelajaran</div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="h-3 w-3 mr-1" />
                    Dibuat: {new Date(classroom.createdAt).toLocaleDateString('id-ID')}
                  </div>
                </div>
                
                <div className="mt-4 pt-4 border-t">
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline" className="flex-1" asChild>
                      <Link href={`/guru/kelas/${classroom.id}`}>
                        <Eye className="h-3 w-3 mr-1" />
                        Detail
                      </Link>
                    </Button>
                    <Button size="sm" className="flex-1" asChild>
                      <Link href={`/guru/absensi?kelas=${classroom.id}`}>
                        <Calendar className="h-3 w-3 mr-1" />
                        Absensi
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Summary Stats */}
      {filteredClassrooms.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {filteredClassrooms.length}
                </div>
                <div className="text-sm text-gray-600">Total Kelas</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {filteredClassrooms.reduce((acc, c) => acc + c.studentCount, 0)}
                </div>
                <div className="text-sm text-gray-600">Total Siswa</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {filteredClassrooms.filter(c => c.isActive).length}
                </div>
                <div className="text-sm text-gray-600">Kelas Aktif</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">
                  {Math.round(filteredClassrooms.reduce((acc, c) => acc + c.studentCount, 0) / filteredClassrooms.length)}
                </div>
                <div className="text-sm text-gray-600">Rata-rata Siswa</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}