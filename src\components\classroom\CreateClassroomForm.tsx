'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Save, Loader2, Users } from 'lucide-react';
import Link from 'next/link';

interface ClassroomFormData {
  name: string;
  grade: string;
  academicYear: string;
  homeroomTeacherId?: number;
}

export function CreateClassroomForm() {
  const [formData, setFormData] = useState<ClassroomFormData>({
    name: '',
    grade: '',
    academicYear: '2024/2025',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  // Mock teachers data - in real implementation, fetch from API
  const mockTeachers = [
    { id: 1, name: 'Bu Sari Dewi', email: '<EMAIL>' },
    { id: 2, name: 'Pak Budi Santoso', email: '<EMAIL>' },
    { id: 3, name: 'Bu Ani Lestari', email: '<EMAIL>' },
    { id: 4, name: 'Pak Dedi Kurniawan', email: '<EMAIL>' },
  ];

  const grades = ['1', '2', '3', '4', '5', '6'];
  const academicYears = ['2024/2025', '2025/2026', '2026/2027'];

  const handleInputChange = (field: keyof ClassroomFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nama kelas wajib diisi';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Nama kelas minimal 2 karakter';
    }

    if (!formData.grade) {
      newErrors.grade = 'Tingkat kelas wajib dipilih';
    }

    if (!formData.academicYear) {
      newErrors.academicYear = 'Tahun ajaran wajib dipilih';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // In real implementation, call API to create classroom
      const response = await fetch('/api/classrooms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        throw new Error('Gagal membuat kelas');
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Redirect to classroom list with success message
        router.push('/guru/kelas?success=created');
      }
    } catch (error) {
      console.error('Create classroom error:', error);
      setErrors({ submit: 'Terjadi kesalahan saat membuat kelas' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Back Button */}
      <div className="flex items-center space-x-4">
        <Button variant="outline" asChild>
          <Link href="/guru/kelas">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali ke Daftar Kelas
          </Link>
        </Button>
      </div>

      {/* Form Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Informasi Kelas Baru</span>
          </CardTitle>
          <CardDescription>
            Masukkan informasi dasar untuk kelas yang akan dibuat
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {errors.submit && (
              <Alert variant="destructive">
                <AlertDescription>{errors.submit}</AlertDescription>
              </Alert>
            )}

            {/* Classroom Name */}
            <div className="space-y-2">
              <Label htmlFor="name">
                Nama Kelas <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                placeholder="Contoh: Kelas 5A, Kelas 6 Unggulan"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={errors.name ? 'border-red-500' : ''}
                disabled={isSubmitting}
              />
              {errors.name && (
                <p className="text-sm text-red-600">{errors.name}</p>
              )}
              <p className="text-sm text-gray-500">
                Nama kelas akan ditampilkan di seluruh sistem
              </p>
            </div>

            {/* Grade Level */}
            <div className="space-y-2">
              <Label htmlFor="grade">
                Tingkat Kelas <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.grade}
                onValueChange={(value) => handleInputChange('grade', value)}
                disabled={isSubmitting}
              >
                <SelectTrigger className={errors.grade ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Pilih tingkat kelas" />
                </SelectTrigger>
                <SelectContent>
                  {grades.map((grade) => (
                    <SelectItem key={grade} value={grade}>
                      Kelas {grade}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.grade && (
                <p className="text-sm text-red-600">{errors.grade}</p>
              )}
            </div>

            {/* Academic Year */}
            <div className="space-y-2">
              <Label htmlFor="academicYear">
                Tahun Ajaran <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.academicYear}
                onValueChange={(value) => handleInputChange('academicYear', value)}
                disabled={isSubmitting}
              >
                <SelectTrigger className={errors.academicYear ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Pilih tahun ajaran" />
                </SelectTrigger>
                <SelectContent>
                  {academicYears.map((year) => (
                    <SelectItem key={year} value={year}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.academicYear && (
                <p className="text-sm text-red-600">{errors.academicYear}</p>
              )}
            </div>

            {/* Homeroom Teacher */}
            <div className="space-y-2">
              <Label htmlFor="homeroomTeacher">
                Wali Kelas (Opsional)
              </Label>
              <Select
                value={formData.homeroomTeacherId?.toString() || ''}
                onValueChange={(value) => handleInputChange('homeroomTeacherId', parseInt(value))}
                disabled={isSubmitting}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih wali kelas" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Belum ditentukan</SelectItem>
                  {mockTeachers.map((teacher) => (
                    <SelectItem key={teacher.id} value={teacher.id.toString()}>
                      {teacher.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-sm text-gray-500">
                Wali kelas dapat diatur nanti setelah kelas dibuat
              </p>
            </div>

            {/* Info Alert */}
            <Alert>
              <AlertDescription>
                Setelah kelas dibuat, Anda dapat menambahkan mata pelajaran dan 
                mendaftarkan siswa ke dalam kelas ini.
              </AlertDescription>
            </Alert>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-4 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={isSubmitting}
              >
                Batal
              </Button>
              
              <Button
                type="submit"
                disabled={isSubmitting}
                className="min-w-32"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Buat Kelas
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}