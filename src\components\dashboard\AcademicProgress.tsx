import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { BarChart3, TrendingUp, Award, Target, BookOpen } from 'lucide-react';
import Link from 'next/link';

interface SubjectGrade {
  subject: string;
  currentGrade: number;
  maxGrade: number;
  letterGrade: string;
  trend: 'up' | 'down' | 'stable';
  assignmentCount: number;
  lastUpdated: string;
}

interface AcademicProgressProps {
  grades: SubjectGrade[];
  overallAverage: number;
  semester: string;
  targetGrade?: number;
}

export function AcademicProgress({ 
  grades, 
  overallAverage, 
  semester,
  targetGrade = 85 
}: AcademicProgressProps) {
  const getGradeColor = (grade: number) => {
    if (grade >= 90) return 'text-green-600';
    if (grade >= 80) return 'text-blue-600';
    if (grade >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getGradeBadgeColor = (letterGrade: string) => {
    switch (letterGrade) {
      case 'A':
        return 'bg-green-100 text-green-800';
      case 'B':
        return 'bg-blue-100 text-blue-800';
      case 'C':
        return 'bg-yellow-100 text-yellow-800';
      case 'D':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendIcon = (trend: SubjectGrade['trend']) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down':
        return <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />;
      case 'stable':
        return <div className="h-4 w-4 border-b-2 border-gray-400" />;
    }
  };

  const progressToTarget = Math.min((overallAverage / targetGrade) * 100, 100);

  if (grades.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Progress Akademik</span>
          </CardTitle>
          <CardDescription>Semester {semester}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-2">Belum ada nilai</p>
            <p className="text-sm text-gray-500">
              Nilai akan muncul setelah guru memberikan penilaian
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <BarChart3 className="h-5 w-5" />
          <span>Progress Akademik</span>
        </CardTitle>
        <CardDescription>Semester {semester}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Overall Progress */}
          <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between mb-3">
              <div>
                <h4 className="font-semibold text-gray-900">Rata-rata Keseluruhan</h4>
                <p className="text-sm text-gray-600">Target: {targetGrade}</p>
              </div>
              <div className="text-right">
                <div className={`text-2xl font-bold ${getGradeColor(overallAverage)}`}>
                  {overallAverage.toFixed(1)}
                </div>
                <div className="flex items-center space-x-1">
                  <Target className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">
                    {progressToTarget.toFixed(0)}% dari target
                  </span>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress ke Target</span>
                <span>{progressToTarget.toFixed(1)}%</span>
              </div>
              <Progress value={progressToTarget} className="h-2" />
            </div>
          </div>

          {/* Subject Grades */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">Nilai per Mata Pelajaran</h4>
            
            {grades.map((grade, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-1">
                    <h5 className="font-medium text-gray-900">{grade.subject}</h5>
                    <Badge className={getGradeBadgeColor(grade.letterGrade)}>
                      {grade.letterGrade}
                    </Badge>
                    {getTrendIcon(grade.trend)}
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span>{grade.assignmentCount} tugas</span>
                    <span>•</span>
                    <span>Update: {new Date(grade.lastUpdated).toLocaleDateString('id-ID')}</span>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className={`text-lg font-bold ${getGradeColor(grade.currentGrade)}`}>
                    {grade.currentGrade.toFixed(1)}
                  </div>
                  <div className="text-sm text-gray-500">
                    / {grade.maxGrade}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Achievement Summary */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
              <Award className="h-6 w-6 text-green-600 mx-auto mb-1" />
              <div className="text-lg font-bold text-green-700">
                {grades.filter(g => g.letterGrade === 'A').length}
              </div>
              <div className="text-sm text-green-600">Nilai A</div>
            </div>
            
            <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
              <TrendingUp className="h-6 w-6 text-blue-600 mx-auto mb-1" />
              <div className="text-lg font-bold text-blue-700">
                {grades.filter(g => g.trend === 'up').length}
              </div>
              <div className="text-sm text-blue-600">Meningkat</div>
            </div>
          </div>

          <div className="pt-4 border-t">
            <Button variant="outline" className="w-full" asChild>
              <Link href="/siswa/nilai">
                Lihat Detail Nilai
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}