import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BookOpen, Clock, Calendar, AlertTriangle, CheckCircle } from 'lucide-react';
import Link from 'next/link';

interface Assignment {
  id: number;
  title: string;
  subject: string;
  teacher: string;
  dueDate: string;
  status: 'not_started' | 'in_progress' | 'submitted' | 'graded';
  priority: 'low' | 'medium' | 'high';
  description: string;
  maxScore: number;
  submittedScore?: number;
}

interface ActiveAssignmentsProps {
  assignments: Assignment[];
}

export function ActiveAssignments({ assignments }: ActiveAssignmentsProps) {
  const getStatusColor = (status: Assignment['status']) => {
    switch (status) {
      case 'not_started':
        return 'bg-red-100 text-red-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'submitted':
        return 'bg-blue-100 text-blue-800';
      case 'graded':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: Assignment['status']) => {
    switch (status) {
      case 'not_started':
        return 'Belum Mulai';
      case 'in_progress':
        return 'Sedang Dikerjakan';
      case 'submitted':
        return 'Sudah Dikumpulkan';
      case 'graded':
        return 'Sudah Dinilai';
      default:
        return 'Unknown';
    }
  };

  const getPriorityColor = (priority: Assignment['priority']) => {
    switch (priority) {
      case 'high':
        return 'text-red-600';
      case 'medium':
        return 'text-yellow-600';
      case 'low':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  const getDaysUntilDue = (dueDate: string) => {
    const due = new Date(dueDate);
    const now = new Date();
    const diffTime = due.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const formatDueDate = (dueDate: string) => {
    const date = new Date(dueDate);
    return date.toLocaleDateString('id-ID', {
      weekday: 'short',
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (assignments.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BookOpen className="h-5 w-5" />
            <span>Tugas Aktif</span>
          </CardTitle>
          <CardDescription>Tugas yang perlu dikerjakan</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-2">Tidak ada tugas aktif</p>
            <p className="text-sm text-gray-500">
              Semua tugas sudah selesai. Bagus sekali!
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <BookOpen className="h-5 w-5" />
          <span>Tugas Aktif</span>
        </CardTitle>
        <CardDescription>
          {assignments.length} tugas perlu perhatian
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {assignments.map((assignment) => {
            const daysUntilDue = getDaysUntilDue(assignment.dueDate);
            const isOverdue = daysUntilDue < 0;
            const isDueSoon = daysUntilDue <= 2 && daysUntilDue >= 0;

            return (
              <div
                key={assignment.id}
                className={`p-4 border rounded-lg transition-colors ${
                  isOverdue 
                    ? 'border-red-200 bg-red-50' 
                    : isDueSoon 
                    ? 'border-yellow-200 bg-yellow-50'
                    : 'border-gray-200 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium text-gray-900">{assignment.title}</h4>
                      <Badge className={getStatusColor(assignment.status)}>
                        {getStatusText(assignment.status)}
                      </Badge>
                      {assignment.priority === 'high' && (
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                      <span className="font-medium">{assignment.subject}</span>
                      <span>•</span>
                      <span>{assignment.teacher}</span>
                    </div>
                    
                    <p className="text-sm text-gray-700 mb-3">
                      {assignment.description}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className={isOverdue ? 'text-red-600 font-medium' : 'text-gray-600'}>
                        {formatDueDate(assignment.dueDate)}
                      </span>
                    </div>
                    
                    {isOverdue ? (
                      <span className="text-red-600 font-medium">
                        Terlambat {Math.abs(daysUntilDue)} hari
                      </span>
                    ) : isDueSoon ? (
                      <span className="text-yellow-600 font-medium">
                        {daysUntilDue === 0 ? 'Hari ini' : `${daysUntilDue} hari lagi`}
                      </span>
                    ) : (
                      <span className="text-gray-600">
                        {daysUntilDue} hari lagi
                      </span>
                    )}
                    
                    {assignment.status === 'graded' && assignment.submittedScore && (
                      <div className="flex items-center space-x-1">
                        <span className="text-green-600 font-medium">
                          Nilai: {assignment.submittedScore}/{assignment.maxScore}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {assignment.status === 'not_started' && (
                      <Button size="sm" asChild>
                        <Link href={`/siswa/tugas/${assignment.id}`}>
                          Mulai Kerjakan
                        </Link>
                      </Button>
                    )}
                    
                    {assignment.status === 'in_progress' && (
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/siswa/tugas/${assignment.id}`}>
                          Lanjutkan
                        </Link>
                      </Button>
                    )}
                    
                    {assignment.status === 'submitted' && (
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/siswa/tugas/${assignment.id}`}>
                          Lihat Pengumpulan
                        </Link>
                      </Button>
                    )}
                    
                    {assignment.status === 'graded' && (
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/siswa/tugas/${assignment.id}`}>
                          Lihat Nilai
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="mt-4 pt-4 border-t">
          <Button variant="outline" className="w-full" asChild>
            <Link href="/siswa/tugas">
              Lihat Semua Tugas
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}