import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Trophy, 
  Star, 
  Award, 
  Target, 
  TrendingUp,
  Zap,
  Crown,
  Medal
} from 'lucide-react';
import Link from 'next/link';

interface BadgeEarned {
  id: number;
  name: string;
  description: string;
  icon: string;
  earnedAt: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

interface GamificationStatsProps {
  currentXP: number;
  currentLevel: number;
  xpToNextLevel: number;
  totalXP: number;
  classRank: number;
  totalStudents: number;
  badges: BadgeEarned[];
  weeklyXP: number;
  streak: number;
}

export function GamificationStats({
  currentXP,
  currentLevel,
  xpToNextLevel,
  totalXP,
  classRank,
  totalStudents,
  badges,
  weeklyXP,
  streak
}: GamificationStatsProps) {
  const progressToNextLevel = ((currentXP) / (currentXP + xpToNextLevel)) * 100;
  
  const getBadgeRarityColor = (rarity: BadgeEarned['rarity']) => {
    switch (rarity) {
      case 'common':
        return 'bg-gray-100 text-gray-800 border-gray-300';
      case 'rare':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'epic':
        return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'legendary':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getRankIcon = () => {
    if (classRank === 1) return <Crown className="h-5 w-5 text-yellow-500" />;
    if (classRank <= 3) return <Medal className="h-5 w-5 text-orange-500" />;
    if (classRank <= 10) return <Trophy className="h-5 w-5 text-blue-500" />;
    return <Target className="h-5 w-5 text-gray-500" />;
  };

  const getRankColor = () => {
    if (classRank === 1) return 'text-yellow-600';
    if (classRank <= 3) return 'text-orange-600';
    if (classRank <= 10) return 'text-blue-600';
    return 'text-gray-600';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Star className="h-5 w-5" />
          <span>Progress Gamifikasi</span>
        </CardTitle>
        <CardDescription>XP dan pencapaian Anda</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Level Progress */}
          <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200">
            <div className="flex items-center justify-between mb-3">
              <div>
                <h4 className="font-semibold text-gray-900">Level Saat Ini</h4>
                <p className="text-sm text-gray-600">
                  {currentXP.toLocaleString()} / {(currentXP + xpToNextLevel).toLocaleString()} XP
                </p>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-purple-600">
                  {currentLevel}
                </div>
                <div className="text-sm text-gray-600">
                  {xpToNextLevel.toLocaleString()} XP lagi
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress ke Level {currentLevel + 1}</span>
                <span>{progressToNextLevel.toFixed(1)}%</span>
              </div>
              <Progress value={progressToNextLevel} className="h-3" />
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-center mb-2">
                {getRankIcon()}
              </div>
              <div className={`text-xl font-bold ${getRankColor()}`}>
                #{classRank}
              </div>
              <div className="text-sm text-gray-600">
                dari {totalStudents} siswa
              </div>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
              <Zap className="h-6 w-6 text-green-600 mx-auto mb-2" />
              <div className="text-xl font-bold text-green-700">
                {weeklyXP.toLocaleString()}
              </div>
              <div className="text-sm text-green-600">XP minggu ini</div>
            </div>
            
            <div className="text-center p-4 bg-orange-50 rounded-lg border border-orange-200">
              <TrendingUp className="h-6 w-6 text-orange-600 mx-auto mb-2" />
              <div className="text-xl font-bold text-orange-700">
                {totalXP.toLocaleString()}
              </div>
              <div className="text-sm text-orange-600">Total XP</div>
            </div>
            
            <div className="text-center p-4 bg-red-50 rounded-lg border border-red-200">
              <Award className="h-6 w-6 text-red-600 mx-auto mb-2" />
              <div className="text-xl font-bold text-red-700">
                {streak}
              </div>
              <div className="text-sm text-red-600">Hari berturut</div>
            </div>
          </div>

          {/* Recent Badges */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-gray-900">Badge Terbaru</h4>
              <Button variant="ghost" size="sm" asChild>
                <Link href="/siswa/badges">
                  Lihat Semua ({badges.length})
                </Link>
              </Button>
            </div>
            
            {badges.length === 0 ? (
              <div className="text-center py-4">
                <Award className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Belum ada badge</p>
                <p className="text-xs text-gray-500">
                  Kerjakan tugas dan hadir ke kelas untuk mendapatkan badge
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {badges.slice(0, 3).map((badge) => (
                  <div
                    key={badge.id}
                    className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                      <Award className="h-5 w-5 text-white" />
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h5 className="font-medium text-gray-900">{badge.name}</h5>
                        <Badge className={getBadgeRarityColor(badge.rarity)}>
                          {badge.rarity}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600">{badge.description}</p>
                    </div>
                    
                    <div className="text-xs text-gray-500">
                      {new Date(badge.earnedAt).toLocaleDateString('id-ID')}
                    </div>
                  </div>
                ))}
                
                {badges.length > 3 && (
                  <Button variant="outline" size="sm" className="w-full" asChild>
                    <Link href="/siswa/badges">
                      Lihat {badges.length - 3} Badge Lainnya
                    </Link>
                  </Button>
                )}
              </div>
            )}
          </div>

          {/* Motivational Message */}
          <div className="p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
            <div className="flex items-center space-x-2">
              <Star className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {classRank === 1 
                    ? "Hebat! Anda peringkat 1 di kelas!" 
                    : classRank <= 5 
                    ? "Bagus! Anda masuk 5 besar!" 
                    : "Terus semangat! Tingkatkan XP Anda!"}
                </p>
                <p className="text-xs text-gray-600">
                  Kerjakan tugas dan hadir ke kelas untuk mendapatkan lebih banyak XP
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}