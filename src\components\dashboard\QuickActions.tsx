import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Calendar, 
  BookOpen, 
  Users, 
  BarChart3, 
  Plus,
  FileText,
  UserPlus,
  ClipboardList
} from 'lucide-react';
import Link from 'next/link';

interface QuickAction {
  title: string;
  description: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  badge?: number;
}

interface QuickActionsProps {
  userRole: 'teacher' | 'student';
}

export function QuickActions({ userRole }: QuickActionsProps) {
  const teacherActions: QuickAction[] = [
    {
      title: 'Catat Absensi',
      description: 'Rekam kehadiran siswa hari ini',
      href: '/guru/absensi',
      icon: Calendar,
      color: 'bg-blue-500 hover:bg-blue-600',
    },
    {
      title: 'Buat Tugas',
      description: 'Buat tugas baru untuk siswa',
      href: '/guru/tugas/buat',
      icon: Plus,
      color: 'bg-green-500 hover:bg-green-600',
    },
    {
      title: '<PERSON><PERSON><PERSON>',
      description: 'Atur kelas dan mata pelajaran',
      href: '/guru/kelas',
      icon: Users,
      color: 'bg-purple-500 hover:bg-purple-600',
    },
    {
      title: 'Lihat Laporan',
      description: 'Analisis performa dan kehadiran',
      href: '/guru/laporan',
      icon: BarChart3,
      color: 'bg-orange-500 hover:bg-orange-600',
    },
    {
      title: 'Tambah Siswa',
      description: 'Daftarkan siswa baru',
      href: '/guru/siswa/tambah',
      icon: UserPlus,
      color: 'bg-indigo-500 hover:bg-indigo-600',
    },
    {
      title: 'Export Data',
      description: 'Download laporan absensi',
      href: '/guru/export',
      icon: FileText,
      color: 'bg-gray-500 hover:bg-gray-600',
    },
  ];

  const studentActions: QuickAction[] = [
    {
      title: 'Tugas Aktif',
      description: 'Lihat dan kerjakan tugas',
      href: '/siswa/tugas',
      icon: BookOpen,
      color: 'bg-blue-500 hover:bg-blue-600',
      badge: 3,
    },
    {
      title: 'Nilai Saya',
      description: 'Cek nilai dan progress',
      href: '/siswa/nilai',
      icon: BarChart3,
      color: 'bg-green-500 hover:bg-green-600',
    },
    {
      title: 'Absensi Saya',
      description: 'Lihat rekap kehadiran',
      href: '/siswa/absensi',
      icon: Calendar,
      color: 'bg-purple-500 hover:bg-purple-600',
    },
    {
      title: 'Jadwal Kelas',
      description: 'Lihat jadwal pelajaran',
      href: '/siswa/jadwal',
      icon: ClipboardList,
      color: 'bg-orange-500 hover:bg-orange-600',
    },
  ];

  const actions = userRole === 'teacher' ? teacherActions : studentActions;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Aksi Cepat</CardTitle>
        <CardDescription>
          {userRole === 'teacher' 
            ? 'Akses fitur yang sering digunakan dengan cepat'
            : 'Akses informasi penting dengan mudah'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {actions.map((action) => (
            <Button
              key={action.href}
              variant="outline"
              className="h-auto p-4 flex flex-col items-start space-y-2 hover:shadow-md transition-all"
              asChild
            >
              <Link href={action.href}>
                <div className="flex items-center justify-between w-full">
                  <div className={`p-2 rounded-lg ${action.color} text-white`}>
                    <action.icon className="h-5 w-5" />
                  </div>
                  {action.badge && (
                    <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1">
                      {action.badge}
                    </span>
                  )}
                </div>
                <div className="text-left">
                  <h4 className="font-medium text-gray-900">{action.title}</h4>
                  <p className="text-sm text-gray-600 mt-1">{action.description}</p>
                </div>
              </Link>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}