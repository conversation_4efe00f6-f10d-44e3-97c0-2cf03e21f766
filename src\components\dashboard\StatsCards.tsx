import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Users, 
  Calendar, 
  BookOpen, 
  TrendingUp, 
  TrendingDown,
  Minus,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';

interface Stat {
  title: string;
  value: string | number;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  trend?: {
    value: number;
    isPositive: boolean;
    period: string;
  };
  color: string;
}

interface StatsCardsProps {
  userRole: 'teacher' | 'student';
  stats: {
    totalStudents?: number;
    totalClasses?: number;
    todayAttendance?: number;
    pendingAssignments?: number;
    completedAssignments?: number;
    averageGrade?: number;
    totalXP?: number;
    currentLevel?: number;
    attendanceRate?: number;
    weeklyProgress?: number;
  };
}

export function StatsCards({ userRole, stats }: StatsCardsProps) {
  const teacherStats: Stat[] = [
    {
      title: 'Total Siswa',
      value: stats.totalStudents || 0,
      description: 'Siswa aktif di semua kelas',
      icon: Users,
      color: 'text-blue-600',
      trend: {
        value: 5,
        isPositive: true,
        period: 'dari bulan lalu'
      }
    },
    {
      title: 'Kelas Aktif',
      value: stats.totalClasses || 0,
      description: 'Kelas yang sedang diajar',
      icon: BookOpen,
      color: 'text-green-600',
    },
    {
      title: 'Kehadiran Hari Ini',
      value: `${stats.todayAttendance || 0}%`,
      description: 'Rata-rata kehadiran siswa',
      icon: Calendar,
      color: 'text-purple-600',
      trend: {
        value: 2,
        isPositive: true,
        period: 'dari kemarin'
      }
    },
    {
      title: 'Tugas Pending',
      value: stats.pendingAssignments || 0,
      description: 'Tugas menunggu penilaian',
      icon: Clock,
      color: 'text-orange-600',
      trend: {
        value: 3,
        isPositive: false,
        period: 'dari minggu lalu'
      }
    },
  ];

  const studentStats: Stat[] = [
    {
      title: 'Total XP',
      value: stats.totalXP?.toLocaleString() || '0',
      description: 'Experience Points yang dikumpulkan',
      icon: TrendingUp,
      color: 'text-blue-600',
      trend: {
        value: stats.weeklyProgress || 0,
        isPositive: (stats.weeklyProgress || 0) > 0,
        period: 'minggu ini'
      }
    },
    {
      title: 'Level Saat Ini',
      value: stats.currentLevel || 1,
      description: 'Level berdasarkan XP',
      icon: CheckCircle,
      color: 'text-green-600',
    },
    {
      title: 'Tingkat Kehadiran',
      value: `${stats.attendanceRate || 0}%`,
      description: 'Kehadiran bulan ini',
      icon: Calendar,
      color: 'text-purple-600',
      trend: {
        value: 5,
        isPositive: true,
        period: 'dari bulan lalu'
      }
    },
    {
      title: 'Tugas Selesai',
      value: `${stats.completedAssignments || 0}/${(stats.completedAssignments || 0) + (stats.pendingAssignments || 0)}`,
      description: 'Tugas yang sudah dikerjakan',
      icon: BookOpen,
      color: 'text-orange-600',
    },
  ];

  const currentStats = userRole === 'teacher' ? teacherStats : studentStats;

  const getTrendIcon = (isPositive: boolean) => {
    if (isPositive) {
      return <TrendingUp className="h-4 w-4 text-green-600" />;
    } else {
      return <TrendingDown className="h-4 w-4 text-red-600" />;
    }
  };

  const getTrendColor = (isPositive: boolean) => {
    return isPositive ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {currentStats.map((stat, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {stat.title}
            </CardTitle>
            <stat.icon className={`h-5 w-5 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-gray-900">
                {stat.value}
              </div>
              
              <p className="text-xs text-gray-600">
                {stat.description}
              </p>
              
              {stat.trend && (
                <div className="flex items-center space-x-1">
                  {getTrendIcon(stat.trend.isPositive)}
                  <span className={`text-xs font-medium ${getTrendColor(stat.trend.isPositive)}`}>
                    {stat.trend.isPositive ? '+' : ''}{stat.trend.value}%
                  </span>
                  <span className="text-xs text-gray-500">
                    {stat.trend.period}
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}