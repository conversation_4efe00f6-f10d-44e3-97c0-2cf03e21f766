import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Clock, MapPin, Users, ChevronRight } from 'lucide-react';
import Link from 'next/link';

interface ScheduleItem {
  id: number;
  subject: string;
  className: string;
  time: string;
  room: string;
  studentCount: number;
  status: 'upcoming' | 'ongoing' | 'completed';
}

interface TeachingScheduleProps {
  schedules: ScheduleItem[];
  date?: string;
}

export function TeachingSchedule({ schedules, date }: TeachingScheduleProps) {
  const today = date || new Date().toLocaleDateString('id-ID', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const getStatusColor = (status: ScheduleItem['status']) => {
    switch (status) {
      case 'upcoming':
        return 'bg-blue-100 text-blue-800';
      case 'ongoing':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: ScheduleItem['status']) => {
    switch (status) {
      case 'upcoming':
        return 'Akan Datang';
      case 'ongoing':
        return 'Sedang Berlangsung';
      case 'completed':
        return 'Selesai';
      default:
        return 'Unknown';
    }
  };

  if (schedules.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <span>Jadwal Mengajar Hari Ini</span>
          </CardTitle>
          <CardDescription>{today}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-2">Tidak ada jadwal mengajar hari ini</p>
            <p className="text-sm text-gray-500">
              Nikmati waktu luang Anda atau persiapkan materi untuk besok
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Clock className="h-5 w-5" />
          <span>Jadwal Mengajar Hari Ini</span>
        </CardTitle>
        <CardDescription>{today}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {schedules.map((schedule) => (
            <div
              key={schedule.id}
              className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h4 className="font-medium text-gray-900">{schedule.subject}</h4>
                  <Badge className={getStatusColor(schedule.status)}>
                    {getStatusText(schedule.status)}
                  </Badge>
                </div>
                
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>{schedule.className}</span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{schedule.time}</span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <MapPin className="h-4 w-4" />
                    <span>{schedule.room}</span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>{schedule.studentCount} siswa</span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {schedule.status === 'upcoming' && (
                  <Button size="sm" variant="outline" asChild>
                    <Link href={`/guru/absensi?class=${schedule.className}`}>
                      Absensi
                    </Link>
                  </Button>
                )}
                
                {schedule.status === 'ongoing' && (
                  <Button size="sm" asChild>
                    <Link href={`/guru/kelas/${schedule.id}`}>
                      Kelola Kelas
                    </Link>
                  </Button>
                )}
                
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/guru/kelas/${schedule.id}`}>
                    <ChevronRight className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 pt-4 border-t">
          <Button variant="outline" className="w-full" asChild>
            <Link href="/guru/jadwal">
              Lihat Jadwal Lengkap
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}