import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Trophy, Medal, Award, Star, TrendingUp } from 'lucide-react';
import Link from 'next/link';

interface Student {
  id: number;
  name: string;
  className: string;
  totalXP: number;
  level: number;
  badges: number;
  weeklyXP: number;
  rank: number;
}

interface XPLeaderboardProps {
  students: Student[];
  timeframe?: 'week' | 'month' | 'all';
  showClassFilter?: boolean;
}

export function XPLeaderboard({ 
  students, 
  timeframe = 'week',
  showClassFilter = false 
}: XPLeaderboardProps) {
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 2:
        return <Medal className="h-5 w-5 text-gray-400" />;
      case 3:
        return <Award className="h-5 w-5 text-amber-600" />;
      default:
        return <span className="text-sm font-bold text-gray-500">#{rank}</span>;
    }
  };

  const getRankBadgeColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 2:
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 3:
        return 'bg-amber-100 text-amber-800 border-amber-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getTimeframeText = () => {
    switch (timeframe) {
      case 'week':
        return 'Minggu Ini';
      case 'month':
        return 'Bulan Ini';
      case 'all':
        return 'Sepanjang Masa';
      default:
        return 'Minggu Ini';
    }
  };

  if (students.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Trophy className="h-5 w-5" />
            <span>Leaderboard XP Siswa</span>
          </CardTitle>
          <CardDescription>{getTimeframeText()}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-2">Belum ada data XP siswa</p>
            <p className="text-sm text-gray-500">
              XP akan muncul setelah siswa mulai mengumpulkan tugas dan hadir ke kelas
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Trophy className="h-5 w-5" />
              <span>Leaderboard XP Siswa</span>
            </CardTitle>
            <CardDescription>{getTimeframeText()}</CardDescription>
          </div>
          
          <div className="flex items-center space-x-2">
            {showClassFilter && (
              <Button variant="outline" size="sm">
                Semua Kelas
              </Button>
            )}
            <Button variant="outline" size="sm" asChild>
              <Link href="/guru/gamifikasi">
                <TrendingUp className="h-4 w-4 mr-1" />
                Detail
              </Link>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {students.slice(0, 10).map((student) => (
            <div
              key={student.id}
              className={`flex items-center justify-between p-3 rounded-lg border transition-colors ${
                student.rank <= 3 
                  ? 'bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200' 
                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-8 h-8">
                  {getRankIcon(student.rank)}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h4 className="font-medium text-gray-900">{student.name}</h4>
                    <Badge 
                      variant="outline" 
                      className={getRankBadgeColor(student.rank)}
                    >
                      Level {student.level}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{student.className}</p>
                </div>
              </div>
              
              <div className="text-right">
                <div className="flex items-center space-x-2">
                  <div className="text-right">
                    <p className="font-bold text-lg text-primary-600">
                      {student.totalXP.toLocaleString()} XP
                    </p>
                    {timeframe === 'week' && (
                      <p className="text-xs text-gray-500">
                        +{student.weeklyXP} minggu ini
                      </p>
                    )}
                  </div>
                  
                  {student.badges > 0 && (
                    <div className="flex items-center space-x-1">
                      <Award className="h-4 w-4 text-amber-500" />
                      <span className="text-sm font-medium text-amber-600">
                        {student.badges}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {students.length > 10 && (
          <div className="mt-4 pt-4 border-t">
            <Button variant="outline" className="w-full" asChild>
              <Link href="/guru/gamifikasi">
                Lihat Semua Siswa ({students.length})
              </Link>
            </Button>
          </div>
        )}
        
        <div className="mt-4 pt-4 border-t">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>Total siswa aktif: {students.length}</span>
            <span>Rata-rata XP: {Math.round(students.reduce((acc, s) => acc + s.totalXP, 0) / students.length).toLocaleString()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}