'use client';

'use client';

import { ReactNode } from 'react';
import { LogoutButton } from '@/components/auth/LogoutButton';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  BookOpen, 
  Users, 
  Calendar, 
  BarChart3, 
  Settings,
  Bell,
  Menu,
  X
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

interface DashboardLayoutProps {
  children: ReactNode;
  user: {
    name: string;
    email: string;
    role: 'teacher' | 'student';
    isSuperAdmin?: boolean;
  };
  title: string;
  subtitle?: string;
}

interface NavItem {
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: number;
  adminOnly?: boolean;
}

export function DashboardLayout({ children, user, title, subtitle }: DashboardLayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const teacherNavItems: NavItem[] = [
    {
      label: 'Dashboard',
      href: '/guru',
      icon: BarChart3,
    },
    {
      label: 'Kelas',
      href: '/guru/kelas',
      icon: Users,
    },
    {
      label: 'Absensi',
      href: '/guru/absensi',
      icon: Calendar,
    },
    {
      label: 'Tugas',
      href: '/guru/tugas',
      icon: BookOpen,
    },
    {
      label: 'Laporan',
      href: '/guru/laporan',
      icon: BarChart3,
    },
    {
      label: 'Pengaturan',
      href: '/guru/pengaturan',
      icon: Settings,
      adminOnly: true,
    },
  ];

  const studentNavItems: NavItem[] = [
    {
      label: 'Dashboard',
      href: '/siswa',
      icon: BarChart3,
    },
    {
      label: 'Tugas',
      href: '/siswa/tugas',
      icon: BookOpen,
    },
    {
      label: 'Nilai',
      href: '/siswa/nilai',
      icon: BarChart3,
    },
    {
      label: 'Absensi',
      href: '/siswa/absensi',
      icon: Calendar,
    },
  ];

  const navItems = user.role === 'teacher' ? teacherNavItems : studentNavItems;
  const filteredNavItems = navItems.filter(item => 
    !item.adminOnly || (item.adminOnly && user.isSuperAdmin)
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Title */}
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? (
                  <X className="h-5 w-5" />
                ) : (
                  <Menu className="h-5 w-5" />
                )}
              </Button>
              
              <Link href="/" className="flex items-center space-x-2">
                <BookOpen className="h-8 w-8 text-primary-600" />
                <span className="text-xl font-bold text-gray-900 hidden sm:block">
                  GuruFlow
                </span>
              </Link>
              
              <div className="hidden md:block border-l border-gray-300 pl-4">
                <h1 className="text-lg font-semibold text-gray-900">{title}</h1>
                {subtitle && (
                  <p className="text-sm text-gray-600">{subtitle}</p>
                )}
              </div>
            </div>

            {/* User Info and Actions */}
            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <Button variant="ghost" size="sm" className="relative">
                <Bell className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  3
                </span>
              </Button>

              {/* User Info */}
              <div className="hidden md:flex items-center space-x-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{user.name}</p>
                  <p className="text-xs text-gray-600">
                    {user.role === 'teacher' ? 'Guru' : 'Siswa'}
                    {user.isSuperAdmin && ' • Admin'}
                  </p>
                </div>
                <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {user.name.charAt(0).toUpperCase()}
                  </span>
                </div>
              </div>

              <LogoutButton variant="ghost" size="sm" />
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t bg-white">
            <div className="px-4 py-2 space-y-1">
              {filteredNavItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <item.icon className="h-5 w-5" />
                  <span>{item.label}</span>
                  {item.badge && (
                    <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1">
                      {item.badge}
                    </span>
                  )}
                </Link>
              ))}
            </div>
          </div>
        )}
      </header>

      <div className="flex">
        {/* Sidebar - Desktop */}
        <aside className="hidden md:flex md:flex-shrink-0">
          <div className="flex flex-col w-64">
            <div className="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white border-r">
              <nav className="mt-5 flex-1 px-4 space-y-1">
                {filteredNavItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                  >
                    <item.icon className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" />
                    {item.label}
                    {item.badge && (
                      <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                ))}
              </nav>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 overflow-hidden">
          <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            {/* Mobile Title */}
            <div className="md:hidden mb-6">
              <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
              {subtitle && (
                <p className="text-gray-600">{subtitle}</p>
              )}
            </div>
            
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}