'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { LogoutButton } from '@/components/auth/LogoutButton';
import { 
  Home, 
  Users, 
  Calendar, 
  BookOpen, 
  BarChart3, 
  User,
  FileText,
  Settings,
  UserPlus
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: number;
  adminOnly?: boolean;
}

interface DesktopSidebarProps {
  user?: {
    name: string;
    email: string;
    role: 'teacher' | 'student';
    isSuperAdmin?: boolean;
  } | null;
}

export function DesktopSidebar({ user }: DesktopSidebarProps) {
  const pathname = usePathname();

  // Handle case where user is not yet loaded
  if (!user) {
    return (
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow pt-5 bg-white border-r border-gray-200 overflow-y-auto">
          <div className="flex items-center justify-center h-16">
            <div className="animate-pulse bg-gray-200 h-8 w-32 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  const teacherNavItems: NavItem[] = [
    {
      title: 'Dashboard',
      href: '/guru',
      icon: Home,
    },
    {
      title: 'Kelas',
      href: '/guru/kelas',
      icon: Users,
    },
    {
      title: 'Siswa',
      href: '/guru/siswa',
      icon: UserPlus,
    },
    {
      title: 'Absensi',
      href: '/guru/absensi',
      icon: Calendar,
    },
    {
      title: 'Tugas',
      href: '/guru/tugas',
      icon: BookOpen,
    },
    {
      title: 'Laporan',
      href: '/guru/laporan',
      icon: BarChart3,
    },
    {
      title: 'Pengaturan',
      href: '/guru/pengaturan',
      icon: Settings,
      adminOnly: true,
    },
  ];

  const studentNavItems: NavItem[] = [
    {
      title: 'Dashboard',
      href: '/siswa',
      icon: Home,
    },
    {
      title: 'Tugas',
      href: '/siswa/tugas',
      icon: BookOpen,
      badge: 3,
    },
    {
      title: 'Nilai',
      href: '/siswa/nilai',
      icon: BarChart3,
    },
    {
      title: 'Absensi',
      href: '/siswa/absensi',
      icon: Calendar,
    },
    {
      title: 'Jadwal',
      href: '/siswa/jadwal',
      icon: FileText,
    },
    {
      title: 'Profil',
      href: '/siswa/profil',
      icon: User,
    },
  ];

  const navItems = user.role === 'teacher' ? teacherNavItems : studentNavItems;
  const filteredNavItems = navItems.filter(item => 
    !item.adminOnly || (item.adminOnly && user.isSuperAdmin)
  );

  return (
    <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
      <div className="flex flex-col flex-grow pt-5 bg-white border-r border-gray-200 overflow-y-auto">
        {/* User Info */}
        <div className="flex items-center flex-shrink-0 px-4 pb-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-white font-medium">
                {user.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user.name}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {user.role === 'teacher' ? 'Guru' : 'Siswa'}
                {user.isSuperAdmin && ' • Admin'}
              </p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="mt-5 flex-1 px-4 space-y-1">
          {filteredNavItems.map((item) => {
            const isActive = pathname === item.href || 
              (item.href !== '/guru' && item.href !== '/siswa' && pathname.startsWith(item.href));
            
            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                  isActive
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                )}
              >
                <item.icon 
                  className={cn(
                    'mr-3 h-5 w-5 transition-colors',
                    isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-500'
                  )} 
                />
                <span className="flex-1">{item.title}</span>
                {item.badge && (
                  <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-1">
                    {item.badge}
                  </span>
                )}
              </Link>
            );
          })}
        </nav>

        {/* Footer */}
        <div className="flex-shrink-0 p-4 border-t border-gray-200">
          <LogoutButton 
            variant="outline" 
            size="sm" 
            className="w-full justify-start"
          />
        </div>
      </div>
    </div>
  );
}