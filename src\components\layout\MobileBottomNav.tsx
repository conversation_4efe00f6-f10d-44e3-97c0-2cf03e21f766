'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  Home,
  Users,
  BookOpen,
  Calendar,
  BarChart3,
  FileText,
  User,
  GraduationCap
} from 'lucide-react';

interface MobileBottomNavProps {
  userRole?: 'teacher' | 'student';
}

const teacherNavItems = [
  { href: '/guru', label: 'Home', icon: Home },
  { href: '/guru/kelas', label: 'Kelas', icon: BookOpen },
  { href: '/guru/absensi', label: 'Absensi', icon: Calendar },
  { href: '/guru/laporan', label: 'Laporan', icon: BarChart3 },
];

const studentNavItems = [
  { href: '/siswa', label: 'Home', icon: Home },
  { href: '/siswa/tugas', label: 'Tugas', icon: FileText },
  { href: '/siswa/nilai', label: '<PERSON><PERSON>', icon: BarChart3 },
  { href: '/siswa/profil', label: 'Profil', icon: User },
];

export function MobileBottomNav({ userRole }: MobileBottomNavProps) {
  const pathname = usePathname();
  const navItems = userRole === 'teacher' ? teacherNavItems : studentNavItems;

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-2 py-1 z-50 lg:hidden safe-area-bottom">
      <div className="flex justify-around items-center">
        {navItems.map((item) => {
          const Icon = item.icon;
          const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
          
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center py-2 px-3 rounded-lg transition-all duration-200 min-w-[60px]",
                "active:scale-95 touch-manipulation",
                isActive
                  ? "text-blue-600 bg-blue-50"
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              )}
            >
              <Icon className={cn(
                "h-5 w-5 mb-1 transition-colors",
                isActive ? "text-blue-600" : "text-gray-600"
              )} />
              <span className={cn(
                "text-xs font-medium transition-colors",
                isActive ? "text-blue-600" : "text-gray-600"
              )}>
                {item.label}
              </span>
            </Link>
          );
        })}
      </div>
    </div>
  );
}