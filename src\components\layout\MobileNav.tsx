'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { 
  Home, 
  Users, 
  Calendar, 
  BookOpen, 
  BarChart3, 
  User,
  FileText,
  Settings,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileNavProps {
  isOpen: boolean;
  onClose: () => void;
  userRole?: 'teacher' | 'student';
}

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: number;
}

export function MobileNav({ isOpen, onClose, userRole }: MobileNavProps) {
  const pathname = usePathname();

  // Close on route change
  useEffect(() => {
    onClose();
  }, [pathname, onClose]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const teacherNavItems: NavItem[] = [
    {
      title: 'Dashboard',
      href: '/guru',
      icon: Home,
    },
    {
      title: 'Kelas',
      href: '/guru/kelas',
      icon: Users,
    },
    {
      title: 'Siswa',
      href: '/guru/siswa',
      icon: User,
    },
    {
      title: 'Absensi',
      href: '/guru/absensi',
      icon: Calendar,
    },
    {
      title: 'Tugas',
      href: '/guru/tugas',
      icon: BookOpen,
    },
    {
      title: 'Laporan',
      href: '/guru/laporan',
      icon: BarChart3,
    },
    {
      title: 'Pengaturan',
      href: '/guru/pengaturan',
      icon: Settings,
    },
  ];

  const studentNavItems: NavItem[] = [
    {
      title: 'Dashboard',
      href: '/siswa',
      icon: Home,
    },
    {
      title: 'Tugas',
      href: '/siswa/tugas',
      icon: BookOpen,
      badge: 3,
    },
    {
      title: 'Nilai',
      href: '/siswa/nilai',
      icon: BarChart3,
    },
    {
      title: 'Absensi',
      href: '/siswa/absensi',
      icon: Calendar,
    },
    {
      title: 'Jadwal',
      href: '/siswa/jadwal',
      icon: FileText,
    },
    {
      title: 'Profil',
      href: '/siswa/profil',
      icon: User,
    },
  ];

  const navItems = userRole === 'teacher' ? teacherNavItems : studentNavItems;

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
        onClick={onClose}
      />
      
      {/* Mobile Navigation Panel */}
      <div className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl lg:hidden transform transition-transform duration-300 ease-in-out">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold text-gray-900">
            GuruFlow
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="p-2"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Navigation Items */}
        <nav className="flex-1 px-4 py-6 space-y-2">
          {navItems.map((item) => {
            const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
            
            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors',
                  isActive
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                )}
              >
                <item.icon className="mr-3 h-5 w-5" />
                <span className="flex-1">{item.title}</span>
                {item.badge && (
                  <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-1">
                    {item.badge}
                  </span>
                )}
              </Link>
            );
          })}
        </nav>

        {/* Footer */}
        <div className="p-4 border-t">
          <div className="text-xs text-gray-500 text-center">
            GuruFlow v1.0
          </div>
        </div>
      </div>
    </>
  );
}