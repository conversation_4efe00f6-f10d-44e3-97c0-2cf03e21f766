'use client';

import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useIsMobile } from '@/hooks/useResponsive';
import { MobileNav } from './MobileNav';
import { MobileBottomNav } from './MobileBottomNav';
import { DesktopSidebar } from './DesktopSidebar';
import { Button } from '@/components/ui/button';
import { LogoutButton } from '@/components/auth/LogoutButton';
import { Menu, Bell, Search } from 'lucide-react';
import { ResponsiveContainer } from '@/components/ui/responsive-container';

interface ResponsiveDashboardLayoutProps {
  children: React.ReactNode;
  title?: string;
  showBackButton?: boolean;
  onBackClick?: () => void;
}

export function ResponsiveDashboardLayout({ 
  children, 
  title,
  showBackButton = false,
  onBackClick
}: ResponsiveDashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user } = useAuth();
  const isMobile = useIsMobile();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Navigation Sidebar */}
      {isMobile && (
        <MobileNav 
          isOpen={sidebarOpen} 
          onClose={() => setSidebarOpen(false)}
          userRole={user?.role}
        />
      )}
      
      {/* Desktop Sidebar */}
      {!isMobile && (
        <div className="fixed inset-y-0 left-0 w-64 z-30">
          <DesktopSidebar user={user} />
        </div>
      )}
      
      {/* Main Content Area */}
      <div className={cn(
        "min-h-screen",
        !isMobile && "pl-64"
      )}>
        {/* Mobile Header */}
        {isMobile && (
          <div className="sticky top-0 z-20 bg-white shadow-sm border-b border-gray-200 safe-area-top">
            <div className="flex items-center justify-between px-4 py-3">
              {/* Left: Menu or Back Button */}
              <div className="flex items-center space-x-3">
                {showBackButton ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onBackClick}
                    className="touch-target"
                  >
                    <ArrowLeft className="h-5 w-5" />
                  </Button>
                ) : (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSidebarOpen(true)}
                    className="touch-target"
                  >
                    <Menu className="h-5 w-5" />
                  </Button>
                )}
                
                {title && (
                  <h1 className="text-lg font-semibold text-gray-900 truncate">
                    {title}
                  </h1>
                )}
              </div>
              
              {/* Right: Actions */}
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="touch-target"
                >
                  <Search className="h-5 w-5" />
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="touch-target relative"
                >
                  <Bell className="h-5 w-5" />
                  {/* Notification badge */}
                  <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
                </Button>
                
                <LogoutButton variant="ghost" size="sm" className="touch-target" />
              </div>
            </div>
            
            {/* User Info Bar */}
            {user && !title && (
              <div className="px-4 py-2 bg-blue-50 border-t border-blue-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-900">
                      {user.name}
                    </p>
                    <p className="text-xs text-blue-600">
                      {user.role === 'teacher' ? 'Guru' : 'Siswa'}
                    </p>
                  </div>
                  <div className="text-xs text-blue-600">
                    {new Date().toLocaleDateString('id-ID', {
                      weekday: 'short',
                      day: 'numeric',
                      month: 'short'
                    })}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Desktop Header */}
        {!isMobile && (
          <div className="sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200">
            <div className="px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center py-4">
                <div>
                  {title ? (
                    <h1 className="text-2xl font-semibold text-gray-900">
                      {title}
                    </h1>
                  ) : (
                    <h1 className="text-2xl font-semibold text-gray-900">
                      {user?.role === 'teacher' ? 'Dashboard Guru' : 'Dashboard Siswa'}
                    </h1>
                  )}
                </div>
                <div className="flex items-center space-x-4">
                  <Button variant="ghost" size="sm">
                    <Search className="h-5 w-5" />
                  </Button>
                  
                  <Button variant="ghost" size="sm" className="relative">
                    <Bell className="h-5 w-5" />
                    <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
                  </Button>
                  
                  <div className="flex items-center space-x-3">
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        {user?.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {user?.role === 'teacher' ? 'Guru' : 'Siswa'}
                      </p>
                    </div>
                    <LogoutButton variant="outline" size="sm" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Page Content */}
        <main className={cn(
          "flex-1",
          isMobile ? "pb-20" : "pb-6" // Extra padding for mobile bottom nav
        )}>
          <ResponsiveContainer padding="md" className="py-6">
            {children}
          </ResponsiveContainer>
        </main>
      </div>
      
      {/* Mobile Bottom Navigation */}
      {isMobile && (
        <MobileBottomNav userRole={user?.role} />
      )}
    </div>
  );
}

// Import missing ArrowLeft icon
import { ArrowLeft } from 'lucide-react';
import { cn } from '@/lib/utils';