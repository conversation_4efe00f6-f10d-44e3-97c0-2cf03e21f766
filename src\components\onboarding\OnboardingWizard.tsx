'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { <PERSON>Circle, ArrowLeft, ArrowRight } from 'lucide-react';
import { SchoolSetupStep } from './steps/SchoolSetupStep';
import { TeacherSetupStep } from './steps/TeacherSetupStep';
import { CompletionStep } from './steps/CompletionStep';

export interface OnboardingData {
  school: {
    name: string;
    level: 'SD' | 'SMP' | 'SMA' | 'SMK';
    logo?: File | null;
    signature?: File | null;
  };
  teacher: {
    name: string;
    email: string;
    password: string;
    confirmPassword: string;
  };
}

const STEPS = [
  {
    id: 'school',
    title: 'Informasi Sekolah',
    description: 'Masukkan informasi dasar sekolah Anda',
  },
  {
    id: 'teacher',
    title: 'Akun Administrator',
    description: 'Buat akun guru pertama sebagai administrator',
  },
  {
    id: 'complete',
    title: '<PERSON><PERSON><PERSON>',
    description: 'Konfigurasi sekolah telah selesai',
  },
];

export function OnboardingWizard() {
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    school: {
      name: '',
      level: 'SD',
      logo: null,
      signature: null,
    },
    teacher: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });
  const router = useRouter();

  const updateData = (stepData: Partial<OnboardingData>) => {
    setOnboardingData(prev => ({
      ...prev,
      ...stepData,
    }));
  };

  const nextStep = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const submitOnboarding = async () => {
    setIsSubmitting(true);
    
    try {
      // Create FormData for file uploads
      const formData = new FormData();
      
      // Add school data
      formData.append('schoolName', onboardingData.school.name);
      formData.append('schoolLevel', onboardingData.school.level);
      
      if (onboardingData.school.logo) {
        formData.append('logo', onboardingData.school.logo);
      }
      
      if (onboardingData.school.signature) {
        formData.append('signature', onboardingData.school.signature);
      }
      
      // Add teacher data
      formData.append('teacherName', onboardingData.teacher.name);
      formData.append('teacherEmail', onboardingData.teacher.email);
      formData.append('teacherPassword', onboardingData.teacher.password);
      
      const response = await fetch('/api/onboarding', {
        method: 'POST',
        body: formData,
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error?.message || 'Onboarding gagal');
      }
      
      if (result.success) {
        nextStep(); // Move to completion step
      }
    } catch (error) {
      console.error('Onboarding error:', error);
      // Handle error - could show toast or error message
      alert(error instanceof Error ? error.message : 'Terjadi kesalahan saat setup');
    } finally {
      setIsSubmitting(false);
    }
  };

  const completeOnboarding = () => {
    // Redirect to login page after completion
    router.push('/login');
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <SchoolSetupStep
            data={onboardingData.school}
            onUpdate={(schoolData) => updateData({ school: schoolData })}
            onNext={nextStep}
          />
        );
      case 1:
        return (
          <TeacherSetupStep
            data={onboardingData.teacher}
            onUpdate={(teacherData) => updateData({ teacher: teacherData })}
            onNext={submitOnboarding}
            onPrev={prevStep}
            isSubmitting={isSubmitting}
          />
        );
      case 2:
        return (
          <CompletionStep
            schoolName={onboardingData.school.name}
            teacherName={onboardingData.teacher.name}
            teacherEmail={onboardingData.teacher.email}
            onComplete={completeOnboarding}
          />
        );
      default:
        return null;
    }
  };

  const progressPercentage = ((currentStep + 1) / STEPS.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Selamat Datang di GuruFlow
          </h1>
          <p className="text-gray-600">
            Mari siapkan sekolah Anda dalam beberapa langkah mudah
          </p>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            {STEPS.map((step, index) => (
              <div
                key={step.id}
                className={`flex items-center ${
                  index < STEPS.length - 1 ? 'flex-1' : ''
                }`}
              >
                <div
                  className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                    index <= currentStep
                      ? 'bg-primary-600 border-primary-600 text-white'
                      : 'bg-white border-gray-300 text-gray-400'
                  }`}
                >
                  {index < currentStep ? (
                    <CheckCircle className="w-6 h-6" />
                  ) : (
                    <span className="text-sm font-medium">{index + 1}</span>
                  )}
                </div>
                {index < STEPS.length - 1 && (
                  <div
                    className={`flex-1 h-1 mx-4 ${
                      index < currentStep ? 'bg-primary-600' : 'bg-gray-200'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
          
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-1">
              {STEPS[currentStep].title}
            </h2>
            <p className="text-gray-600 text-sm">
              {STEPS[currentStep].description}
            </p>
          </div>
          
          <Progress value={progressPercentage} className="mt-4" />
        </div>

        {/* Step Content */}
        <Card>
          <CardContent className="p-6">
            {renderStep()}
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-6 text-sm text-gray-500">
          Langkah {currentStep + 1} dari {STEPS.length}
        </div>
      </div>
    </div>
  );
}