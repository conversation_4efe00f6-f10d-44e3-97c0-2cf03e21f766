'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, School, User, ArrowRight, Sparkles } from 'lucide-react';

interface CompletionStepProps {
  schoolName: string;
  teacherName: string;
  teacherEmail: string;
  onComplete: () => void;
}

export function CompletionStep({ 
  schoolName, 
  teacherName, 
  teacherEmail, 
  onComplete 
}: CompletionStepProps) {
  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          <div className="bg-green-100 p-4 rounded-full">
            <CheckCircle className="w-12 h-12 text-green-600" />
          </div>
        </div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          Selamat! Setup Berhasil
        </h3>
        <p className="text-gray-600">
          Sekolah Anda telah berhasil dikonfigurasi dan siap digunakan
        </p>
      </div>

      <div className="grid gap-4">
        {/* School Info Card */}
        <Card className="border-green-200 bg-green-50">
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-3">
              <School className="w-5 h-5 text-green-600" />
              <CardTitle className="text-lg text-green-800">
                Informasi Sekolah
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-green-700">Nama Sekolah:</span>
                <span className="text-sm text-green-800">{schoolName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-green-700">Status:</span>
                <span className="text-sm text-green-800 font-medium">Aktif</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Admin Account Card */}
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-3">
              <User className="w-5 h-5 text-blue-600" />
              <CardTitle className="text-lg text-blue-800">
                Akun Administrator
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-blue-700">Nama:</span>
                <span className="text-sm text-blue-800">{teacherName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-blue-700">Email:</span>
                <span className="text-sm text-blue-800">{teacherEmail}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-blue-700">Role:</span>
                <span className="text-sm text-blue-800 font-medium">Super Administrator</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Next Steps */}
      <Card className="border-purple-200 bg-purple-50">
        <CardHeader className="pb-3">
          <div className="flex items-center space-x-3">
            <Sparkles className="w-5 h-5 text-purple-600" />
            <CardTitle className="text-lg text-purple-800">
              Langkah Selanjutnya
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-0.5">
                1
              </div>
              <div>
                <p className="text-sm font-medium text-purple-800">Login ke Dashboard</p>
                <p className="text-xs text-purple-600">
                  Gunakan email dan password yang telah dibuat untuk masuk
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-0.5">
                2
              </div>
              <div>
                <p className="text-sm font-medium text-purple-800">Buat Kelas Pertama</p>
                <p className="text-xs text-purple-600">
                  Mulai dengan membuat kelas dan mata pelajaran
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-0.5">
                3
              </div>
              <div>
                <p className="text-sm font-medium text-purple-800">Tambah Siswa</p>
                <p className="text-xs text-purple-600">
                  Daftarkan siswa secara individual atau import dari CSV
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-0.5">
                4
              </div>
              <div>
                <p className="text-sm font-medium text-purple-800">Mulai Absensi</p>
                <p className="text-xs text-purple-600">
                  Catat kehadiran siswa dan kelola administrasi harian
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Success Message */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center space-x-3">
          <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0" />
          <div>
            <p className="text-sm font-medium text-gray-900">
              Setup selesai! GuruFlow siap digunakan.
            </p>
            <p className="text-xs text-gray-600 mt-1">
              Semua data telah tersimpan dengan aman. Anda dapat mengubah pengaturan 
              sekolah kapan saja melalui menu pengaturan.
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-center pt-4">
        <Button 
          onClick={onComplete}
          size="lg"
          className="min-w-48"
        >
          Masuk ke Dashboard
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}