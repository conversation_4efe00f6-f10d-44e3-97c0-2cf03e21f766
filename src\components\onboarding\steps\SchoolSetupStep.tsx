'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, X, ArrowRight } from 'lucide-react';
import { SCHOOL_LEVELS } from '@/lib/constants';

interface SchoolData {
  name: string;
  level: 'SD' | 'SMP' | 'SMA' | 'SMK';
  logo?: File | null;
  signature?: File | null;
}

interface SchoolSetupStepProps {
  data: SchoolData;
  onUpdate: (data: SchoolData) => void;
  onNext: () => void;
}

export function SchoolSetupStep({ data, onUpdate, onNext }: SchoolSetupStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [signaturePreview, setSignaturePreview] = useState<string | null>(null);

  const handleInputChange = (field: keyof SchoolData, value: string) => {
    onUpdate({
      ...data,
      [field]: value,
    });
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleFileChange = (field: 'logo' | 'signature', file: File | null) => {
    onUpdate({
      ...data,
      [field]: file,
    });

    // Create preview
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        if (field === 'logo') {
          setLogoPreview(result);
        } else {
          setSignaturePreview(result);
        }
      };
      reader.readAsDataURL(file);
    } else {
      if (field === 'logo') {
        setLogoPreview(null);
      } else {
        setSignaturePreview(null);
      }
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!data.name.trim()) {
      newErrors.name = 'Nama sekolah wajib diisi';
    } else if (data.name.trim().length < 3) {
      newErrors.name = 'Nama sekolah minimal 3 karakter';
    }

    if (!data.level) {
      newErrors.level = 'Jenjang sekolah wajib dipilih';
    }

    // Validate file types if uploaded
    if (data.logo) {
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(data.logo.type)) {
        newErrors.logo = 'Logo harus berupa file gambar (JPG, PNG, GIF)';
      } else if (data.logo.size > 2 * 1024 * 1024) { // 2MB
        newErrors.logo = 'Ukuran logo maksimal 2MB';
      }
    }

    if (data.signature) {
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(data.signature.type)) {
        newErrors.signature = 'Tanda tangan harus berupa file gambar (JPG, PNG, GIF)';
      } else if (data.signature.size > 1 * 1024 * 1024) { // 1MB
        newErrors.signature = 'Ukuran tanda tangan maksimal 1MB';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Informasi Sekolah
        </h3>
        <p className="text-gray-600 text-sm">
          Masukkan informasi dasar sekolah Anda untuk memulai
        </p>
      </div>

      <div className="space-y-4">
        {/* School Name */}
        <div className="space-y-2">
          <Label htmlFor="schoolName">
            Nama Sekolah <span className="text-red-500">*</span>
          </Label>
          <Input
            id="schoolName"
            placeholder="Contoh: SD Negeri 1 Jakarta"
            value={data.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={errors.name ? 'border-red-500' : ''}
          />
          {errors.name && (
            <p className="text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        {/* School Level */}
        <div className="space-y-2">
          <Label htmlFor="schoolLevel">
            Jenjang Sekolah <span className="text-red-500">*</span>
          </Label>
          <Select
            value={data.level}
            onValueChange={(value) => handleInputChange('level', value)}
          >
            <SelectTrigger className={errors.level ? 'border-red-500' : ''}>
              <SelectValue placeholder="Pilih jenjang sekolah" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(SCHOOL_LEVELS).map(([key, label]) => (
                <SelectItem key={key} value={key}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.level && (
            <p className="text-sm text-red-600">{errors.level}</p>
          )}
        </div>

        {/* Logo Upload */}
        <div className="space-y-2">
          <Label>Logo Sekolah (Opsional)</Label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
            {logoPreview ? (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <img
                    src={logoPreview}
                    alt="Logo preview"
                    className="w-12 h-12 object-cover rounded"
                  />
                  <div>
                    <p className="text-sm font-medium">{data.logo?.name}</p>
                    <p className="text-xs text-gray-500">
                      {data.logo && (data.logo.size / 1024).toFixed(1)} KB
                    </p>
                  </div>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleFileChange('logo', null)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            ) : (
              <div className="text-center">
                <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600 mb-2">
                  Klik untuk upload logo sekolah
                </p>
                <p className="text-xs text-gray-500">
                  JPG, PNG, GIF maksimal 2MB
                </p>
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0] || null;
                    handleFileChange('logo', file);
                  }}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </div>
            )}
          </div>
          {errors.logo && (
            <p className="text-sm text-red-600">{errors.logo}</p>
          )}
        </div>

        {/* Signature Upload */}
        <div className="space-y-2">
          <Label>Tanda Tangan Kepala Sekolah (Opsional)</Label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
            {signaturePreview ? (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <img
                    src={signaturePreview}
                    alt="Signature preview"
                    className="w-12 h-12 object-cover rounded"
                  />
                  <div>
                    <p className="text-sm font-medium">{data.signature?.name}</p>
                    <p className="text-xs text-gray-500">
                      {data.signature && (data.signature.size / 1024).toFixed(1)} KB
                    </p>
                  </div>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleFileChange('signature', null)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            ) : (
              <div className="text-center">
                <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600 mb-2">
                  Klik untuk upload tanda tangan
                </p>
                <p className="text-xs text-gray-500">
                  JPG, PNG, GIF maksimal 1MB
                </p>
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0] || null;
                    handleFileChange('signature', file);
                  }}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </div>
            )}
          </div>
          {errors.signature && (
            <p className="text-sm text-red-600">{errors.signature}</p>
          )}
        </div>
      </div>

      <Alert>
        <AlertDescription>
          Logo dan tanda tangan dapat diubah nanti melalui pengaturan sekolah.
          Informasi ini akan digunakan untuk laporan dan dokumen resmi.
        </AlertDescription>
      </Alert>

      <div className="flex justify-end">
        <Button onClick={handleNext} className="min-w-32">
          Lanjutkan
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}