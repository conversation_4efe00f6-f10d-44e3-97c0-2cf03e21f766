'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, ArrowRight, Eye, EyeOff, Loader2, Shield } from 'lucide-react';

interface TeacherData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

interface TeacherSetupStepProps {
  data: TeacherData;
  onUpdate: (data: TeacherData) => void;
  onNext: () => void;
  onPrev: () => void;
  isSubmitting: boolean;
}

export function TeacherSetupStep({ 
  data, 
  onUpdate, 
  onNext, 
  onPrev, 
  isSubmitting 
}: TeacherSetupStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleInputChange = (field: keyof TeacherData, value: string) => {
    onUpdate({
      ...data,
      [field]: value,
    });
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Name validation
    if (!data.name.trim()) {
      newErrors.name = 'Nama guru wajib diisi';
    } else if (data.name.trim().length < 2) {
      newErrors.name = 'Nama guru minimal 2 karakter';
    }

    // Email validation
    if (!data.email.trim()) {
      newErrors.email = 'Email wajib diisi';
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.email)) {
        newErrors.email = 'Format email tidak valid';
      }
    }

    // Password validation
    if (!data.password) {
      newErrors.password = 'Password wajib diisi';
    } else if (data.password.length < 8) {
      newErrors.password = 'Password minimal 8 karakter';
    } else {
      // Check password strength
      const hasUpperCase = /[A-Z]/.test(data.password);
      const hasLowerCase = /[a-z]/.test(data.password);
      const hasNumbers = /\d/.test(data.password);
      
      if (!hasUpperCase) {
        newErrors.password = 'Password harus mengandung huruf besar';
      } else if (!hasLowerCase) {
        newErrors.password = 'Password harus mengandung huruf kecil';
      } else if (!hasNumbers) {
        newErrors.password = 'Password harus mengandung angka';
      }
    }

    // Confirm password validation
    if (!data.confirmPassword) {
      newErrors.confirmPassword = 'Konfirmasi password wajib diisi';
    } else if (data.password !== data.confirmPassword) {
      newErrors.confirmPassword = 'Password tidak cocok';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  const getPasswordStrength = () => {
    if (!data.password) return { strength: 0, label: '', color: '' };
    
    let score = 0;
    if (data.password.length >= 8) score++;
    if (/[A-Z]/.test(data.password)) score++;
    if (/[a-z]/.test(data.password)) score++;
    if (/\d/.test(data.password)) score++;
    if (/[^A-Za-z0-9]/.test(data.password)) score++;
    
    if (score <= 2) return { strength: 25, label: 'Lemah', color: 'bg-red-500' };
    if (score === 3) return { strength: 50, label: 'Sedang', color: 'bg-yellow-500' };
    if (score === 4) return { strength: 75, label: 'Kuat', color: 'bg-blue-500' };
    return { strength: 100, label: 'Sangat Kuat', color: 'bg-green-500' };
  };

  const passwordStrength = getPasswordStrength();

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-3">
          <div className="bg-primary-100 p-3 rounded-full">
            <Shield className="w-6 h-6 text-primary-600" />
          </div>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Akun Administrator Pertama
        </h3>
        <p className="text-gray-600 text-sm">
          Buat akun guru yang akan menjadi administrator sekolah
        </p>
      </div>

      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Akun ini akan memiliki akses penuh sebagai superadmin untuk mengelola 
          sekolah, guru, dan siswa. Pastikan informasi yang dimasukkan benar.
        </AlertDescription>
      </Alert>

      <div className="space-y-4">
        {/* Teacher Name */}
        <div className="space-y-2">
          <Label htmlFor="teacherName">
            Nama Lengkap Guru <span className="text-red-500">*</span>
          </Label>
          <Input
            id="teacherName"
            placeholder="Contoh: Budi Santoso, S.Pd"
            value={data.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={errors.name ? 'border-red-500' : ''}
            disabled={isSubmitting}
          />
          {errors.name && (
            <p className="text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        {/* Teacher Email */}
        <div className="space-y-2">
          <Label htmlFor="teacherEmail">
            Email <span className="text-red-500">*</span>
          </Label>
          <Input
            id="teacherEmail"
            type="email"
            placeholder="<EMAIL>"
            value={data.email}
            onChange={(e) => handleInputChange('email', e.target.value.toLowerCase())}
            className={errors.email ? 'border-red-500' : ''}
            disabled={isSubmitting}
          />
          {errors.email && (
            <p className="text-sm text-red-600">{errors.email}</p>
          )}
          <p className="text-xs text-gray-500">
            Email ini akan digunakan untuk login ke sistem
          </p>
        </div>

        {/* Password */}
        <div className="space-y-2">
          <Label htmlFor="teacherPassword">
            Password <span className="text-red-500">*</span>
          </Label>
          <div className="relative">
            <Input
              id="teacherPassword"
              type={showPassword ? 'text' : 'password'}
              placeholder="Masukkan password yang kuat"
              value={data.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              className={errors.password ? 'border-red-500' : ''}
              disabled={isSubmitting}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isSubmitting}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
          
          {/* Password Strength Indicator */}
          {data.password && (
            <div className="space-y-1">
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">Kekuatan Password:</span>
                <span className={`text-xs font-medium ${
                  passwordStrength.strength <= 25 ? 'text-red-600' :
                  passwordStrength.strength <= 50 ? 'text-yellow-600' :
                  passwordStrength.strength <= 75 ? 'text-blue-600' : 'text-green-600'
                }`}>
                  {passwordStrength.label}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all ${passwordStrength.color}`}
                  style={{ width: `${passwordStrength.strength}%` }}
                />
              </div>
            </div>
          )}
          
          {errors.password && (
            <p className="text-sm text-red-600">{errors.password}</p>
          )}
          
          <div className="text-xs text-gray-500 space-y-1">
            <p>Password harus memenuhi kriteria:</p>
            <ul className="list-disc list-inside space-y-0.5 ml-2">
              <li className={data.password.length >= 8 ? 'text-green-600' : ''}>
                Minimal 8 karakter
              </li>
              <li className={/[A-Z]/.test(data.password) ? 'text-green-600' : ''}>
                Mengandung huruf besar
              </li>
              <li className={/[a-z]/.test(data.password) ? 'text-green-600' : ''}>
                Mengandung huruf kecil
              </li>
              <li className={/\d/.test(data.password) ? 'text-green-600' : ''}>
                Mengandung angka
              </li>
            </ul>
          </div>
        </div>

        {/* Confirm Password */}
        <div className="space-y-2">
          <Label htmlFor="confirmPassword">
            Konfirmasi Password <span className="text-red-500">*</span>
          </Label>
          <div className="relative">
            <Input
              id="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              placeholder="Ulangi password"
              value={data.confirmPassword}
              onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
              className={errors.confirmPassword ? 'border-red-500' : ''}
              disabled={isSubmitting}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              disabled={isSubmitting}
            >
              {showConfirmPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
          {errors.confirmPassword && (
            <p className="text-sm text-red-600">{errors.confirmPassword}</p>
          )}
        </div>
      </div>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={onPrev}
          disabled={isSubmitting}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Kembali
        </Button>
        
        <Button
          onClick={handleNext}
          disabled={isSubmitting}
          className="min-w-32"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Menyiapkan...
            </>
          ) : (
            <>
              Selesaikan Setup
              <ArrowRight className="w-4 h-4 ml-2" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
}