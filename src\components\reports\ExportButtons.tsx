'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { Download, FileText, Table, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface ExportButtonsProps {
  classroomId?: number;
  subjectId?: number;
  startDate?: string;
  endDate?: string;
  studentId?: number;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
}

export function ExportButtons({
  classroomId,
  subjectId,
  startDate,
  endDate,
  studentId,
  className,
  variant = 'outline',
  size = 'default',
}: ExportButtonsProps) {
  const [loading, setLoading] = useState<string | null>(null);
  const { toast } = useToast();

  const buildQueryParams = (additionalParams: Record<string, any> = {}) => {
    const params = new URLSearchParams();
    
    if (classroomId) params.set('classroomId', classroomId.toString());
    if (subjectId) params.set('subjectId', subjectId.toString());
    if (startDate) params.set('startDate', startDate);
    if (endDate) params.set('endDate', endDate);
    if (studentId) params.set('studentId', studentId.toString());
    
    Object.entries(additionalParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.set(key, value.toString());
      }
    });
    
    return params.toString();
  };

  const handleExport = async (type: 'pdf' | 'csv', exportType: string) => {
    const loadingKey = `${type}-${exportType}`;
    setLoading(loadingKey);
    
    try {
      const queryParams = buildQueryParams({ type: exportType });
      const url = `/api/reports/${type}?${queryParams}`;
      
      const response = await fetch(url);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Gagal mengunduh file');
      }
      
      // Get filename from response headers
      const contentDisposition = response.headers.get('content-disposition');
      let filename = `export_${Date.now()}.${type === 'pdf' ? 'pdf' : 'csv'}`;
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch) {
          filename = decodeURIComponent(filenameMatch[1].replace(/['"]/g, ''));
        }
      }
      
      // Create blob and download
      const blob = await response.blob();
      const downloadUrl = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(downloadUrl);
      
      toast({
        title: 'Export Berhasil',
        description: `File ${filename} berhasil diunduh`,
      });
      
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: 'Export Gagal',
        description: error instanceof Error ? error.message : 'Terjadi kesalahan saat mengunduh file',
        variant: 'destructive',
      });
    } finally {
      setLoading(null);
    }
  };

  const isLoading = (type: string) => loading === type;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant={variant} 
          size={size}
          className={cn("gap-2", className)}
          disabled={loading !== null}
        >
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Download className="h-4 w-4" />
          )}
          Export
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Format Export</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {/* PDF Exports */}
        <DropdownMenuLabel className="text-xs text-muted-foreground">
          PDF Reports
        </DropdownMenuLabel>
        
        {classroomId && subjectId && startDate && endDate && (
          <DropdownMenuItem
            onClick={() => handleExport('pdf', 'attendance')}
            disabled={isLoading('pdf-attendance')}
            className="gap-2"
          >
            {isLoading('pdf-attendance') ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <FileText className="h-4 w-4" />
            )}
            Laporan Absensi PDF
          </DropdownMenuItem>
        )}
        
        {studentId && (
          <DropdownMenuItem
            onClick={() => handleExport('pdf', 'student')}
            disabled={isLoading('pdf-student')}
            className="gap-2"
          >
            {isLoading('pdf-student') ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <FileText className="h-4 w-4" />
            )}
            Rapor Siswa PDF
          </DropdownMenuItem>
        )}
        
        <DropdownMenuSeparator />
        
        {/* CSV Exports */}
        <DropdownMenuLabel className="text-xs text-muted-foreground">
          CSV Data
        </DropdownMenuLabel>
        
        <DropdownMenuItem
          onClick={() => handleExport('csv', 'attendance')}
          disabled={isLoading('csv-attendance')}
          className="gap-2"
        >
          {isLoading('csv-attendance') ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Table className="h-4 w-4" />
          )}
          Data Absensi CSV
        </DropdownMenuItem>
        
        <DropdownMenuItem
          onClick={() => handleExport('csv', 'students')}
          disabled={isLoading('csv-students')}
          className="gap-2"
        >
          {isLoading('csv-students') ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Table className="h-4 w-4" />
          )}
          Daftar Siswa CSV
        </DropdownMenuItem>
        
        <DropdownMenuItem
          onClick={() => handleExport('csv', 'attendance-summary')}
          disabled={isLoading('csv-attendance-summary')}
          className="gap-2"
        >
          {isLoading('csv-attendance-summary') ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Table className="h-4 w-4" />
          )}
          Ringkasan Absensi CSV
        </DropdownMenuItem>
        
        <DropdownMenuItem
          onClick={() => handleExport('csv', 'classrooms')}
          disabled={isLoading('csv-classrooms')}
          className="gap-2"
        >
          {isLoading('csv-classrooms') ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Table className="h-4 w-4" />
          )}
          Daftar Kelas CSV
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Simplified export button for specific use cases
interface SimpleExportButtonProps {
  type: 'pdf' | 'csv';
  exportType: string;
  label: string;
  icon?: React.ReactNode;
  classroomId?: number;
  subjectId?: number;
  startDate?: string;
  endDate?: string;
  studentId?: number;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
}

export function SimpleExportButton({
  type,
  exportType,
  label,
  icon,
  classroomId,
  subjectId,
  startDate,
  endDate,
  studentId,
  className,
  variant = 'outline',
  size = 'default',
}: SimpleExportButtonProps) {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleExport = async () => {
    setLoading(true);
    
    try {
      const params = new URLSearchParams();
      params.set('type', exportType);
      
      if (classroomId) params.set('classroomId', classroomId.toString());
      if (subjectId) params.set('subjectId', subjectId.toString());
      if (startDate) params.set('startDate', startDate);
      if (endDate) params.set('endDate', endDate);
      if (studentId) params.set('studentId', studentId.toString());
      
      const url = `/api/reports/${type}?${params.toString()}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Gagal mengunduh file');
      }
      
      // Get filename from response headers
      const contentDisposition = response.headers.get('content-disposition');
      let filename = `export_${Date.now()}.${type}`;
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch) {
          filename = decodeURIComponent(filenameMatch[1].replace(/['"]/g, ''));
        }
      }
      
      // Create blob and download
      const blob = await response.blob();
      const downloadUrl = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(downloadUrl);
      
      toast({
        title: 'Export Berhasil',
        description: `File ${filename} berhasil diunduh`,
      });
      
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: 'Export Gagal',
        description: error instanceof Error ? error.message : 'Terjadi kesalahan saat mengunduh file',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleExport}
      disabled={loading}
      className={cn("gap-2", className)}
    >
      {loading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        icon || <Download className="h-4 w-4" />
      )}
      {label}
    </Button>
  );
}