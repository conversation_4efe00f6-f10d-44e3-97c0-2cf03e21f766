'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Upload, 
  Download, 
  FileText, 
  CheckCircle, 
  AlertTriangle,
  X,
  Loader2
} from 'lucide-react';

interface BulkImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface ImportResult {
  success: number;
  failed: number;
  errors: Array<{
    row: number;
    error: string;
    data: any;
  }>;
}

export function BulkImportModal({ isOpen, onClose, onSuccess }: BulkImportModalProps) {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [step, setStep] = useState<'select' | 'uploading' | 'result'>('select');

  if (!isOpen) return null;

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile && selectedFile.type === 'text/csv') {
      setFile(selectedFile);
    } else {
      alert('Silakan pilih file CSV yang valid');
    }
  };

  const downloadTemplate = () => {
    // Create CSV template
    const csvContent = [
      'nama,nis,email,telepon,tanggal_lahir,jenis_kelamin,alamat,nama_orangtua,telepon_orangtua,email_orangtua,kelas_id',
      'Ahmad Rizki,2024001,<EMAIL>,081234567890,2012-05-15,male,Jl. Merdeka No. 1,Budi Ahmad,081234567891,<EMAIL>,1',
      'Siti Nurhaliza,2024002,<EMAIL>,081234567892,2012-03-22,female,Jl. Sudirman No. 2,Ani Siti,081234567893,<EMAIL>,1'
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'template_import_siswa.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const handleUpload = async () => {
    if (!file) return;

    setIsUploading(true);
    setStep('uploading');
    setUploadProgress(0);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', file);

      // In real implementation, call API to process CSV
      const response = await fetch('/api/students/bulk-import', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        throw new Error('Upload gagal');
      }

      const result = await response.json();
      
      // Mock result for demonstration
      const mockResult: ImportResult = {
        success: 25,
        failed: 3,
        errors: [
          { row: 5, error: 'Email sudah terdaftar', data: { nama: 'John Doe', email: '<EMAIL>' } },
          { row: 12, error: 'Format tanggal lahir tidak valid', data: { nama: 'Jane Smith', tanggal_lahir: '15/05/2012' } },
          { row: 18, error: 'NIS sudah digunakan', data: { nama: 'Bob Wilson', nis: '2024001' } },
        ]
      };

      setTimeout(() => {
        setImportResult(mockResult);
        setStep('result');
      }, 1000);

    } catch (error) {
      console.error('Upload error:', error);
      alert('Terjadi kesalahan saat upload file');
      setStep('select');
    } finally {
      setIsUploading(false);
    }
  };

  const handleClose = () => {
    setFile(null);
    setImportResult(null);
    setStep('select');
    setUploadProgress(0);
    onClose();
  };

  const handleSuccess = () => {
    handleClose();
    onSuccess();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Import Siswa dari CSV</CardTitle>
                <CardDescription>
                  Upload file CSV untuk menambahkan banyak siswa sekaligus
                </CardDescription>
              </div>
              <Button variant="ghost" size="sm" onClick={handleClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {step === 'select' && (
              <>
                {/* Template Download */}
                <Alert>
                  <Download className="h-4 w-4" />
                  <AlertDescription>
                    <div className="flex items-center justify-between">
                      <span>Download template CSV terlebih dahulu untuk format yang benar</span>
                      <Button variant="outline" size="sm" onClick={downloadTemplate}>
                        <Download className="h-3 w-3 mr-1" />
                        Download Template
                      </Button>
                    </div>
                  </AlertDescription>
                </Alert>

                {/* File Upload */}
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  {file ? (
                    <div className="space-y-4">
                      <FileText className="h-12 w-12 text-green-600 mx-auto" />
                      <div>
                        <p className="font-medium text-gray-900">{file.name}</p>
                        <p className="text-sm text-gray-600">
                          {(file.size / 1024).toFixed(1)} KB
                        </p>
                      </div>
                      <div className="flex justify-center space-x-2">
                        <Button onClick={handleUpload} disabled={isUploading}>
                          <Upload className="h-4 w-4 mr-2" />
                          Upload & Proses
                        </Button>
                        <Button variant="outline" onClick={() => setFile(null)}>
                          Ganti File
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Upload className="h-12 w-12 text-gray-400 mx-auto" />
                      <div>
                        <p className="text-lg font-medium text-gray-900 mb-2">
                          Pilih file CSV
                        </p>
                        <p className="text-gray-600 mb-4">
                          Drag & drop file CSV atau klik untuk memilih
                        </p>
                        <input
                          type="file"
                          accept=".csv"
                          onChange={handleFileSelect}
                          className="hidden"
                          id="csv-upload"
                        />
                        <label htmlFor="csv-upload">
                          <Button asChild>
                            <span>Pilih File CSV</span>
                          </Button>
                        </label>
                      </div>
                    </div>
                  )}
                </div>

                {/* Instructions */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Petunjuk Import:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• File harus dalam format CSV dengan encoding UTF-8</li>
                    <li>• Gunakan template yang disediakan untuk format yang benar</li>
                    <li>• Pastikan email dan NIS unik (tidak duplikat)</li>
                    <li>• Format tanggal: YYYY-MM-DD (contoh: 2012-05-15)</li>
                    <li>• Jenis kelamin: male atau female</li>
                    <li>• Kelas ID harus sesuai dengan kelas yang ada</li>
                  </ul>
                </div>
              </>
            )}

            {step === 'uploading' && (
              <div className="text-center space-y-4">
                <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto" />
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Memproses File CSV...
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Sedang memvalidasi dan mengimpor data siswa
                  </p>
                  <Progress value={uploadProgress} className="w-full max-w-md mx-auto" />
                  <p className="text-sm text-gray-500 mt-2">
                    {uploadProgress}% selesai
                  </p>
                </div>
              </div>
            )}

            {step === 'result' && importResult && (
              <div className="space-y-6">
                {/* Summary */}
                <div className="grid grid-cols-2 gap-4">
                  <Card className="border-green-200 bg-green-50">
                    <CardContent className="pt-4">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <div>
                          <div className="text-2xl font-bold text-green-700">
                            {importResult.success}
                          </div>
                          <div className="text-sm text-green-600">Berhasil diimpor</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-red-200 bg-red-50">
                    <CardContent className="pt-4">
                      <div className="flex items-center space-x-2">
                        <AlertTriangle className="h-5 w-5 text-red-600" />
                        <div>
                          <div className="text-2xl font-bold text-red-700">
                            {importResult.failed}
                          </div>
                          <div className="text-sm text-red-600">Gagal diimpor</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Errors */}
                {importResult.errors.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">
                      Data yang Gagal Diimpor:
                    </h4>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {importResult.errors.map((error, index) => (
                        <Alert key={index} variant="destructive">
                          <AlertTriangle className="h-4 w-4" />
                          <AlertDescription>
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium">Baris {error.row}: {error.error}</p>
                                <p className="text-sm opacity-90">
                                  Data: {JSON.stringify(error.data)}
                                </p>
                              </div>
                            </div>
                          </AlertDescription>
                        </Alert>
                      ))}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={handleClose}>
                    Tutup
                  </Button>
                  <Button onClick={handleSuccess}>
                    Selesai
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}