'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Save, Loader2, User, Users } from 'lucide-react';
import Link from 'next/link';

interface StudentFormData {
  name: string;
  studentId: string;
  email: string;
  phone?: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | '';
  address?: string;
  parentName?: string;
  parentPhone?: string;
  parentEmail?: string;
  classroomId?: number;
  enrollmentDate: string;
}

export function CreateStudentForm() {
  const [formData, setFormData] = useState<StudentFormData>({
    name: '',
    studentId: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: '',
    address: '',
    parentName: '',
    parentPhone: '',
    parentEmail: '',
    enrollmentDate: new Date().toISOString().split('T')[0],
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  // Mock classrooms data - in real implementation, fetch from API
  const mockClassrooms = [
    { id: 1, name: 'Kelas 5A', grade: '5' },
    { id: 2, name: 'Kelas 5B', grade: '5' },
    { id: 3, name: 'Kelas 6A', grade: '6' },
    { id: 4, name: 'Kelas 4A', grade: '4' },
  ];

  const handleInputChange = (field: keyof StudentFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const generateStudentId = () => {
    // Generate student ID based on current year and random number
    const year = new Date().getFullYear();
    const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const generatedId = `${year}${randomNum}`;
    
    handleInputChange('studentId', generatedId);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields validation
    if (!formData.name.trim()) {
      newErrors.name = 'Nama siswa wajib diisi';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Nama siswa minimal 2 karakter';
    }

    if (!formData.studentId.trim()) {
      newErrors.studentId = 'NIS wajib diisi';
    } else if (formData.studentId.trim().length < 6) {
      newErrors.studentId = 'NIS minimal 6 digit';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email wajib diisi';
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        newErrors.email = 'Format email tidak valid';
      }
    }

    if (!formData.dateOfBirth) {
      newErrors.dateOfBirth = 'Tanggal lahir wajib diisi';
    } else {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age < 5 || age > 18) {
        newErrors.dateOfBirth = 'Usia siswa harus antara 5-18 tahun';
      }
    }

    if (!formData.gender) {
      newErrors.gender = 'Jenis kelamin wajib dipilih';
    }

    if (!formData.enrollmentDate) {
      newErrors.enrollmentDate = 'Tanggal masuk wajib diisi';
    }

    // Optional fields validation
    if (formData.phone && formData.phone.length < 10) {
      newErrors.phone = 'Nomor telepon minimal 10 digit';
    }

    if (formData.parentPhone && formData.parentPhone.length < 10) {
      newErrors.parentPhone = 'Nomor telepon orang tua minimal 10 digit';
    }

    if (formData.parentEmail && formData.parentEmail.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.parentEmail)) {
        newErrors.parentEmail = 'Format email orang tua tidak valid';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // In real implementation, call API to create student
      const response = await fetch('/api/students', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        throw new Error('Gagal menambahkan siswa');
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Redirect to student list with success message
        router.push('/guru/siswa?success=created');
      }
    } catch (error) {
      console.error('Create student error:', error);
      setErrors({ submit: 'Terjadi kesalahan saat menambahkan siswa' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Back Button */}
      <div className="flex items-center space-x-4">
        <Button variant="outline" asChild>
          <Link href="/guru/siswa">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali ke Daftar Siswa
          </Link>
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {errors.submit && (
          <Alert variant="destructive">
            <AlertDescription>{errors.submit}</AlertDescription>
          </Alert>
        )}

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Informasi Dasar Siswa</span>
            </CardTitle>
            <CardDescription>
              Masukkan informasi dasar siswa yang akan didaftarkan
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Full Name */}
              <div className="space-y-2">
                <Label htmlFor="name">
                  Nama Lengkap <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  placeholder="Nama lengkap siswa"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={errors.name ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.name && (
                  <p className="text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              {/* Student ID */}
              <div className="space-y-2">
                <Label htmlFor="studentId">
                  NIS (Nomor Induk Siswa) <span className="text-red-500">*</span>
                </Label>
                <div className="flex space-x-2">
                  <Input
                    id="studentId"
                    placeholder="Contoh: 2024001"
                    value={formData.studentId}
                    onChange={(e) => handleInputChange('studentId', e.target.value)}
                    className={errors.studentId ? 'border-red-500' : ''}
                    disabled={isSubmitting}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={generateStudentId}
                    disabled={isSubmitting}
                  >
                    Generate
                  </Button>
                </div>
                {errors.studentId && (
                  <p className="text-sm text-red-600">{errors.studentId}</p>
                )}
              </div>

              {/* Email */}
              <div className="space-y-2">
                <Label htmlFor="email">
                  Email <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value.toLowerCase())}
                  className={errors.email ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.email && (
                  <p className="text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              {/* Phone */}
              <div className="space-y-2">
                <Label htmlFor="phone">Nomor Telepon</Label>
                <Input
                  id="phone"
                  placeholder="081234567890"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className={errors.phone ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.phone && (
                  <p className="text-sm text-red-600">{errors.phone}</p>
                )}
              </div>

              {/* Date of Birth */}
              <div className="space-y-2">
                <Label htmlFor="dateOfBirth">
                  Tanggal Lahir <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                  className={errors.dateOfBirth ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.dateOfBirth && (
                  <p className="text-sm text-red-600">{errors.dateOfBirth}</p>
                )}
              </div>

              {/* Gender */}
              <div className="space-y-2">
                <Label htmlFor="gender">
                  Jenis Kelamin <span className="text-red-500">*</span>
                </Label>
                <Select
                  value={formData.gender}
                  onValueChange={(value) => handleInputChange('gender', value)}
                  disabled={isSubmitting}
                >
                  <SelectTrigger className={errors.gender ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Pilih jenis kelamin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Laki-laki</SelectItem>
                    <SelectItem value="female">Perempuan</SelectItem>
                  </SelectContent>
                </Select>
                {errors.gender && (
                  <p className="text-sm text-red-600">{errors.gender}</p>
                )}
              </div>
            </div>

            {/* Address */}
            <div className="space-y-2">
              <Label htmlFor="address">Alamat</Label>
              <Input
                id="address"
                placeholder="Alamat lengkap siswa"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                disabled={isSubmitting}
              />
            </div>
          </CardContent>
        </Card>

        {/* Parent Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Informasi Orang Tua/Wali</span>
            </CardTitle>
            <CardDescription>
              Informasi kontak orang tua atau wali siswa (opsional)
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Parent Name */}
              <div className="space-y-2">
                <Label htmlFor="parentName">Nama Orang Tua/Wali</Label>
                <Input
                  id="parentName"
                  placeholder="Nama lengkap orang tua/wali"
                  value={formData.parentName}
                  onChange={(e) => handleInputChange('parentName', e.target.value)}
                  disabled={isSubmitting}
                />
              </div>

              {/* Parent Phone */}
              <div className="space-y-2">
                <Label htmlFor="parentPhone">Nomor Telepon Orang Tua</Label>
                <Input
                  id="parentPhone"
                  placeholder="081234567890"
                  value={formData.parentPhone}
                  onChange={(e) => handleInputChange('parentPhone', e.target.value)}
                  className={errors.parentPhone ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.parentPhone && (
                  <p className="text-sm text-red-600">{errors.parentPhone}</p>
                )}
              </div>

              {/* Parent Email */}
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="parentEmail">Email Orang Tua</Label>
                <Input
                  id="parentEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.parentEmail}
                  onChange={(e) => handleInputChange('parentEmail', e.target.value.toLowerCase())}
                  className={errors.parentEmail ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.parentEmail && (
                  <p className="text-sm text-red-600">{errors.parentEmail}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enrollment Information */}
        <Card>
          <CardHeader>
            <CardTitle>Informasi Pendaftaran</CardTitle>
            <CardDescription>
              Pengaturan kelas dan tanggal masuk siswa
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Classroom */}
              <div className="space-y-2">
                <Label htmlFor="classroom">Kelas (Opsional)</Label>
                <Select
                  value={formData.classroomId?.toString() || ''}
                  onValueChange={(value) => handleInputChange('classroomId', parseInt(value))}
                  disabled={isSubmitting}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih kelas" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Belum ditentukan</SelectItem>
                    {mockClassrooms.map((classroom) => (
                      <SelectItem key={classroom.id} value={classroom.id.toString()}>
                        {classroom.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-sm text-gray-500">
                  Kelas dapat diatur nanti setelah siswa terdaftar
                </p>
              </div>

              {/* Enrollment Date */}
              <div className="space-y-2">
                <Label htmlFor="enrollmentDate">
                  Tanggal Masuk <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="enrollmentDate"
                  type="date"
                  value={formData.enrollmentDate}
                  onChange={(e) => handleInputChange('enrollmentDate', e.target.value)}
                  className={errors.enrollmentDate ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.enrollmentDate && (
                  <p className="text-sm text-red-600">{errors.enrollmentDate}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            Batal
          </Button>
          
          <Button
            type="submit"
            disabled={isSubmitting}
            className="min-w-32"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Menyimpan...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Tambah Siswa
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}