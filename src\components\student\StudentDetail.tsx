'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  ArrowLeft, 
  Edit, 
  User, 
  Mail,
  Phone,
  Calendar,
  MapPin,
  Users,
  BookOpen,
  Trophy,
  TrendingUp,
  Award,
  Star,
  Clock
} from 'lucide-react';
import Link from 'next/link';

interface StudentDetailProps {
  studentId: number;
}

interface AttendanceRecord {
  date: string;
  status: 'H' | 'I' | 'S' | 'A';
  subject: string;
  notes?: string;
}

interface Grade {
  subject: string;
  assignment: string;
  score: number;
  maxScore: number;
  date: string;
  type: 'quiz' | 'assignment' | 'exam';
}

interface Badge {
  id: number;
  name: string;
  description: string;
  earnedAt: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

export function StudentDetail({ studentId }: StudentDetailProps) {
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data - in real implementation, fetch from API based on studentId
  const mockStudent = {
    id: studentId,
    name: 'Ahmad Rizki Pratama',
    studentId: '2024001',
    email: '<EMAIL>',
    phone: '081234567890',
    dateOfBirth: '2012-05-15',
    gender: 'male',
    address: 'Jl. Merdeka No. 123, Jakarta Pusat',
    enrollmentDate: '2024-07-20',
    isActive: true,
    parentName: 'Budi Pratama',
    parentPhone: '081234567891',
    parentEmail: '<EMAIL>',
    classrooms: [
      { id: 1, name: 'Kelas 5A', grade: '5' }
    ],
    totalXP: 2450,
    level: 8,
    attendanceRate: 95,
    currentStreak: 15,
    totalBadges: 5,
  };

  const mockAttendance: AttendanceRecord[] = [
    { date: '2024-12-16', status: 'H', subject: 'Matematika' },
    { date: '2024-12-16', status: 'H', subject: 'IPA' },
    { date: '2024-12-15', status: 'H', subject: 'Bahasa Indonesia' },
    { date: '2024-12-15', status: 'I', subject: 'IPS', notes: 'Sakit demam' },
    { date: '2024-12-14', status: 'H', subject: 'Matematika' },
  ];

  const mockGrades: Grade[] = [
    { subject: 'Matematika', assignment: 'Ulangan Harian Bab 5', score: 88, maxScore: 100, date: '2024-12-10', type: 'exam' },
    { subject: 'IPA', assignment: 'Laporan Percobaan', score: 92, maxScore: 100, date: '2024-12-08', type: 'assignment' },
    { subject: 'Bahasa Indonesia', assignment: 'Kuis Tata Bahasa', score: 85, maxScore: 100, date: '2024-12-05', type: 'quiz' },
    { subject: 'IPS', assignment: 'Tugas Peta Indonesia', score: 90, maxScore: 100, date: '2024-12-03', type: 'assignment' },
  ];

  const mockBadges: Badge[] = [
    { id: 1, name: 'Rajin Hadir', description: 'Hadir 30 hari berturut-turut', earnedAt: '2024-12-10', rarity: 'rare' },
    { id: 2, name: 'Pengumpul Tugas', description: 'Mengumpulkan 10 tugas tepat waktu', earnedAt: '2024-12-05', rarity: 'common' },
    { id: 3, name: 'Siswa Teladan', description: 'Mendapat nilai A dalam 5 mata pelajaran', earnedAt: '2024-11-28', rarity: 'epic' },
  ];

  const getStatusColor = (status: AttendanceRecord['status']) => {
    switch (status) {
      case 'H': return 'bg-green-100 text-green-800';
      case 'I': return 'bg-yellow-100 text-yellow-800';
      case 'S': return 'bg-blue-100 text-blue-800';
      case 'A': return 'bg-red-100 text-red-800';
    }
  };

  const getStatusText = (status: AttendanceRecord['status']) => {
    switch (status) {
      case 'H': return 'Hadir';
      case 'I': return 'Izin';
      case 'S': return 'Sakit';
      case 'A': return 'Alpa';
    }
  };

  const getGradeColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 80) return 'text-blue-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getBadgeRarityColor = (rarity: Badge['rarity']) => {
    switch (rarity) {
      case 'common': return 'bg-gray-100 text-gray-800';
      case 'rare': return 'bg-blue-100 text-blue-800';
      case 'epic': return 'bg-purple-100 text-purple-800';
      case 'legendary': return 'bg-yellow-100 text-yellow-800';
    }
  };

  const age = new Date().getFullYear() - new Date(mockStudent.dateOfBirth).getFullYear();
  const xpToNextLevel = 500; // Mock XP needed for next level
  const progressToNextLevel = ((mockStudent.totalXP % 1000) / 1000) * 100;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center space-x-4">
          <Button variant="outline" asChild>
            <Link href="/guru/siswa">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Kembali
            </Link>
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{mockStudent.name}</h1>
            <p className="text-gray-600">
              NIS: {mockStudent.studentId} • {mockStudent.classrooms[0]?.name}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant={mockStudent.isActive ? 'default' : 'secondary'}>
            {mockStudent.isActive ? 'Aktif' : 'Nonaktif'}
          </Badge>
          
          <Button variant="outline" asChild>
            <Link href={`/guru/siswa/${studentId}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Profile
            </Link>
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Ringkasan</TabsTrigger>
          <TabsTrigger value="attendance">Absensi</TabsTrigger>
          <TabsTrigger value="grades">Nilai</TabsTrigger>
          <TabsTrigger value="achievements">Pencapaian</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-5 w-5" />
                  <span>Informasi Pribadi</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Nama Lengkap</label>
                  <p className="text-gray-900">{mockStudent.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Jenis Kelamin</label>
                  <p className="text-gray-900">{mockStudent.gender === 'male' ? 'Laki-laki' : 'Perempuan'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Tanggal Lahir</label>
                  <p className="text-gray-900">
                    {new Date(mockStudent.dateOfBirth).toLocaleDateString('id-ID')} ({age} tahun)
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Alamat</label>
                  <p className="text-gray-900">{mockStudent.address}</p>
                </div>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Informasi Kontak</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <div>
                    <label className="text-sm font-medium text-gray-500">Email</label>
                    <p className="text-gray-900">{mockStudent.email}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <div>
                    <label className="text-sm font-medium text-gray-500">Telepon</label>
                    <p className="text-gray-900">{mockStudent.phone}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-gray-400" />
                  <div>
                    <label className="text-sm font-medium text-gray-500">Orang Tua</label>
                    <p className="text-gray-900">{mockStudent.parentName}</p>
                    <p className="text-sm text-gray-600">{mockStudent.parentPhone}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Academic Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Statistik Akademik</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Kehadiran</span>
                  </div>
                  <span className="font-bold text-green-600">{mockStudent.attendanceRate}%</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-blue-500" />
                    <span className="text-sm">Streak Hadir</span>
                  </div>
                  <span className="font-bold text-blue-600">{mockStudent.currentStreak} hari</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <BookOpen className="h-4 w-4 text-purple-500" />
                    <span className="text-sm">Rata-rata Nilai</span>
                  </div>
                  <span className="font-bold text-purple-600">
                    {(mockGrades.reduce((acc, g) => acc + (g.score / g.maxScore), 0) / mockGrades.length * 100).toFixed(1)}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Award className="h-4 w-4 text-orange-500" />
                    <span className="text-sm">Total Badge</span>
                  </div>
                  <span className="font-bold text-orange-600">{mockStudent.totalBadges}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Gamification Progress */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Star className="h-5 w-5" />
                <span>Progress Gamifikasi</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-1">
                    Level {mockStudent.level}
                  </div>
                  <div className="text-sm text-gray-600 mb-3">
                    {mockStudent.totalXP.toLocaleString()} XP
                  </div>
                  <Progress value={progressToNextLevel} className="h-2" />
                  <div className="text-xs text-gray-500 mt-1">
                    {xpToNextLevel} XP ke level berikutnya
                  </div>
                </div>
                
                <div className="text-center">
                  <Trophy className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                  <div className="text-lg font-bold text-gray-900">Peringkat #3</div>
                  <div className="text-sm text-gray-600">di kelasnya</div>
                </div>
                
                <div className="text-center">
                  <TrendingUp className="h-8 w-8 text-green-500 mx-auto mb-2" />
                  <div className="text-lg font-bold text-gray-900">+320 XP</div>
                  <div className="text-sm text-gray-600">minggu ini</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Attendance Tab */}
        <TabsContent value="attendance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Riwayat Absensi Terbaru</CardTitle>
              <CardDescription>
                Kehadiran siswa dalam 2 minggu terakhir
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {mockAttendance.map((record, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Badge className={getStatusColor(record.status)}>
                        {getStatusText(record.status)}
                      </Badge>
                      <div>
                        <p className="font-medium">{record.subject}</p>
                        <p className="text-sm text-gray-600">
                          {new Date(record.date).toLocaleDateString('id-ID', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}
                        </p>
                      </div>
                    </div>
                    {record.notes && (
                      <div className="text-sm text-gray-600 italic">
                        {record.notes}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Grades Tab */}
        <TabsContent value="grades" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Nilai Terbaru</CardTitle>
              <CardDescription>
                Hasil penilaian tugas dan ujian
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {mockGrades.map((grade, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium">{grade.assignment}</h4>
                        <Badge variant="outline" className="text-xs">
                          {grade.type === 'exam' ? 'Ujian' : grade.type === 'assignment' ? 'Tugas' : 'Kuis'}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600">{grade.subject}</p>
                      <p className="text-xs text-gray-500">
                        {new Date(grade.date).toLocaleDateString('id-ID')}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className={`text-lg font-bold ${getGradeColor(grade.score, grade.maxScore)}`}>
                        {grade.score}/{grade.maxScore}
                      </div>
                      <div className="text-sm text-gray-600">
                        {((grade.score / grade.maxScore) * 100).toFixed(0)}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Achievements Tab */}
        <TabsContent value="achievements" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Badge yang Diraih</CardTitle>
              <CardDescription>
                Pencapaian dan penghargaan siswa
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {mockBadges.map((badge) => (
                  <div key={badge.id} className="flex items-center space-x-3 p-4 border rounded-lg">
                    <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                      <Award className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium">{badge.name}</h4>
                        <Badge className={getBadgeRarityColor(badge.rarity)}>
                          {badge.rarity}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-1">{badge.description}</p>
                      <p className="text-xs text-gray-500">
                        Diraih: {new Date(badge.earnedAt).toLocaleDateString('id-ID')}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}