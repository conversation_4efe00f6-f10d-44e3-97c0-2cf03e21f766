'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Search, 
  Users, 
  Download,
  Upload,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  UserCheck,
  UserX,
  Mail,
  Phone,
  Calendar
} from 'lucide-react';
import Link from 'next/link';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { BulkImportModal } from './BulkImportModal';

interface Student {
  id: number;
  name: string;
  studentId: string;
  email: string;
  phone?: string;
  dateOfBirth: string;
  enrollmentDate: string;
  isActive: boolean;
  classrooms: Array<{
    id: number;
    name: string;
    grade: string;
  }>;
  totalXP: number;
  level: number;
  attendanceRate: number;
}

export function StudentList() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [showImportModal, setShowImportModal] = useState(false);

  // Mock data - in real implementation, fetch from API
  const mockStudents: Student[] = [
    {
      id: 1,
      name: 'Ahmad Rizki Pratama',
      studentId: '2024001',
      email: '<EMAIL>',
      phone: '081234567890',
      dateOfBirth: '2012-05-15',
      enrollmentDate: '2024-07-20',
      isActive: true,
      classrooms: [
        { id: 1, name: 'Kelas 5A', grade: '5' }
      ],
      totalXP: 2450,
      level: 8,
      attendanceRate: 95,
    },
    {
      id: 2,
      name: 'Siti Nurhaliza',
      studentId: '2024002',
      email: '<EMAIL>',
      phone: '081234567891',
      dateOfBirth: '2012-03-22',
      enrollmentDate: '2024-07-20',
      isActive: true,
      classrooms: [
        { id: 2, name: 'Kelas 5B', grade: '5' }
      ],
      totalXP: 2380,
      level: 7,
      attendanceRate: 92,
    },
    {
      id: 3,
      name: 'Budi Santoso',
      studentId: '2024003',
      email: '<EMAIL>',
      dateOfBirth: '2012-08-10',
      enrollmentDate: '2024-07-21',
      isActive: true,
      classrooms: [
        { id: 1, name: 'Kelas 5A', grade: '5' }
      ],
      totalXP: 2250,
      level: 7,
      attendanceRate: 88,
    },
    {
      id: 4,
      name: 'Dewi Lestari',
      studentId: '2024004',
      email: '<EMAIL>',
      phone: '081234567893',
      dateOfBirth: '2012-12-05',
      enrollmentDate: '2024-07-21',
      isActive: false,
      classrooms: [
        { id: 3, name: 'Kelas 6A', grade: '6' }
      ],
      totalXP: 1800,
      level: 5,
      attendanceRate: 75,
    },
  ];

  const mockClassrooms = [
    { id: 1, name: 'Kelas 5A', grade: '5' },
    { id: 2, name: 'Kelas 5B', grade: '5' },
    { id: 3, name: 'Kelas 6A', grade: '6' },
  ];

  const filteredStudents = mockStudents.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesClass = selectedClass === 'all' || 
                        student.classrooms.some(c => c.id.toString() === selectedClass);
    
    const matchesStatus = selectedStatus === 'all' || 
                         (selectedStatus === 'active' && student.isActive) ||
                         (selectedStatus === 'inactive' && !student.isActive);
    
    return matchesSearch && matchesClass && matchesStatus;
  });

  const handleToggleStatus = (studentId: number) => {
    // In real implementation, call API to toggle student status
    console.log('Toggle student status:', studentId);
  };

  const handleDeleteStudent = (studentId: number) => {
    // In real implementation, show confirmation dialog and call API
    console.log('Delete student:', studentId);
  };

  const handleBulkImport = () => {
    setShowImportModal(true);
  };

  const handleExportData = () => {
    // In real implementation, generate and download CSV/Excel file
    console.log('Export student data');
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Daftar Siswa</h2>
          <p className="text-gray-600">
            Kelola profil dan enrollment {mockStudents.length} siswa
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleBulkImport}>
            <Upload className="h-4 w-4 mr-2" />
            Import CSV
          </Button>
          
          <Button variant="outline" onClick={handleExportData}>
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
          
          <Button asChild>
            <Link href="/guru/siswa/tambah">
              <Plus className="h-4 w-4 mr-2" />
              Tambah Siswa
            </Link>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cari nama, NIS, atau email siswa..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="sm:w-48">
              <select
                value={selectedClass}
                onChange={(e) => setSelectedClass(e.target.value)}
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">Semua Kelas</option>
                {mockClassrooms.map(classroom => (
                  <option key={classroom.id} value={classroom.id.toString()}>
                    {classroom.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="sm:w-32">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">Semua Status</option>
                <option value="active">Aktif</option>
                <option value="inactive">Nonaktif</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Student Grid */}
      {filteredStudents.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm || selectedClass !== 'all' || selectedStatus !== 'all' 
                ? 'Tidak ada siswa yang ditemukan' 
                : 'Belum ada siswa'
              }
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedClass !== 'all' || selectedStatus !== 'all'
                ? 'Coba ubah filter pencarian Anda'
                : 'Mulai dengan menambahkan siswa pertama'
              }
            </p>
            {!searchTerm && selectedClass === 'all' && selectedStatus === 'all' && (
              <Button asChild>
                <Link href="/guru/siswa/tambah">
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Siswa Pertama
                </Link>
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredStudents.map((student) => (
            <Card key={student.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{student.name}</CardTitle>
                    <CardDescription>NIS: {student.studentId}</CardDescription>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge variant={student.isActive ? 'default' : 'secondary'}>
                      {student.isActive ? 'Aktif' : 'Nonaktif'}
                    </Badge>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/guru/siswa/${student.id}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            Lihat Profile
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/guru/siswa/${student.id}/edit`}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Siswa
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleToggleStatus(student.id)}
                        >
                          {student.isActive ? (
                            <>
                              <UserX className="h-4 w-4 mr-2" />
                              Nonaktifkan
                            </>
                          ) : (
                            <>
                              <UserCheck className="h-4 w-4 mr-2" />
                              Aktifkan
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeleteStudent(student.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Hapus Siswa
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  {/* Contact Info */}
                  <div className="space-y-1">
                    <div className="flex items-center text-sm text-gray-600">
                      <Mail className="h-3 w-3 mr-2" />
                      <span className="truncate">{student.email}</span>
                    </div>
                    {student.phone && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Phone className="h-3 w-3 mr-2" />
                        <span>{student.phone}</span>
                      </div>
                    )}
                  </div>
                  
                  {/* Class Info */}
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-1">Kelas:</p>
                    <div className="flex flex-wrap gap-1">
                      {student.classrooms.map((classroom) => (
                        <Badge key={classroom.id} variant="outline" className="text-xs">
                          {classroom.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-2 text-center">
                    <div className="p-2 bg-blue-50 rounded">
                      <div className="text-sm font-bold text-blue-600">
                        Level {student.level}
                      </div>
                      <div className="text-xs text-blue-500">
                        {student.totalXP.toLocaleString()} XP
                      </div>
                    </div>
                    
                    <div className="p-2 bg-green-50 rounded">
                      <div className="text-sm font-bold text-green-600">
                        {student.attendanceRate}%
                      </div>
                      <div className="text-xs text-green-500">Kehadiran</div>
                    </div>
                    
                    <div className="p-2 bg-purple-50 rounded">
                      <div className="text-sm font-bold text-purple-600">
                        {new Date().getFullYear() - new Date(student.dateOfBirth).getFullYear()}
                      </div>
                      <div className="text-xs text-purple-500">Tahun</div>
                    </div>
                  </div>
                  
                  {/* Enrollment Date */}
                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="h-3 w-3 mr-1" />
                    Terdaftar: {new Date(student.enrollmentDate).toLocaleDateString('id-ID')}
                  </div>
                </div>
                
                <div className="mt-4 pt-4 border-t">
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline" className="flex-1" asChild>
                      <Link href={`/guru/siswa/${student.id}`}>
                        <Eye className="h-3 w-3 mr-1" />
                        Profile
                      </Link>
                    </Button>
                    <Button size="sm" className="flex-1" asChild>
                      <Link href={`/guru/absensi?siswa=${student.id}`}>
                        <Calendar className="h-3 w-3 mr-1" />
                        Absensi
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Summary Stats */}
      {filteredStudents.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {filteredStudents.length}
                </div>
                <div className="text-sm text-gray-600">Total Siswa</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {filteredStudents.filter(s => s.isActive).length}
                </div>
                <div className="text-sm text-gray-600">Siswa Aktif</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {Math.round(filteredStudents.reduce((acc, s) => acc + s.attendanceRate, 0) / filteredStudents.length)}%
                </div>
                <div className="text-sm text-gray-600">Rata-rata Kehadiran</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">
                  {Math.round(filteredStudents.reduce((acc, s) => acc + s.totalXP, 0) / filteredStudents.length).toLocaleString()}
                </div>
                <div className="text-sm text-gray-600">Rata-rata XP</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-600">
                  {Math.round(filteredStudents.reduce((acc, s) => acc + s.level, 0) / filteredStudents.length)}
                </div>
                <div className="text-sm text-gray-600">Rata-rata Level</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bulk Import Modal */}
      <BulkImportModal
        isOpen={showImportModal}
        onClose={() => setShowImportModal(false)}
        onSuccess={() => {
          // In real implementation, refresh student list
          console.log('Import successful, refreshing list...');
        }}
      />
    </div>
  );
}