import * as React from "react";
import { cn } from "@/lib/utils";
import { Loader2, Refresh<PERSON><PERSON>, AlertCircle, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ResponsiveCard } from "./responsive-card";

// Basic loading spinner
export function LoadingSpinner({ 
  className, 
  size = "default" 
}: { 
  className?: string; 
  size?: "sm" | "default" | "lg" 
}) {
  const sizeClasses = {
    sm: "h-4 w-4",
    default: "h-6 w-6",
    lg: "h-8 w-8",
  };

  return (
    <Loader2 className={cn("animate-spin", sizeClasses[size], className)} />
  );
}

// Loading overlay for components
export function LoadingOverlay({ 
  children, 
  loading, 
  className 
}: { 
  children: React.ReactNode; 
  loading: boolean; 
  className?: string; 
}) {
  return (
    <div className={cn("relative", className)}>
      {children}
      {loading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10 rounded-lg">
          <div className="flex flex-col items-center space-y-2">
            <LoadingSpinner size="lg" />
            <p className="text-sm text-gray-600">Memuat...</p>
          </div>
        </div>
      )}
    </div>
  );
}

// Full page loading state
export function PageLoading({ message = "Memuat halaman..." }: { message?: string }) {
  return (
    <div className="min-h-screen-safe flex items-center justify-center bg-gray-50">
      <div className="text-center space-y-4">
        <LoadingSpinner size="lg" className="mx-auto" />
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-gray-900">GuruFlow</h3>
          <p className="text-sm text-gray-600">{message}</p>
        </div>
      </div>
    </div>
  );
}

// Loading button state
export function LoadingButton({
  loading,
  children,
  disabled,
  className,
  ...props
}: React.ComponentProps<typeof Button> & { loading?: boolean }) {
  return (
    <Button
      disabled={loading || disabled}
      className={cn(
        "relative",
        loading && "text-transparent",
        className
      )}
      {...props}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <LoadingSpinner size="sm" />
        </div>
      )}
      {children}
    </Button>
  );
}

// Empty state component
export function EmptyState({
  icon: Icon = AlertCircle,
  title,
  description,
  action,
  className,
}: {
  icon?: React.ComponentType<{ className?: string }>;
  title: string;
  description?: string;
  action?: React.ReactNode;
  className?: string;
}) {
  return (
    <ResponsiveCard className={cn("text-center py-12", className)}>
      <div className="space-y-4">
        <div className="mx-auto w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
          <Icon className="h-6 w-6 text-gray-400" />
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          {description && (
            <p className="text-sm text-gray-600 max-w-sm mx-auto">
              {description}
            </p>
          )}
        </div>
        {action && (
          <div className="pt-2">
            {action}
          </div>
        )}
      </div>
    </ResponsiveCard>
  );
}

// Error state component
export function ErrorState({
  title = "Terjadi Kesalahan",
  description = "Maaf, terjadi kesalahan saat memuat data. Silakan coba lagi.",
  onRetry,
  className,
}: {
  title?: string;
  description?: string;
  onRetry?: () => void;
  className?: string;
}) {
  return (
    <ResponsiveCard className={cn("text-center py-12 border-red-200 bg-red-50", className)}>
      <div className="space-y-4">
        <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
          <AlertCircle className="h-6 w-6 text-red-600" />
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-red-900">{title}</h3>
          <p className="text-sm text-red-700 max-w-sm mx-auto">
            {description}
          </p>
        </div>
        {onRetry && (
          <div className="pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="border-red-300 text-red-700 hover:bg-red-100"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Coba Lagi
            </Button>
          </div>
        )}
      </div>
    </ResponsiveCard>
  );
}

// Success state component
export function SuccessState({
  title,
  description,
  action,
  className,
}: {
  title: string;
  description?: string;
  action?: React.ReactNode;
  className?: string;
}) {
  return (
    <ResponsiveCard className={cn("text-center py-12 border-green-200 bg-green-50", className)}>
      <div className="space-y-4">
        <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle className="h-6 w-6 text-green-600" />
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-green-900">{title}</h3>
          {description && (
            <p className="text-sm text-green-700 max-w-sm mx-auto">
              {description}
            </p>
          )}
        </div>
        {action && (
          <div className="pt-2">
            {action}
          </div>
        )}
      </div>
    </ResponsiveCard>
  );
}

// Inline loading state for lists
export function InlineLoading({ 
  message = "Memuat...", 
  className 
}: { 
  message?: string; 
  className?: string; 
}) {
  return (
    <div className={cn("flex items-center justify-center py-8", className)}>
      <div className="flex items-center space-x-3">
        <LoadingSpinner size="sm" />
        <span className="text-sm text-gray-600">{message}</span>
      </div>
    </div>
  );
}

// Progressive loading for data lists
export function ProgressiveLoading({
  items,
  loading,
  hasMore,
  onLoadMore,
  className,
}: {
  items: React.ReactNode[];
  loading: boolean;
  hasMore: boolean;
  onLoadMore?: () => void;
  className?: string;
}) {
  return (
    <div className={cn("space-y-3", className)}>
      {items}
      
      {loading && (
        <InlineLoading message="Memuat lebih banyak..." />
      )}
      
      {!loading && hasMore && onLoadMore && (
        <div className="text-center py-4">
          <Button
            variant="outline"
            size="sm"
            onClick={onLoadMore}
          >
            Muat Lebih Banyak
          </Button>
        </div>
      )}
      
      {!loading && !hasMore && items.length > 0 && (
        <div className="text-center py-4">
          <p className="text-sm text-gray-500">
            Semua data telah dimuat
          </p>
        </div>
      )}
    </div>
  );
}

// Refresh indicator
export function RefreshIndicator({
  refreshing,
  onRefresh,
  className,
}: {
  refreshing: boolean;
  onRefresh: () => void;
  className?: string;
}) {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={onRefresh}
      disabled={refreshing}
      className={cn("touch-target", className)}
    >
      <RefreshCw className={cn(
        "h-4 w-4",
        refreshing && "animate-spin"
      )} />
    </Button>
  );
}