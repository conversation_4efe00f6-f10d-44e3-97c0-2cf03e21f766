import * as React from "react";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

// Responsive card that adapts to screen size
const ResponsiveCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    variant?: 'default' | 'compact' | 'elevated';
    padding?: 'sm' | 'md' | 'lg';
  }
>(({ className, variant = 'default', padding = 'md', ...props }, ref) => {
  const variantClasses = {
    default: 'bg-white border border-gray-200 shadow-sm',
    compact: 'bg-white border border-gray-200 shadow-sm',
    elevated: 'bg-white border border-gray-200 shadow-md hover:shadow-lg transition-shadow',
  };

  const paddingClasses = {
    sm: 'p-3 sm:p-4',
    md: 'p-4 sm:p-6',
    lg: 'p-6 sm:p-8',
  };

  return (
    <Card
      ref={ref}
      className={cn(
        "rounded-lg transition-all duration-200",
        variantClasses[variant],
        variant === 'compact' && paddingClasses[padding],
        className
      )}
      {...props}
    />
  );
});
ResponsiveCard.displayName = "ResponsiveCard";

// Stats card optimized for mobile
const ResponsiveStatsCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    icon?: React.ReactNode;
    title: string;
    value: string | number;
    subtitle?: string;
    trend?: {
      direction: 'up' | 'down' | 'neutral';
      value?: string;
    };
    color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray';
  }
>(({ 
  className, 
  icon, 
  title, 
  value, 
  subtitle, 
  trend, 
  color = 'blue', 
  ...props 
}, ref) => {
  const colorClasses = {
    blue: {
      bg: 'bg-blue-50 border-blue-200',
      icon: 'text-blue-600 bg-blue-100',
      accent: 'border-l-blue-500',
    },
    green: {
      bg: 'bg-green-50 border-green-200',
      icon: 'text-green-600 bg-green-100',
      accent: 'border-l-green-500',
    },
    yellow: {
      bg: 'bg-yellow-50 border-yellow-200',
      icon: 'text-yellow-600 bg-yellow-100',
      accent: 'border-l-yellow-500',
    },
    red: {
      bg: 'bg-red-50 border-red-200',
      icon: 'text-red-600 bg-red-100',
      accent: 'border-l-red-500',
    },
    purple: {
      bg: 'bg-purple-50 border-purple-200',
      icon: 'text-purple-600 bg-purple-100',
      accent: 'border-l-purple-500',
    },
    gray: {
      bg: 'bg-gray-50 border-gray-200',
      icon: 'text-gray-600 bg-gray-100',
      accent: 'border-l-gray-500',
    },
  };

  const trendClasses = {
    up: 'bg-green-100 text-green-800',
    down: 'bg-red-100 text-red-800',
    neutral: 'bg-gray-100 text-gray-800',
  };

  const trendIcons = {
    up: '↗',
    down: '↘',
    neutral: '→',
  };

  return (
    <ResponsiveCard
      ref={ref}
      className={cn(
        "border-l-4 hover:shadow-md transition-shadow",
        colorClasses[color].bg,
        colorClasses[color].accent,
        className
      )}
      padding="md"
      {...props}
    >
      <CardContent className="p-0">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            {/* Header with icon and title */}
            <div className="flex items-center space-x-3 mb-2">
              {icon && (
                <div className={cn(
                  "flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-lg flex items-center justify-center",
                  colorClasses[color].icon
                )}>
                  {icon}
                </div>
              )}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-600 truncate">
                  {title}
                </p>
              </div>
            </div>
            
            {/* Value */}
            <div className="mb-1">
              <p className="text-2xl sm:text-3xl font-bold text-gray-900">
                {value}
              </p>
            </div>
            
            {/* Subtitle */}
            {subtitle && (
              <p className="text-xs sm:text-sm text-gray-500">
                {subtitle}
              </p>
            )}
          </div>
          
          {/* Trend indicator */}
          {trend && (
            <div className={cn(
              "flex-shrink-0 ml-3 px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1",
              trendClasses[trend.direction]
            )}>
              <span>{trendIcons[trend.direction]}</span>
              {trend.value && <span>{trend.value}</span>}
            </div>
          )}
        </div>
      </CardContent>
    </ResponsiveCard>
  );
});
ResponsiveStatsCard.displayName = "ResponsiveStatsCard";

// Action card for quick actions
const ResponsiveActionCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    icon?: React.ReactNode;
    title: string;
    description?: string;
    action?: React.ReactNode;
    href?: string;
    disabled?: boolean;
  }
>(({ 
  className, 
  icon, 
  title, 
  description, 
  action, 
  href, 
  disabled = false,
  onClick,
  ...props 
}, ref) => {
  const Component = href ? 'a' : 'div';
  
  return (
    <Component
      ref={ref as any}
      href={href}
      onClick={disabled ? undefined : onClick}
      className={cn(
        "block w-full",
        !disabled && "cursor-pointer",
        disabled && "opacity-50 cursor-not-allowed"
      )}
      {...(href ? {} : props)}
    >
      <ResponsiveCard
        className={cn(
          "h-full transition-all duration-200",
          !disabled && "hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",
          className
        )}
        variant="elevated"
      >
        <CardContent className="p-4 sm:p-6">
          <div className="flex items-start space-x-3 sm:space-x-4">
            {/* Icon */}
            {icon && (
              <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <div className="text-blue-600">
                  {icon}
                </div>
              </div>
            )}
            
            {/* Content */}
            <div className="flex-1 min-w-0">
              <h4 className="text-base sm:text-lg font-semibold text-gray-900 mb-1 truncate">
                {title}
              </h4>
              {description && (
                <p className="text-sm text-gray-600 line-clamp-2">
                  {description}
                </p>
              )}
            </div>
            
            {/* Action */}
            {action && (
              <div className="flex-shrink-0">
                {action}
              </div>
            )}
          </div>
        </CardContent>
      </ResponsiveCard>
    </Component>
  );
});
ResponsiveActionCard.displayName = "ResponsiveActionCard";

// List card for displaying items in a list
const ResponsiveListCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    items: Array<{
      id: string;
      title: string;
      subtitle?: string;
      meta?: string;
      action?: React.ReactNode;
      href?: string;
    }>;
    emptyMessage?: string;
  }
>(({ className, items, emptyMessage = "Tidak ada data", ...props }, ref) => {
  if (items.length === 0) {
    return (
      <ResponsiveCard ref={ref} className={className} {...props}>
        <CardContent className="p-8 text-center">
          <p className="text-gray-500">{emptyMessage}</p>
        </CardContent>
      </ResponsiveCard>
    );
  }

  return (
    <ResponsiveCard ref={ref} className={className} {...props}>
      <CardContent className="p-0">
        <div className="divide-y divide-gray-200">
          {items.map((item, index) => {
            const ItemComponent = item.href ? 'a' : 'div';
            
            return (
              <ItemComponent
                key={item.id}
                href={item.href}
                className={cn(
                  "flex items-center justify-between p-4 sm:p-6 transition-colors",
                  item.href && "hover:bg-gray-50 cursor-pointer"
                )}
              >
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm sm:text-base font-medium text-gray-900 truncate">
                    {item.title}
                  </h4>
                  {item.subtitle && (
                    <p className="text-xs sm:text-sm text-gray-600 truncate mt-1">
                      {item.subtitle}
                    </p>
                  )}
                  {item.meta && (
                    <p className="text-xs text-gray-500 mt-1">
                      {item.meta}
                    </p>
                  )}
                </div>
                
                {item.action && (
                  <div className="flex-shrink-0 ml-4">
                    {item.action}
                  </div>
                )}
              </ItemComponent>
            );
          })}
        </div>
      </CardContent>
    </ResponsiveCard>
  );
});
ResponsiveListCard.displayName = "ResponsiveListCard";

export {
  ResponsiveCard,
  ResponsiveStatsCard,
  ResponsiveActionCard,
  ResponsiveListCard,
};