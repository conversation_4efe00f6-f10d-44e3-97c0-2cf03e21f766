import * as React from "react";
import { cn } from "@/lib/utils";

// Responsive container with fluid design
const ResponsiveContainer = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
    padding?: 'none' | 'sm' | 'md' | 'lg';
  }
>(({ className, maxWidth = 'full', padding = 'md', children, ...props }, ref) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full',
  };

  const paddingClasses = {
    none: '',
    sm: 'px-4 sm:px-6',
    md: 'px-4 sm:px-6 lg:px-8',
    lg: 'px-6 sm:px-8 lg:px-12',
  };

  return (
    <div
      ref={ref}
      className={cn(
        "mx-auto w-full",
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
});
ResponsiveContainer.displayName = "ResponsiveContainer";

// Responsive grid with automatic columns
const ResponsiveGrid = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    cols?: {
      default?: number;
      sm?: number;
      md?: number;
      lg?: number;
      xl?: number;
    };
    gap?: 'sm' | 'md' | 'lg';
  }
>(({ className, cols = { default: 1, sm: 2, lg: 3 }, gap = 'md', children, ...props }, ref) => {
  const gapClasses = {
    sm: 'gap-3',
    md: 'gap-4',
    lg: 'gap-6',
  };

  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
    5: 'grid-cols-5',
    6: 'grid-cols-6',
  };

  const getGridClasses = () => {
    const classes = ['grid'];
    
    if (cols.default) classes.push(gridCols[cols.default as keyof typeof gridCols]);
    if (cols.sm) classes.push(`sm:${gridCols[cols.sm as keyof typeof gridCols]}`);
    if (cols.md) classes.push(`md:${gridCols[cols.md as keyof typeof gridCols]}`);
    if (cols.lg) classes.push(`lg:${gridCols[cols.lg as keyof typeof gridCols]}`);
    if (cols.xl) classes.push(`xl:${gridCols[cols.xl as keyof typeof gridCols]}`);
    
    return classes.join(' ');
  };

  return (
    <div
      ref={ref}
      className={cn(
        getGridClasses(),
        gapClasses[gap],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
});
ResponsiveGrid.displayName = "ResponsiveGrid";

// Responsive flex container
const ResponsiveFlex = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    direction?: {
      default?: 'row' | 'col';
      sm?: 'row' | 'col';
      md?: 'row' | 'col';
      lg?: 'row' | 'col';
    };
    gap?: 'sm' | 'md' | 'lg';
    align?: 'start' | 'center' | 'end' | 'stretch';
    justify?: 'start' | 'center' | 'end' | 'between' | 'around';
  }
>(({ 
  className, 
  direction = { default: 'row' }, 
  gap = 'md', 
  align = 'start',
  justify = 'start',
  children, 
  ...props 
}, ref) => {
  const gapClasses = {
    sm: 'gap-3',
    md: 'gap-4',
    lg: 'gap-6',
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
  };

  const getFlexClasses = () => {
    const classes = ['flex'];
    
    if (direction.default === 'col') classes.push('flex-col');
    if (direction.sm === 'row') classes.push('sm:flex-row');
    if (direction.sm === 'col') classes.push('sm:flex-col');
    if (direction.md === 'row') classes.push('md:flex-row');
    if (direction.md === 'col') classes.push('md:flex-col');
    if (direction.lg === 'row') classes.push('lg:flex-row');
    if (direction.lg === 'col') classes.push('lg:flex-col');
    
    return classes.join(' ');
  };

  return (
    <div
      ref={ref}
      className={cn(
        getFlexClasses(),
        gapClasses[gap],
        alignClasses[align],
        justifyClasses[justify],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
});
ResponsiveFlex.displayName = "ResponsiveFlex";

// Responsive section with proper spacing
const ResponsiveSection = React.forwardRef<
  HTMLElement,
  React.HTMLAttributes<HTMLElement> & {
    spacing?: 'sm' | 'md' | 'lg' | 'xl';
    background?: 'white' | 'gray' | 'transparent';
  }
>(({ className, spacing = 'md', background = 'transparent', children, ...props }, ref) => {
  const spacingClasses = {
    sm: 'py-4 sm:py-6',
    md: 'py-6 sm:py-8 lg:py-12',
    lg: 'py-8 sm:py-12 lg:py-16',
    xl: 'py-12 sm:py-16 lg:py-20',
  };

  const backgroundClasses = {
    white: 'bg-white',
    gray: 'bg-gray-50',
    transparent: 'bg-transparent',
  };

  return (
    <section
      ref={ref}
      className={cn(
        spacingClasses[spacing],
        backgroundClasses[background],
        className
      )}
      {...props}
    >
      {children}
    </section>
  );
});
ResponsiveSection.displayName = "ResponsiveSection";

export {
  ResponsiveContainer,
  ResponsiveGrid,
  ResponsiveFlex,
  ResponsiveSection,
};