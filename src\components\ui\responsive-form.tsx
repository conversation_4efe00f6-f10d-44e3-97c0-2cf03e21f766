import * as React from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ResponsiveCard } from "./responsive-card";

// Responsive form container
const ResponsiveForm = React.forwardRef<
  HTMLFormElement,
  React.FormHTMLAttributes<HTMLFormElement> & {
    spacing?: 'sm' | 'md' | 'lg';
  }
>(({ className, spacing = 'md', ...props }, ref) => {
  const spacingClasses = {
    sm: 'space-y-4',
    md: 'space-y-6',
    lg: 'space-y-8',
  };

  return (
    <form
      ref={ref}
      className={cn(spacingClasses[spacing], className)}
      {...props}
    />
  );
});
ResponsiveForm.displayName = "ResponsiveForm";

// Responsive form section with card wrapper
const ResponsiveFormSection = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    title?: string;
    description?: string;
    spacing?: 'sm' | 'md' | 'lg';
  }
>(({ className, title, description, spacing = 'md', children, ...props }, ref) => {
  const spacingClasses = {
    sm: 'space-y-3',
    md: 'space-y-4',
    lg: 'space-y-6',
  };

  return (
    <ResponsiveCard ref={ref} className={className} {...props}>
      <div className="p-4 sm:p-6">
        {(title || description) && (
          <div className="mb-6">
            {title && (
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {title}
              </h3>
            )}
            {description && (
              <p className="text-sm text-gray-600">
                {description}
              </p>
            )}
          </div>
        )}
        <div className={spacingClasses[spacing]}>
          {children}
        </div>
      </div>
    </ResponsiveCard>
  );
});
ResponsiveFormSection.displayName = "ResponsiveFormSection";

// Responsive form field with enhanced mobile support
const ResponsiveFormField = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    label?: string;
    error?: string;
    hint?: string;
    required?: boolean;
    layout?: 'vertical' | 'horizontal';
  }
>(({ 
  className, 
  label, 
  error, 
  hint, 
  required, 
  layout = 'vertical',
  children, 
  ...props 
}, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "space-y-2",
        layout === 'horizontal' && "sm:flex sm:items-start sm:space-y-0 sm:space-x-4",
        className
      )}
      {...props}
    >
      {label && (
        <Label 
          className={cn(
            "text-sm font-medium text-gray-700",
            layout === 'horizontal' && "sm:w-1/3 sm:pt-2",
            error && "text-red-600"
          )}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      
      <div className={cn(
        "space-y-1",
        layout === 'horizontal' && "sm:w-2/3"
      )}>
        {children}
        
        {hint && !error && (
          <p className="text-xs text-gray-500">
            {hint}
          </p>
        )}
        
        {error && (
          <p className="text-xs text-red-600 flex items-center space-x-1">
            <span>⚠️</span>
            <span>{error}</span>
          </p>
        )}
      </div>
    </div>
  );
});
ResponsiveFormField.displayName = "ResponsiveFormField";

// Enhanced mobile input with better touch targets
const ResponsiveInput = React.forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement> & {
    error?: boolean;
    fullWidth?: boolean;
  }
>(({ className, error, fullWidth = true, type, ...props }, ref) => (
  <Input
    type={type}
    className={cn(
      // Base mobile-friendly styles
      "h-11 sm:h-10 text-base sm:text-sm px-4 py-3 sm:py-2",
      "border-gray-300 rounded-lg",
      "focus:border-blue-500 focus:ring-2 focus:ring-blue-200",
      "transition-colors duration-200",
      // Error state
      error && "border-red-500 focus:border-red-500 focus:ring-red-200",
      // Full width
      fullWidth && "w-full",
      className
    )}
    ref={ref}
    {...props}
  />
));
ResponsiveInput.displayName = "ResponsiveInput";

// Enhanced mobile textarea
const ResponsiveTextarea = React.forwardRef<
  HTMLTextAreaElement,
  React.TextareaHTMLAttributes<HTMLTextAreaElement> & {
    error?: boolean;
    fullWidth?: boolean;
  }
>(({ className, error, fullWidth = true, ...props }, ref) => (
  <Textarea
    className={cn(
      // Base mobile-friendly styles
      "min-h-[100px] text-base sm:text-sm px-4 py-3 sm:py-2",
      "border-gray-300 rounded-lg resize-none",
      "focus:border-blue-500 focus:ring-2 focus:ring-blue-200",
      "transition-colors duration-200",
      // Error state
      error && "border-red-500 focus:border-red-500 focus:ring-red-200",
      // Full width
      fullWidth && "w-full",
      className
    )}
    ref={ref}
    {...props}
  />
));
ResponsiveTextarea.displayName = "ResponsiveTextarea";

// Enhanced mobile select
const ResponsiveSelect = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<typeof SelectTrigger> & {
    error?: boolean;
    placeholder?: string;
    children: React.ReactNode;
  }
>(({ className, error, placeholder, children, ...props }, ref) => (
  <Select>
    <SelectTrigger
      ref={ref}
      className={cn(
        // Base mobile-friendly styles
        "h-11 sm:h-10 text-base sm:text-sm px-4 py-3 sm:py-2",
        "border-gray-300 rounded-lg",
        "focus:border-blue-500 focus:ring-2 focus:ring-blue-200",
        "transition-colors duration-200",
        // Error state
        error && "border-red-500 focus:border-red-500 focus:ring-red-200",
        className
      )}
      {...props}
    >
      <SelectValue placeholder={placeholder} />
    </SelectTrigger>
    <SelectContent>
      {children}
    </SelectContent>
  </Select>
));
ResponsiveSelect.displayName = "ResponsiveSelect";

// Responsive form actions with proper mobile layout
const ResponsiveFormActions = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    primaryAction?: React.ReactNode;
    secondaryAction?: React.ReactNode;
    layout?: 'horizontal' | 'vertical' | 'auto';
    align?: 'start' | 'center' | 'end';
  }
>(({ 
  className, 
  primaryAction, 
  secondaryAction, 
  layout = 'auto', 
  align = 'end',
  children, 
  ...props 
}, ref) => {
  const alignClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
  };

  const getLayoutClasses = () => {
    if (layout === 'vertical') return 'flex-col space-y-3';
    if (layout === 'horizontal') return 'flex-row space-x-3';
    // Auto layout: vertical on mobile, horizontal on desktop
    return 'flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3';
  };

  return (
    <div
      ref={ref}
      className={cn(
        "flex pt-6 border-t border-gray-200",
        getLayoutClasses(),
        alignClasses[align],
        className
      )}
      {...props}
    >
      {children || (
        <>
          {secondaryAction && (
            <div className={layout !== 'vertical' ? "flex-1 sm:flex-initial" : ""}>
              {secondaryAction}
            </div>
          )}
          {primaryAction && (
            <div className={layout !== 'vertical' ? "flex-1 sm:flex-initial" : ""}>
              {primaryAction}
            </div>
          )}
        </>
      )}
    </div>
  );
});
ResponsiveFormActions.displayName = "ResponsiveFormActions";

// Enhanced mobile button
const ResponsiveButton = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<typeof Button> & {
    fullWidth?: boolean;
  }
>(({ className, size = "default", fullWidth = false, ...props }, ref) => (
  <Button
    ref={ref}
    size={size}
    className={cn(
      // Enhanced touch targets for mobile
      "h-11 sm:h-10 text-base sm:text-sm font-medium",
      "min-w-[44px] min-h-[44px] sm:min-w-0 sm:min-h-0",
      "transition-all duration-200",
      "active:scale-[0.98]",
      // Size variations
      size === "sm" && "h-9 text-sm",
      size === "lg" && "h-12 text-lg",
      // Full width
      fullWidth && "w-full",
      className
    )}
    {...props}
  />
));
ResponsiveButton.displayName = "ResponsiveButton";

// Responsive checkbox/radio group
const ResponsiveChoiceGroup = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    label?: string;
    error?: string;
    layout?: 'vertical' | 'horizontal' | 'grid';
    columns?: number;
  }
>(({ 
  className, 
  label, 
  error, 
  layout = 'vertical', 
  columns = 2,
  children, 
  ...props 
}, ref) => {
  const getLayoutClasses = () => {
    if (layout === 'horizontal') return 'flex flex-wrap gap-4';
    if (layout === 'grid') return `grid grid-cols-1 sm:grid-cols-${columns} gap-3`;
    return 'space-y-3';
  };

  return (
    <div
      ref={ref}
      className={cn("space-y-3", className)}
      {...props}
    >
      {label && (
        <Label className={cn(
          "text-sm font-medium text-gray-700",
          error && "text-red-600"
        )}>
          {label}
        </Label>
      )}
      
      <div className={getLayoutClasses()}>
        {children}
      </div>
      
      {error && (
        <p className="text-xs text-red-600 flex items-center space-x-1">
          <span>⚠️</span>
          <span>{error}</span>
        </p>
      )}
    </div>
  );
});
ResponsiveChoiceGroup.displayName = "ResponsiveChoiceGroup";

export {
  ResponsiveForm,
  ResponsiveFormSection,
  ResponsiveFormField,
  ResponsiveInput,
  ResponsiveTextarea,
  ResponsiveSelect,
  ResponsiveFormActions,
  ResponsiveButton,
  ResponsiveChoiceGroup,
};