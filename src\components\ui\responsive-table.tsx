import * as React from "react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/useResponsive";
import { ResponsiveCard } from "./responsive-card";

// Responsive table that converts to cards on mobile
interface Column<T> {
  key: keyof T;
  header: string;
  render?: (value: any, item: T) => React.ReactNode;
  className?: string;
  sortable?: boolean;
  mobileLabel?: string; // Label to show on mobile card view
}

interface ResponsiveTableProps<T> {
  data: T[];
  columns: Column<T>[];
  keyField: keyof T;
  className?: string;
  emptyMessage?: string;
  loading?: boolean;
  onRowClick?: (item: T) => void;
  mobileCardRender?: (item: T) => React.ReactNode; // Custom mobile card renderer
}

export function ResponsiveTable<T extends Record<string, any>>({
  data,
  columns,
  keyField,
  className,
  emptyMessage = "Tidak ada data",
  loading = false,
  onRowClick,
  mobileCardRender,
}: ResponsiveTableProps<T>) {
  const isMobile = useIsMobile();

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-16 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <ResponsiveCard className="text-center py-12">
        <p className="text-gray-500">{emptyMessage}</p>
      </ResponsiveCard>
    );
  }

  // Mobile view - render as cards
  if (isMobile) {
    return (
      <div className="space-y-3">
        {data.map((item) => {
          if (mobileCardRender) {
            return (
              <div
                key={String(item[keyField])}
                onClick={() => onRowClick?.(item)}
                className={cn(
                  onRowClick && "cursor-pointer"
                )}
              >
                {mobileCardRender(item)}
              </div>
            );
          }

          return (
            <ResponsiveCard
              key={String(item[keyField])}
              onClick={() => onRowClick?.(item)}
              className={cn(
                "transition-all duration-200",
                onRowClick && "cursor-pointer hover:shadow-md active:scale-[0.98]"
              )}
            >
              <div className="p-4 space-y-3">
                {columns.map((column) => {
                  const value = item[column.key];
                  const displayValue = column.render ? column.render(value, item) : value;
                  
                  return (
                    <div key={String(column.key)} className="flex justify-between items-start">
                      <span className="text-sm font-medium text-gray-600 flex-shrink-0 mr-3">
                        {column.mobileLabel || column.header}:
                      </span>
                      <span className="text-sm text-gray-900 text-right flex-1">
                        {displayValue}
                      </span>
                    </div>
                  );
                })}
              </div>
            </ResponsiveCard>
          );
        })}
      </div>
    );
  }

  // Desktop view - render as table
  return (
    <div className={cn("overflow-hidden", className)}>
      <ResponsiveCard>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <th
                    key={String(column.key)}
                    className={cn(
                      "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                      column.className
                    )}
                  >
                    {column.header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.map((item) => (
                <tr
                  key={String(item[keyField])}
                  onClick={() => onRowClick?.(item)}
                  className={cn(
                    "transition-colors",
                    onRowClick && "cursor-pointer hover:bg-gray-50"
                  )}
                >
                  {columns.map((column) => {
                    const value = item[column.key];
                    const displayValue = column.render ? column.render(value, item) : value;
                    
                    return (
                      <td
                        key={String(column.key)}
                        className={cn(
                          "px-6 py-4 whitespace-nowrap text-sm text-gray-900",
                          column.className
                        )}
                      >
                        {displayValue}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </ResponsiveCard>
    </div>
  );
}

// Simplified responsive data list for common use cases
interface ResponsiveDataListProps<T> {
  data: T[];
  keyField: keyof T;
  renderItem: (item: T) => {
    title: string;
    subtitle?: string;
    meta?: string;
    actions?: React.ReactNode;
  };
  onItemClick?: (item: T) => void;
  className?: string;
  emptyMessage?: string;
  loading?: boolean;
}

export function ResponsiveDataList<T extends Record<string, any>>({
  data,
  keyField,
  renderItem,
  onItemClick,
  className,
  emptyMessage = "Tidak ada data",
  loading = false,
}: ResponsiveDataListProps<T>) {
  if (loading) {
    return (
      <div className="space-y-3">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="animate-pulse">
            <ResponsiveCard>
              <div className="p-4 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </ResponsiveCard>
          </div>
        ))}
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <ResponsiveCard className="text-center py-12">
        <p className="text-gray-500">{emptyMessage}</p>
      </ResponsiveCard>
    );
  }

  return (
    <div className={cn("space-y-3", className)}>
      {data.map((item) => {
        const rendered = renderItem(item);
        
        return (
          <ResponsiveCard
            key={String(item[keyField])}
            onClick={() => onItemClick?.(item)}
            className={cn(
              "transition-all duration-200",
              onItemClick && "cursor-pointer hover:shadow-md active:scale-[0.98]"
            )}
          >
            <div className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h4 className="text-base font-semibold text-gray-900 truncate">
                    {rendered.title}
                  </h4>
                  {rendered.subtitle && (
                    <p className="text-sm text-gray-600 truncate mt-1">
                      {rendered.subtitle}
                    </p>
                  )}
                  {rendered.meta && (
                    <p className="text-xs text-gray-500 mt-2">
                      {rendered.meta}
                    </p>
                  )}
                </div>
                
                {rendered.actions && (
                  <div className="flex-shrink-0 ml-4">
                    {rendered.actions}
                  </div>
                )}
              </div>
            </div>
          </ResponsiveCard>
        );
      })}
    </div>
  );
}