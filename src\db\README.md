# GuruFlow Database Schema

This directory contains the database schema, migrations, and related utilities for the GuruFlow application.

## Overview

GuruFlow uses Turso (libSQL) as the database with Drizzle ORM for type-safe database operations. The schema is designed to support Indonesian school administration needs including:

- School configuration and branding
- User management (teachers and students)
- Classroom and subject management
- Student enrollment and profiles
- Attendance tracking with Indonesian codes (H/I/S/A)
- Assignment and grading system
- Gamification with XP and badges

## Files Structure

```
src/db/
├── schema.ts           # Complete database schema definitions
├── migrations/         # Generated migration files
└── README.md          # This file

src/lib/
├── db.ts              # Database connection and utilities
├── migrate.ts         # Migration management utilities
├── seed.ts            # Database seeding with initial data
└── database-utils.ts  # Comprehensive database utilities
```

## Database Tables

### Core System Tables
- **schools**: School configuration (singleton table)
- **users**: All system users (teachers and students)
- **sessions**: Authentication session management

### Academic Structure
- **classrooms**: Class definitions with homeroom teachers
- **subjects**: Subject/course definitions
- **classroom_subjects**: Many-to-many with teacher assignments
- **students**: Student enrollment and profile data

### Operational Data
- **attendance_records**: Daily attendance with Indonesian codes
- **assignments**: Teacher-created tasks and assessments
- **submissions**: Student assignment submissions
- **grades**: Academic performance records

### Gamification System
- **student_xp**: Experience points tracking
- **badges**: Achievement definitions
- **student_badges**: Earned student achievements

## Indonesian Attendance Codes

The system uses standard Indonesian attendance codes:
- **H** (Hadir): Present
- **I** (Izin): Excused absence with permission
- **S** (Sakit): Sick leave
- **A** (Alpa): Unexcused absence

## Setup Instructions

### 1. Environment Configuration

Copy `.env.example` to `.env.local` and configure:

```bash
DATABASE_URL="libsql://your-database.turso.io"
DATABASE_AUTH_TOKEN="your-auth-token"
```

### 2. Generate Migrations

```bash
# Generate migration from schema changes
npm run db:generate

# Apply migrations to database
npm run db:migrate

# Push schema directly (development only)
npm run db:push
```

### 3. Database Initialization

```bash
# Run complete database setup
npm run db:setup

# Seed with initial data
npm run db:seed
```

## Development Commands

```bash
# View database in Drizzle Studio
npm run db:studio

# Check database health
npm run db:health

# Reset database (development only)
npm run db:reset
```

## Schema Relationships

### User Roles and Permissions
- Teachers can manage classrooms, subjects, and students
- Students can view their own data and submit assignments
- Superadmin (first teacher) has full system access

### Classroom Structure
- Each classroom has one homeroom teacher
- Multiple subjects can be assigned to a classroom
- Each classroom-subject pair can have a specific teacher
- Students are enrolled in one classroom at a time

### Attendance System
- Daily attendance records per student per classroom
- Supports historical tracking and editing
- Includes notes for absence reasons
- Tracks who recorded the attendance

### Grading System
- Supports multiple grade types (assignment, quiz, exam, project)
- Links to assignments or can be standalone
- Tracks grading teacher and timestamp
- Supports Indonesian grading standards

### Gamification
- XP points awarded for various activities
- Badges with configurable earning criteria
- Leaderboards and progress tracking
- Motivational system for student engagement

## Data Types and Constraints

### Timestamps
All tables include `created_at` and `updated_at` timestamps using SQLite's `CURRENT_TIMESTAMP`.

### Foreign Keys
Proper foreign key relationships with cascade deletes where appropriate:
- User deletion cascades to sessions and student records
- Classroom deletion sets references to null (preserves history)
- Assignment deletion cascades to submissions and grades

### Unique Constraints
- User emails must be unique
- Student IDs must be unique within the school
- Subject codes must be unique

### Boolean Fields
Using SQLite integer fields with boolean mode for compatibility:
- `is_active`, `is_super_admin`, etc.

## Migration Strategy

### Development
Use `db:push` for rapid schema iteration during development.

### Production
Always use proper migrations (`db:generate` + `db:migrate`) for production deployments.

### Rollback Strategy
Drizzle Kit supports migration rollbacks. Keep migration files in version control.

## Performance Considerations

### Indexes
Key indexes are automatically created for:
- Primary keys
- Foreign key relationships
- Unique constraints

### Query Optimization
- Use Drizzle's query builder for type safety
- Implement proper pagination for large datasets
- Consider read replicas for reporting queries

## Security Notes

### Data Protection
- Passwords are hashed using bcrypt
- Session tokens are cryptographically secure
- Sensitive data is properly validated

### Access Control
- Role-based permissions at application level
- Database-level constraints prevent data corruption
- Audit trails for critical operations

## Backup and Recovery

### Automated Backups
Turso provides automated backups. Configure retention policies based on needs.

### Manual Backups
```bash
# Export database (when available)
npm run db:export

# Import database (when available)
npm run db:import
```

## Monitoring

### Health Checks
Use `checkDatabaseHealth()` function for application monitoring.

### Performance Metrics
Monitor query performance and connection pool usage in production.

### Error Tracking
Database errors are wrapped with context for better debugging.