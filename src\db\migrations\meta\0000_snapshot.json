{"version": "6", "dialect": "sqlite", "id": "6375b357-9159-40c1-8a28-a530a8363936", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"assignments": {"name": "assignments", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "classroom_subject_id": {"name": "classroom_subject_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "due_date": {"name": "due_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "max_score": {"name": "max_score", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 100}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"assignments_classroom_subject_id_classroom_subjects_id_fk": {"name": "assignments_classroom_subject_id_classroom_subjects_id_fk", "tableFrom": "assignments", "tableTo": "classroom_subjects", "columnsFrom": ["classroom_subject_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "assignments_created_by_users_id_fk": {"name": "assignments_created_by_users_id_fk", "tableFrom": "assignments", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "attendance_records": {"name": "attendance_records", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "student_id": {"name": "student_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "classroom_id": {"name": "classroom_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "date": {"name": "date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "recorded_by": {"name": "recorded_by", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"attendance_records_student_id_students_id_fk": {"name": "attendance_records_student_id_students_id_fk", "tableFrom": "attendance_records", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "attendance_records_classroom_id_classrooms_id_fk": {"name": "attendance_records_classroom_id_classrooms_id_fk", "tableFrom": "attendance_records", "tableTo": "classrooms", "columnsFrom": ["classroom_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "attendance_records_recorded_by_users_id_fk": {"name": "attendance_records_recorded_by_users_id_fk", "tableFrom": "attendance_records", "tableTo": "users", "columnsFrom": ["recorded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "badges": {"name": "badges", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "criteria": {"name": "criteria", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "xp_reward": {"name": "xp_reward", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "classroom_subjects": {"name": "classroom_subjects", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "classroom_id": {"name": "classroom_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "subject_id": {"name": "subject_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "teacher_id": {"name": "teacher_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"classroom_subjects_classroom_id_classrooms_id_fk": {"name": "classroom_subjects_classroom_id_classrooms_id_fk", "tableFrom": "classroom_subjects", "tableTo": "classrooms", "columnsFrom": ["classroom_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "classroom_subjects_subject_id_subjects_id_fk": {"name": "classroom_subjects_subject_id_subjects_id_fk", "tableFrom": "classroom_subjects", "tableTo": "subjects", "columnsFrom": ["subject_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "classroom_subjects_teacher_id_users_id_fk": {"name": "classroom_subjects_teacher_id_users_id_fk", "tableFrom": "classroom_subjects", "tableTo": "users", "columnsFrom": ["teacher_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "classrooms": {"name": "classrooms", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "grade": {"name": "grade", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "academic_year": {"name": "academic_year", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "homeroom_teacher_id": {"name": "homeroom_teacher_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"classrooms_homeroom_teacher_id_users_id_fk": {"name": "classrooms_homeroom_teacher_id_users_id_fk", "tableFrom": "classrooms", "tableTo": "users", "columnsFrom": ["homeroom_teacher_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "grades": {"name": "grades", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "submission_id": {"name": "submission_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "student_id": {"name": "student_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "classroom_subject_id": {"name": "classroom_subject_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "max_score": {"name": "max_score", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "grade_type": {"name": "grade_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "graded_by": {"name": "graded_by", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "graded_at": {"name": "graded_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"grades_submission_id_submissions_id_fk": {"name": "grades_submission_id_submissions_id_fk", "tableFrom": "grades", "tableTo": "submissions", "columnsFrom": ["submission_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "grades_student_id_students_id_fk": {"name": "grades_student_id_students_id_fk", "tableFrom": "grades", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "grades_classroom_subject_id_classroom_subjects_id_fk": {"name": "grades_classroom_subject_id_classroom_subjects_id_fk", "tableFrom": "grades", "tableTo": "classroom_subjects", "columnsFrom": ["classroom_subject_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "grades_graded_by_users_id_fk": {"name": "grades_graded_by_users_id_fk", "tableFrom": "grades", "tableTo": "users", "columnsFrom": ["graded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "schools": {"name": "schools", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "signature": {"name": "signature", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "level": {"name": "level", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "sessions": {"name": "sessions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "student_badges": {"name": "student_badges", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "student_id": {"name": "student_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "badge_id": {"name": "badge_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "earned_at": {"name": "earned_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"student_badges_student_id_students_id_fk": {"name": "student_badges_student_id_students_id_fk", "tableFrom": "student_badges", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "student_badges_badge_id_badges_id_fk": {"name": "student_badges_badge_id_badges_id_fk", "tableFrom": "student_badges", "tableTo": "badges", "columnsFrom": ["badge_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "student_xp": {"name": "student_xp", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "student_id": {"name": "student_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "points": {"name": "points", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "source_id": {"name": "source_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "source_type": {"name": "source_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "awarded_by": {"name": "awarded_by", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"student_xp_student_id_students_id_fk": {"name": "student_xp_student_id_students_id_fk", "tableFrom": "student_xp", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "student_xp_awarded_by_users_id_fk": {"name": "student_xp_awarded_by_users_id_fk", "tableFrom": "student_xp", "tableTo": "users", "columnsFrom": ["awarded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "students": {"name": "students", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "student_id": {"name": "student_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "classroom_id": {"name": "classroom_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "enrollment_date": {"name": "enrollment_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"students_student_id_unique": {"name": "students_student_id_unique", "columns": ["student_id"], "isUnique": true}}, "foreignKeys": {"students_user_id_users_id_fk": {"name": "students_user_id_users_id_fk", "tableFrom": "students", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "students_classroom_id_classrooms_id_fk": {"name": "students_classroom_id_classrooms_id_fk", "tableFrom": "students", "tableTo": "classrooms", "columnsFrom": ["classroom_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "subjects": {"name": "subjects", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"subjects_code_unique": {"name": "subjects_code_unique", "columns": ["code"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "submissions": {"name": "submissions", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "assignment_id": {"name": "assignment_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "student_id": {"name": "student_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_url": {"name": "file_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "submitted_at": {"name": "submitted_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"submissions_assignment_id_assignments_id_fk": {"name": "submissions_assignment_id_assignments_id_fk", "tableFrom": "submissions", "tableTo": "assignments", "columnsFrom": ["assignment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "submissions_student_id_students_id_fk": {"name": "submissions_student_id_students_id_fk", "tableFrom": "submissions", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "is_super_admin": {"name": "is_super_admin", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}