import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

// Schools table - Configuration for each school instance
export const schools = sqliteTable('schools', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  name: text('name').notNull(),
  logo: text('logo'), // URL or path to logo file
  signature: text('signature'), // URL or path to signature file
  level: text('level').notNull(), // 'SD', 'SMP', 'SMA'
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull()
});

// Users table - All system users (teachers and students)
export const users = sqliteTable('users', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  email: text('email').notNull().unique(),
  passwordHash: text('password_hash').notNull(),
  role: text('role').notNull(), // 'teacher' | 'student'
  name: text('name').notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).default(true).notNull(),
  isSuperAdmin: integer('is_super_admin', { mode: 'boolean' }).default(false).notNull(),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull()
});

// Sessions table - Authentication session management
export const sessions = sqliteTable('sessions', {
  id: text('id').primaryKey(),
  userId: integer('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  expiresAt: text('expires_at').notNull(),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull()
});

// Classrooms table - Class definitions and management
export const classrooms = sqliteTable('classrooms', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  name: text('name').notNull(),
  grade: text('grade').notNull(), // e.g., '7A', '8B', '12 IPA 1'
  academicYear: text('academic_year').notNull(), // e.g., '2024/2025'
  homeroomTeacherId: integer('homeroom_teacher_id').references(() => users.id, { onDelete: 'set null' }),
  isActive: integer('is_active', { mode: 'boolean' }).default(true).notNull(),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull()
});

// Subjects table - Course/subject definitions
export const subjects = sqliteTable('subjects', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  name: text('name').notNull(), // e.g., 'Matematika', 'Bahasa Indonesia'
  code: text('code').notNull().unique(), // e.g., 'MTK', 'BIND'
  description: text('description'),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull()
});

// Classroom Subjects table - Many-to-many relationship between classrooms and subjects with teacher assignment
export const classroomSubjects = sqliteTable('classroom_subjects', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  classroomId: integer('classroom_id').notNull().references(() => classrooms.id, { onDelete: 'cascade' }),
  subjectId: integer('subject_id').notNull().references(() => subjects.id, { onDelete: 'cascade' }),
  teacherId: integer('teacher_id').references(() => users.id, { onDelete: 'set null' }),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull()
});

// Students table - Student enrollment and profile information
export const students = sqliteTable('students', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  userId: integer('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  studentId: text('student_id').notNull().unique(), // School-specific student ID (NIS/NISN)
  classroomId: integer('classroom_id').references(() => classrooms.id, { onDelete: 'set null' }),
  enrollmentDate: text('enrollment_date').notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).default(true).notNull(),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull()
});

// Attendance Records table - Daily attendance tracking
export const attendanceRecords = sqliteTable('attendance_records', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  studentId: integer('student_id').notNull().references(() => students.id, { onDelete: 'cascade' }),
  classroomId: integer('classroom_id').notNull().references(() => classrooms.id, { onDelete: 'cascade' }),
  date: text('date').notNull(), // Format: YYYY-MM-DD
  status: text('status').notNull(), // 'H' (Hadir), 'I' (Izin), 'S' (Sakit), 'A' (Alpa)
  notes: text('notes'), // Optional notes for absence reasons
  recordedBy: integer('recorded_by').notNull().references(() => users.id),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull()
});

// Assignments table - Teacher-created tasks and assessments
export const assignments = sqliteTable('assignments', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  title: text('title').notNull(),
  description: text('description'),
  classroomSubjectId: integer('classroom_subject_id').notNull().references(() => classroomSubjects.id, { onDelete: 'cascade' }),
  dueDate: text('due_date'), // Format: YYYY-MM-DD HH:MM:SS
  maxScore: integer('max_score').default(100).notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).default(true).notNull(),
  createdBy: integer('created_by').notNull().references(() => users.id),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull()
});

// Submissions table - Student assignment submissions
export const submissions = sqliteTable('submissions', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  assignmentId: integer('assignment_id').notNull().references(() => assignments.id, { onDelete: 'cascade' }),
  studentId: integer('student_id').notNull().references(() => students.id, { onDelete: 'cascade' }),
  content: text('content'), // Submission text or file reference
  fileUrl: text('file_url'), // URL to uploaded file if applicable
  submittedAt: text('submitted_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull()
});

// Grades table - Academic performance records
export const grades = sqliteTable('grades', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  submissionId: integer('submission_id').references(() => submissions.id, { onDelete: 'cascade' }),
  studentId: integer('student_id').notNull().references(() => students.id, { onDelete: 'cascade' }),
  classroomSubjectId: integer('classroom_subject_id').notNull().references(() => classroomSubjects.id, { onDelete: 'cascade' }),
  score: integer('score').notNull(),
  maxScore: integer('max_score').notNull(),
  gradeType: text('grade_type').notNull(), // 'assignment', 'quiz', 'exam', 'project'
  gradedBy: integer('graded_by').notNull().references(() => users.id),
  gradedAt: text('graded_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull()
});

// Student XP table - Gamification experience points tracking
export const studentXp = sqliteTable('student_xp', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  studentId: integer('student_id').notNull().references(() => students.id, { onDelete: 'cascade' }),
  points: integer('points').notNull(),
  reason: text('reason').notNull(), // 'attendance', 'assignment_completion', 'good_grade', etc.
  sourceId: integer('source_id'), // Reference to the source record (assignment, attendance, etc.)
  sourceType: text('source_type'), // 'assignment', 'attendance', 'grade'
  awardedBy: integer('awarded_by').references(() => users.id),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull()
});

// Badges table - Achievement definitions
export const badges = sqliteTable('badges', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  name: text('name').notNull(),
  description: text('description').notNull(),
  icon: text('icon'), // Icon identifier or URL
  criteria: text('criteria').notNull(), // JSON string describing earning criteria
  xpReward: integer('xp_reward').default(0).notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).default(true).notNull(),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull()
});

// Student Badges table - Earned achievements
export const studentBadges = sqliteTable('student_badges', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  studentId: integer('student_id').notNull().references(() => students.id, { onDelete: 'cascade' }),
  badgeId: integer('badge_id').notNull().references(() => badges.id, { onDelete: 'cascade' }),
  earnedAt: text('earned_at').default(sql`CURRENT_TIMESTAMP`).notNull()
});

// Export all table types for use in other files
export type School = typeof schools.$inferSelect;
export type NewSchool = typeof schools.$inferInsert;

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

export type Session = typeof sessions.$inferSelect;
export type NewSession = typeof sessions.$inferInsert;

export type Classroom = typeof classrooms.$inferSelect;
export type NewClassroom = typeof classrooms.$inferInsert;

export type Subject = typeof subjects.$inferSelect;
export type NewSubject = typeof subjects.$inferInsert;

export type ClassroomSubject = typeof classroomSubjects.$inferSelect;
export type NewClassroomSubject = typeof classroomSubjects.$inferInsert;

export type Student = typeof students.$inferSelect;
export type NewStudent = typeof students.$inferInsert;

export type AttendanceRecord = typeof attendanceRecords.$inferSelect;
export type NewAttendanceRecord = typeof attendanceRecords.$inferInsert;

export type Assignment = typeof assignments.$inferSelect;
export type NewAssignment = typeof assignments.$inferInsert;

export type Submission = typeof submissions.$inferSelect;
export type NewSubmission = typeof submissions.$inferInsert;

export type Grade = typeof grades.$inferSelect;
export type NewGrade = typeof grades.$inferInsert;

export type StudentXp = typeof studentXp.$inferSelect;
export type NewStudentXp = typeof studentXp.$inferInsert;

export type Badge = typeof badges.$inferSelect;
export type NewBadge = typeof badges.$inferInsert;

export type StudentBadge = typeof studentBadges.$inferSelect;
export type NewStudentBadge = typeof studentBadges.$inferInsert;

// Attendance audit trail - temporarily disabled
// export const attendanceAudit = sqliteTable('attendance_audit', {
//   id: integer('id').primaryKey({ autoIncrement: true }),
//   attendanceId: integer('attendance_id').notNull().references(() => attendanceRecords.id, { onDelete: 'cascade' }),
//   oldStatus: text('old_status', { enum: ['H', 'I', 'S', 'A'] }),
//   newStatus: text('new_status', { enum: ['H', 'I', 'S', 'A'] }).notNull(),
//   oldNotes: text('old_notes'),
//   newNotes: text('new_notes'),
//   reason: text('reason').notNull(),
//   changedBy: integer('changed_by').notNull().references(() => users.id),
//   createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`),
// });

// export type AttendanceAudit = typeof attendanceAudit.$inferSelect;
// export type NewAttendanceAudit = typeof attendanceAudit.$inferInsert;