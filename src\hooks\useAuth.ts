'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export interface User {
  id: number;
  email: string;
  name: string;
  role: 'teacher' | 'student';
  isActive: boolean;
  isSuperAdmin: boolean;
}

interface AuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
}

/**
 * Client-side authentication hook
 */
export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null,
  });
  const router = useRouter();

  const checkAuth = async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }));
      
      const response = await fetch('/api/auth/me', {
        method: 'GET',
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setAuthState({
            user: data.data.user,
            loading: false,
            error: null,
          });
        } else {
          setAuthState({
            user: null,
            loading: false,
            error: data.error?.message || 'Authentication failed',
          });
        }
      } else {
        setAuthState({
          user: null,
          loading: false,
          error: 'Not authenticated',
        });
      }
    } catch (error) {
      setAuthState({
        user: null,
        loading: false,
        error: error instanceof Error ? error.message : 'Authentication error',
      });
    }
  };

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
      
      setAuthState({
        user: null,
        loading: false,
        error: null,
      });
      
      router.push('/login');
      router.refresh();
    } catch (error) {
      console.error('Logout error:', error);
      // Force redirect even if logout API fails
      router.push('/login');
      router.refresh();
    }
  };

  const requireAuth = () => {
    if (!authState.loading && !authState.user) {
      router.push('/login');
    }
  };

  const requireRole = (allowedRoles: Array<'teacher' | 'student'>) => {
    if (!authState.loading && authState.user) {
      if (!allowedRoles.includes(authState.user.role)) {
        // Redirect to appropriate dashboard
        if (authState.user.role === 'teacher') {
          router.push('/guru');
        } else if (authState.user.role === 'student') {
          router.push('/siswa');
        } else {
          router.push('/login');
        }
      }
    } else if (!authState.loading && !authState.user) {
      router.push('/login');
    }
  };

  const hasPermission = (resource: string, action: 'read' | 'write' | 'delete'): boolean => {
    if (!authState.user) return false;
    
    const { user } = authState;
    
    switch (resource) {
      case 'classroom':
        return user.role === 'teacher';
      
      case 'student':
        if (action === 'read') {
          return true;
        }
        return user.role === 'teacher';
      
      case 'attendance':
        if (action === 'read') {
          return true;
        }
        return user.role === 'teacher';
      
      case 'assignment':
        if (action === 'read') {
          return true;
        }
        return user.role === 'teacher';
      
      case 'grade':
        if (action === 'read') {
          return true;
        }
        return user.role === 'teacher';
      
      case 'school_settings':
        return user.role === 'teacher' && user.isSuperAdmin;
      
      default:
        return false;
    }
  };

  useEffect(() => {
    checkAuth();
  }, []);

  return {
    user: authState.user,
    loading: authState.loading,
    error: authState.error,
    logout,
    requireAuth,
    requireRole,
    hasPermission,
    refetch: checkAuth,
  };
}

/**
 * Hook for teacher-only components
 */
export function useRequireTeacher() {
  const auth = useAuth();
  
  useEffect(() => {
    auth.requireRole(['teacher']);
  }, [auth.user, auth.loading]);
  
  return auth;
}

/**
 * Hook for student-only components
 */
export function useRequireStudent() {
  const auth = useAuth();
  
  useEffect(() => {
    auth.requireRole(['student']);
  }, [auth.user, auth.loading]);
  
  return auth;
}

/**
 * Hook for authenticated components (any role)
 */
export function useRequireAuth() {
  const auth = useAuth();
  
  useEffect(() => {
    auth.requireAuth();
  }, [auth.user, auth.loading]);
  
  return auth;
}