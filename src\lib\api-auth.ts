import { NextRequest, NextResponse } from 'next/server';
import { 
  requireAuthAPI, 
  requireRoleAPI, 
  requireTeacherAP<PERSON>, 
  requireStudentAPI, 
  requireSuperAdminAPI,
  requirePermissionAPI,
  type AuthenticatedUser 
} from './auth-guards';

/**
 * Standard error responses for API routes
 */
export const API_ERRORS = {
  UNAUTHORIZED: {
    success: false,
    error: {
      code: 'UNAUTHORIZED',
      message: 'Authentication required',
    },
  },
  FORBIDDEN: {
    success: false,
    error: {
      code: 'FORBIDDEN',
      message: 'Insufficient permissions',
    },
  },
  INTERNAL_ERROR: {
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: 'Internal server error',
    },
  },
} as const;

/**
 * Wrapper for API routes that require authentication
 */
export function withAuth<T extends any[]>(
  handler: (request: NextRequest, user: AuthenticatedUser, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const user = await requireAuthAPI();
      return await handler(request, user, ...args);
    } catch (error) {
      console.error('API Auth error:', error);
      
      if (error instanceof Error) {
        if (error.message === 'Authentication required') {
          return NextResponse.json(API_ERRORS.UNAUTHORIZED, { status: 401 });
        }
        if (error.message === 'Insufficient permissions') {
          return NextResponse.json(API_ERRORS.FORBIDDEN, { status: 403 });
        }
      }
      
      return NextResponse.json(API_ERRORS.INTERNAL_ERROR, { status: 500 });
    }
  };
}

/**
 * Wrapper for API routes that require specific roles
 */
export function withRole<T extends any[]>(
  allowedRoles: Array<'teacher' | 'student'>,
  handler: (request: NextRequest, user: AuthenticatedUser, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const user = await requireRoleAPI(allowedRoles);
      return await handler(request, user, ...args);
    } catch (error) {
      console.error('API Role error:', error);
      
      if (error instanceof Error) {
        if (error.message === 'Authentication required') {
          return NextResponse.json(API_ERRORS.UNAUTHORIZED, { status: 401 });
        }
        if (error.message === 'Insufficient permissions') {
          return NextResponse.json(API_ERRORS.FORBIDDEN, { status: 403 });
        }
      }
      
      return NextResponse.json(API_ERRORS.INTERNAL_ERROR, { status: 500 });
    }
  };
}

/**
 * Wrapper for teacher-only API routes
 */
export function withTeacher<T extends any[]>(
  handler: (request: NextRequest, user: AuthenticatedUser, ...args: T) => Promise<NextResponse>
) {
  return withRole(['teacher'], handler);
}

/**
 * Wrapper for student-only API routes
 */
export function withStudent<T extends any[]>(
  handler: (request: NextRequest, user: AuthenticatedUser, ...args: T) => Promise<NextResponse>
) {
  return withRole(['student'], handler);
}

/**
 * Wrapper for superadmin-only API routes
 */
export function withSuperAdmin<T extends any[]>(
  handler: (request: NextRequest, user: AuthenticatedUser, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const user = await requireSuperAdminAPI();
      return await handler(request, user, ...args);
    } catch (error) {
      console.error('API SuperAdmin error:', error);
      
      if (error instanceof Error) {
        if (error.message === 'Authentication required') {
          return NextResponse.json(API_ERRORS.UNAUTHORIZED, { status: 401 });
        }
        if (error.message.includes('Superadmin') || error.message === 'Insufficient permissions') {
          return NextResponse.json(API_ERRORS.FORBIDDEN, { status: 403 });
        }
      }
      
      return NextResponse.json(API_ERRORS.INTERNAL_ERROR, { status: 500 });
    }
  };
}

/**
 * Wrapper for API routes that require specific permissions
 */
export function withPermission<T extends any[]>(
  resource: string,
  action: 'read' | 'write' | 'delete',
  handler: (request: NextRequest, user: AuthenticatedUser, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const user = await requirePermissionAPI(resource, action);
      return await handler(request, user, ...args);
    } catch (error) {
      console.error('API Permission error:', error);
      
      if (error instanceof Error) {
        if (error.message === 'Authentication required') {
          return NextResponse.json(API_ERRORS.UNAUTHORIZED, { status: 401 });
        }
        if (error.message.includes('Permission denied') || error.message === 'Insufficient permissions') {
          return NextResponse.json(API_ERRORS.FORBIDDEN, { status: 403 });
        }
      }
      
      return NextResponse.json(API_ERRORS.INTERNAL_ERROR, { status: 500 });
    }
  };
}

/**
 * Helper to create success responses
 */
export function createSuccessResponse<T>(data: T, status = 200): NextResponse {
  return NextResponse.json({
    success: true,
    data,
  }, { status });
}

/**
 * Helper to create error responses
 */
export function createErrorResponse(
  code: string,
  message: string,
  status = 400,
  details?: any
): NextResponse {
  return NextResponse.json({
    success: false,
    error: {
      code,
      message,
      details,
    },
  }, { status });
}