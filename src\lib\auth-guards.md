# Authentication Guards Documentation

This document explains how to use the authentication and authorization system in GuruFlow.

## Server-Side Guards (Pages)

### Basic Authentication
```typescript
import { requireAuth } from '@/lib/auth-guards';

export default async function ProtectedPage() {
  const user = await requireAuth(); // Redirects to login if not authenticated
  
  return <div>Welcome, {user.name}!</div>;
}
```

### Role-Based Authentication
```typescript
import { requireTeacher, requireStudent } from '@/lib/auth-guards';

// Teacher only page
export default async function TeacherPage() {
  const user = await requireTeacher(); // Redirects if not teacher
  
  return <div>Teacher Dashboard</div>;
}

// Student only page
export default async function StudentPage() {
  const user = await requireStudent(); // Redirects if not student
  
  return <div>Student Dashboard</div>;
}
```

### Superadmin Authentication
```typescript
import { requireSuperAdmin } from '@/lib/auth-guards';

export default async function AdminPage() {
  const user = await requireSuperAdmin(); // Requires teacher + superadmin
  
  return <div>Admin Settings</div>;
}
```

## API Route Guards

### Basic API Authentication
```typescript
import { withAuth } from '@/lib/api-auth';

export const GET = withAuth(async (request, user) => {
  // user is guaranteed to be authenticated
  return NextResponse.json({ message: `Hello ${user.name}` });
});
```

### Role-Based API Guards
```typescript
import { withTeacher, withStudent } from '@/lib/api-auth';

// Teacher only endpoint
export const POST = withTeacher(async (request, user) => {
  // Only teachers can access this
  return NextResponse.json({ success: true });
});

// Student only endpoint
export const GET = withStudent(async (request, user) => {
  // Only students can access this
  return NextResponse.json({ data: [] });
});
```

### Permission-Based API Guards
```typescript
import { withPermission } from '@/lib/api-auth';

// Requires 'read' permission on 'attendance' resource
export const GET = withPermission('attendance', 'read', async (request, user) => {
  // Both teachers and students can read attendance
  return NextResponse.json({ records: [] });
});

// Requires 'write' permission on 'attendance' resource
export const POST = withPermission('attendance', 'write', async (request, user) => {
  // Only teachers can write attendance
  return NextResponse.json({ success: true });
});
```

### Superadmin API Guards
```typescript
import { withSuperAdmin } from '@/lib/api-auth';

export const PUT = withSuperAdmin(async (request, user) => {
  // Only superadmin teachers can access this
  return NextResponse.json({ updated: true });
});
```

## Client-Side Guards (React Components)

### Using Auth Hooks
```typescript
import { useAuth, useRequireTeacher } from '@/hooks/useAuth';

function MyComponent() {
  const { user, loading, hasPermission } = useAuth();
  
  if (loading) return <div>Loading...</div>;
  if (!user) return <div>Please login</div>;
  
  return (
    <div>
      <h1>Welcome, {user.name}</h1>
      {hasPermission('classroom', 'write') && (
        <button>Create Classroom</button>
      )}
    </div>
  );
}

// Teacher-only component
function TeacherComponent() {
  const { user, loading } = useRequireTeacher();
  
  if (loading) return <div>Loading...</div>;
  
  return <div>Teacher content</div>;
}
```

### Using Auth Guard Components
```typescript
import { AuthGuard, TeacherGuard, SuperAdminGuard } from '@/components/auth/AuthGuard';

function App() {
  return (
    <AuthGuard>
      <div>This content requires authentication</div>
    </AuthGuard>
  );
}

function TeacherApp() {
  return (
    <TeacherGuard>
      <div>This content is for teachers only</div>
    </TeacherGuard>
  );
}

function AdminApp() {
  return (
    <SuperAdminGuard>
      <div>This content is for superadmins only</div>
    </SuperAdminGuard>
  );
}
```

## Permission System

The permission system is resource and action based:

### Resources
- `classroom` - Classroom management
- `student` - Student management
- `attendance` - Attendance tracking
- `assignment` - Assignment management
- `grade` - Grade management
- `school_settings` - School configuration

### Actions
- `read` - View/fetch data
- `write` - Create/update data
- `delete` - Remove data

### Permission Matrix

| Resource | Teacher Read | Teacher Write | Student Read | Student Write |
|----------|--------------|---------------|--------------|---------------|
| classroom | ✅ | ✅ | ❌ | ❌ |
| student | ✅ | ✅ | ✅ | ❌ |
| attendance | ✅ | ✅ | ✅ | ❌ |
| assignment | ✅ | ✅ | ✅ | ❌ |
| grade | ✅ | ✅ | ✅ | ❌ |
| school_settings | ✅ (superadmin) | ✅ (superadmin) | ❌ | ❌ |

## Error Handling

All auth guards return appropriate HTTP status codes:

- `401 Unauthorized` - Not authenticated
- `403 Forbidden` - Insufficient permissions
- `500 Internal Server Error` - Server error

API responses follow this format:
```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Authentication required"
  }
}
```

## Best Practices

1. **Always use server-side guards for pages** - Client-side guards are for UX only
2. **Use permission-based guards for flexible access control**
3. **Combine multiple guards when needed**
4. **Handle loading states properly in client components**
5. **Provide meaningful error messages**
6. **Test all authentication scenarios**