import { redirect } from 'next/navigation';
import { getCurrentUser, type AuthenticatedUser } from './auth';

/**
 * Server-side authentication guard for pages
 * Redirects to login if not authenticated
 */
export async function requireAuth(): Promise<AuthenticatedUser> {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect('/login');
  }
  
  return user;
}

/**
 * Server-side role-based authentication guard
 * Redirects to login if not authenticated or wrong role
 */
export async function requireRole(allowedRoles: Array<'teacher' | 'student'>): Promise<AuthenticatedUser> {
  const user = await requireAuth();
  
  if (!allowedRoles.includes(user.role)) {
    // Redirect to appropriate dashboard based on actual role
    if (user.role === 'teacher') {
      redirect('/guru');
    } else if (user.role === 'student') {
      redirect('/siswa');
    } else {
      redirect('/login');
    }
  }
  
  return user;
}

/**
 * Server-side teacher-only authentication guard
 */
export async function requireTeacher(): Promise<AuthenticatedUser> {
  return requireRole(['teacher']);
}

/**
 * Server-side student-only authentication guard
 */
export async function requireStudent(): Promise<AuthenticatedUser> {
  return requireRole(['student']);
}

/**
 * Server-side superadmin authentication guard
 */
export async function requireSuperAdmin(): Promise<AuthenticatedUser> {
  const user = await requireTeacher();
  
  if (!user.isSuperAdmin) {
    redirect('/guru');
  }
  
  return user;
}

/**
 * API route authentication guard
 * Returns user or throws error for API responses
 */
export async function requireAuthAPI(): Promise<AuthenticatedUser> {
  const user = await getCurrentUser();
  
  if (!user) {
    throw new Error('Authentication required');
  }
  
  return user;
}

/**
 * API route role-based authentication guard
 */
export async function requireRoleAPI(allowedRoles: Array<'teacher' | 'student'>): Promise<AuthenticatedUser> {
  const user = await requireAuthAPI();
  
  if (!allowedRoles.includes(user.role)) {
    throw new Error('Insufficient permissions');
  }
  
  return user;
}

/**
 * API route teacher-only authentication guard
 */
export async function requireTeacherAPI(): Promise<AuthenticatedUser> {
  return requireRoleAPI(['teacher']);
}

/**
 * API route student-only authentication guard
 */
export async function requireStudentAPI(): Promise<AuthenticatedUser> {
  return requireRoleAPI(['student']);
}

/**
 * API route superadmin authentication guard
 */
export async function requireSuperAdminAPI(): Promise<AuthenticatedUser> {
  const user = await requireTeacherAPI();
  
  if (!user.isSuperAdmin) {
    throw new Error('Superadmin access required');
  }
  
  return user;
}

/**
 * Check if user has permission to access resource
 */
export function hasPermission(
  user: AuthenticatedUser,
  resource: string,
  action: 'read' | 'write' | 'delete'
): boolean {
  // Basic permission logic - can be extended
  switch (resource) {
    case 'classroom':
      return user.role === 'teacher';
    
    case 'student':
      if (action === 'read') {
        return true; // Both teachers and students can read student data
      }
      return user.role === 'teacher';
    
    case 'attendance':
      if (action === 'read') {
        return true; // Both can read attendance
      }
      return user.role === 'teacher'; // Only teachers can write/delete
    
    case 'assignment':
      if (action === 'read') {
        return true; // Both can read assignments
      }
      return user.role === 'teacher'; // Only teachers can create/modify
    
    case 'grade':
      if (action === 'read') {
        return true; // Both can read grades
      }
      return user.role === 'teacher'; // Only teachers can grade
    
    case 'school_settings':
      return user.role === 'teacher' && user.isSuperAdmin;
    
    default:
      return false;
  }
}

/**
 * API route permission guard
 */
export async function requirePermissionAPI(
  resource: string,
  action: 'read' | 'write' | 'delete'
): Promise<AuthenticatedUser> {
  const user = await requireAuthAPI();
  
  if (!hasPermission(user, resource, action)) {
    throw new Error(`Permission denied for ${action} on ${resource}`);
  }
  
  return user;
}