import bcrypt from 'bcryptjs';
import { cookies } from 'next/headers';
import { db } from './db';
import { sessions, users } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { generateId } from './utils';

// Password hashing utilities
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Session management
export interface SessionData {
  id: string;
  userId: number;
  expiresAt: Date;
}

export async function createSession(userId: number): Promise<string> {
  const sessionId = generateId();
  const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
  
  await db.insert(sessions).values({
    id: sessionId,
    userId,
    expiresAt: expiresAt.toISOString(),
  });
  
  return sessionId;
}

export async function validateSession(sessionId: string): Promise<SessionData | null> {
  const session = await db
    .select()
    .from(sessions)
    .where(eq(sessions.id, sessionId))
    .limit(1);
    
  if (session.length === 0) {
    return null;
  }
  
  const sessionData = session[0];
  const expiresAt = new Date(sessionData.expiresAt);
  
  // Check if session is expired
  if (expiresAt < new Date()) {
    await deleteSession(sessionId);
    return null;
  }
  
  return {
    id: sessionData.id,
    userId: sessionData.userId,
    expiresAt,
  };
}

export async function deleteSession(sessionId: string): Promise<void> {
  await db.delete(sessions).where(eq(sessions.id, sessionId));
}

export async function deleteAllUserSessions(userId: number): Promise<void> {
  await db.delete(sessions).where(eq(sessions.userId, userId));
}

// Cookie management utilities
const SESSION_COOKIE_NAME = 'guruflow-session';

export async function setSessionCookie(sessionId: string): Promise<void> {
  const cookieStore = await cookies();
  const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
  
  cookieStore.set(SESSION_COOKIE_NAME, sessionId, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    expires: expiresAt,
    path: '/',
  });
}

export async function getSessionCookie(): Promise<string | null> {
  const cookieStore = await cookies();
  const sessionCookie = cookieStore.get(SESSION_COOKIE_NAME);
  return sessionCookie?.value || null;
}

export async function deleteSessionCookie(): Promise<void> {
  const cookieStore = await cookies();
  cookieStore.delete(SESSION_COOKIE_NAME);
}

// User authentication utilities
export interface AuthenticatedUser {
  id: number;
  email: string;
  name: string;
  role: 'teacher' | 'student';
  isActive: boolean;
  isSuperAdmin: boolean;
}

export async function getCurrentUser(): Promise<AuthenticatedUser | null> {
  const sessionId = await getSessionCookie();
  if (!sessionId) {
    return null;
  }
  
  const session = await validateSession(sessionId);
  if (!session) {
    return null;
  }
  
  const user = await db
    .select({
      id: users.id,
      email: users.email,
      name: users.name,
      role: users.role,
      isActive: users.isActive,
      isSuperAdmin: users.isSuperAdmin,
    })
    .from(users)
    .where(eq(users.id, session.userId))
    .limit(1);
    
  if (user.length === 0 || !user[0].isActive) {
    await deleteSession(sessionId);
    return null;
  }
  
  return user[0] as AuthenticatedUser;
}

export async function authenticateUser(email: string, password: string): Promise<AuthenticatedUser | null> {
  const user = await db
    .select()
    .from(users)
    .where(eq(users.email, email.toLowerCase()))
    .limit(1);
    
  if (user.length === 0 || !user[0].isActive) {
    return null;
  }
  
  const userData = user[0];
  const isValidPassword = await verifyPassword(password, userData.passwordHash);
  
  if (!isValidPassword) {
    return null;
  }
  
  return {
    id: userData.id,
    email: userData.email,
    name: userData.name,
    role: userData.role as 'teacher' | 'student',
    isActive: userData.isActive,
    isSuperAdmin: userData.isSuperAdmin,
  };
}

// Session cleanup utility
export async function cleanupExpiredSessions(): Promise<void> {
  const now = new Date().toISOString();
  await db.delete(sessions).where(eq(sessions.expiresAt, now));
}