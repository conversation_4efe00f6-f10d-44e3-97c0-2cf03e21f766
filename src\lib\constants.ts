/**
 * Application constants for GuruFlow
 */

// Indonesian attendance codes
export const ATTENDANCE_CODES = {
  H: 'Had<PERSON>',
  I: 'Izin', 
  S: 'Sakit',
  A: '<PERSON><PERSON>'
} as const;

export type AttendanceCode = keyof typeof ATTENDANCE_CODES;

// School levels in Indonesia
export const SCHOOL_LEVELS = {
  SD: 'Seko<PERSON>',
  SMP: 'Sekolah Menengah Pertama', 
  SMA: 'Sekolah Menengah Atas',
  SMK: 'Seko<PERSON> Menengah Kejuruan'
} as const;

export type SchoolLevel = keyof typeof SCHOOL_LEVELS;

// User roles
export const USER_ROLES = {
  teacher: '<PERSON>',
  student: '<PERSON><PERSON><PERSON>'
} as const;

export type UserRole = keyof typeof USER_ROLES;

// Grade types for assessments
export const GRADE_TYPES = {
  assignment: 'Tugas',
  quiz: 'Kuis',
  exam: 'Ujian',
  project: 'Proyek'
} as const;

export type GradeType = keyof typeof GRADE_TYPES;

// XP reward reasons
export const XP_REASONS = {
  attendance: '<PERSON><PERSON><PERSON><PERSON>',
  assignment_completion: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  good_grade: '<PERSON><PERSON>',
  perfect_attendance: 'Kehadiran <PERSON>',
  early_submission: 'Pengumpulan Tepat Waktu',
  class_participation: 'Partisipasi Kelas'
} as const;

export type XpReason = keyof typeof XP_REASONS;

// Default XP values
export const DEFAULT_XP_VALUES = {
  attendance: 5,
  assignment_completion: 10,
  good_grade: 15,
  perfect_attendance: 50,
  early_submission: 5,
  class_participation: 3
} as const;

// Indonesian grade scale (common in schools)
export const GRADE_SCALE = {
  A: { min: 90, max: 100, description: 'Sangat Baik' },
  B: { min: 80, max: 89, description: 'Baik' },
  C: { min: 70, max: 79, description: 'Cukup' },
  D: { min: 60, max: 69, description: 'Kurang' },
  E: { min: 0, max: 59, description: 'Sangat Kurang' }
} as const;

// Common Indonesian subjects
export const COMMON_SUBJECTS = [
  'Bahasa Indonesia',
  'Matematika',
  'IPA (Ilmu Pengetahuan Alam)',
  'IPS (Ilmu Pengetahuan Sosial)',
  'Bahasa Inggris',
  'Pendidikan Agama',
  'Pendidikan Kewarganegaraan',
  'Seni Budaya',
  'Pendidikan Jasmani',
  'Prakarya',
  'Fisika',
  'Kimia',
  'Biologi',
  'Sejarah',
  'Geografi',
  'Ekonomi',
  'Sosiologi'
] as const;

// File upload limits
export const FILE_UPLOAD_LIMITS = {
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: [
    'image/jpeg',
    'image/png', 
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ]
} as const;

// Pagination defaults
export const PAGINATION = {
  defaultPageSize: 20,
  maxPageSize: 100,
  pageSizeOptions: [10, 20, 50, 100]
} as const;

// Date formats
export const DATE_FORMATS = {
  display: 'dd MMMM yyyy',
  input: 'yyyy-MM-dd',
  api: 'yyyy-MM-dd HH:mm:ss',
  indonesian: 'dd/MM/yyyy'
} as const;

// Session configuration
export const SESSION_CONFIG = {
  maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
  cookieName: 'guruflow-session',
  secureCookie: process.env.NODE_ENV === 'production'
} as const;

// API response codes
export const API_RESPONSE_CODES = {
  SUCCESS: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  VALIDATION_ERROR: 422,
  INTERNAL_ERROR: 500
} as const;

// Validation rules
export const VALIDATION_RULES = {
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false
  },
  studentId: {
    minLength: 6,
    maxLength: 10,
    pattern: /^\d+$/
  },
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  name: {
    minLength: 2,
    maxLength: 100,
    pattern: /^[a-zA-Z\s.'-]+$/
  }
} as const;

// Indonesian months
export const INDONESIAN_MONTHS = [
  'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
  'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
] as const;

// Indonesian days
export const INDONESIAN_DAYS = [
  'Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'
] as const;

// Default badge configurations
export const DEFAULT_BADGES = [
  {
    name: 'Rajin Hadir',
    description: 'Hadir 30 hari berturut-turut',
    icon: 'attendance-streak',
    xpReward: 100
  },
  {
    name: 'Siswa Teladan', 
    description: 'Mendapat nilai A dalam 5 mata pelajaran',
    icon: 'excellent-student',
    xpReward: 200
  },
  {
    name: 'Pengumpul Tugas',
    description: 'Mengumpulkan 10 tugas tepat waktu',
    icon: 'assignment-master',
    xpReward: 150
  }
] as const;

// Error messages in Indonesian
export const ERROR_MESSAGES = {
  REQUIRED_FIELD: 'Field ini wajib diisi',
  INVALID_EMAIL: 'Format email tidak valid',
  INVALID_PASSWORD: 'Password minimal 8 karakter',
  INVALID_STUDENT_ID: 'NIS harus berupa angka 6-10 digit',
  UNAUTHORIZED: 'Anda tidak memiliki akses',
  NOT_FOUND: 'Data tidak ditemukan',
  DUPLICATE_EMAIL: 'Email sudah terdaftar',
  DUPLICATE_STUDENT_ID: 'NIS sudah terdaftar',
  NETWORK_ERROR: 'Terjadi kesalahan jaringan',
  SERVER_ERROR: 'Terjadi kesalahan server'
} as const;

// Success messages in Indonesian
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Berhasil masuk',
  LOGOUT_SUCCESS: 'Berhasil keluar',
  SAVE_SUCCESS: 'Data berhasil disimpan',
  UPDATE_SUCCESS: 'Data berhasil diperbarui',
  DELETE_SUCCESS: 'Data berhasil dihapus',
  UPLOAD_SUCCESS: 'File berhasil diunggah',
  EMAIL_SENT: 'Email berhasil dikirim'
} as const;