// CSV Export utilities for Indonesian text support

export interface CSVColumn {
  key: string;
  header: string;
  formatter?: (value: any) => string;
}

export interface CSVExportOptions {
  filename: string;
  columns: CSVColumn[];
  data: Record<string, any>[];
  includeTimestamp?: boolean;
  encoding?: 'utf-8' | 'utf-8-bom';
}

// Format date for Indonesian locale
export function formatDateForCSV(date: string | Date): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('id-ID', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
}

// Format datetime for Indonesian locale
export function formatDateTimeForCSV(date: string | Date): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleString('id-ID', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

// Escape CSV field (handle commas, quotes, newlines)
function escapeCSVField(field: string): string {
  if (field == null) return '';
  
  const stringField = String(field);
  
  // If field contains comma, quote, or newline, wrap in quotes and escape internal quotes
  if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n') || stringField.includes('\r')) {
    return `"${stringField.replace(/"/g, '""')}"`;
  }
  
  return stringField;
}

// Generate CSV content from data
export function generateCSVContent(options: CSVExportOptions): string {
  const { columns, data, includeTimestamp = false } = options;
  
  const lines: string[] = [];
  
  // Add timestamp header if requested
  if (includeTimestamp) {
    lines.push(`# Exported on ${formatDateTimeForCSV(new Date())}`);
    lines.push('');
  }
  
  // Add header row
  const headers = columns.map(col => escapeCSVField(col.header));
  lines.push(headers.join(','));
  
  // Add data rows
  data.forEach(row => {
    const values = columns.map(col => {
      const value = row[col.key];
      const formattedValue = col.formatter ? col.formatter(value) : value;
      return escapeCSVField(formattedValue);
    });
    lines.push(values.join(','));
  });
  
  return lines.join('\n');
}

// Create CSV blob with proper encoding for Indonesian text
export function createCSVBlob(content: string, encoding: 'utf-8' | 'utf-8-bom' = 'utf-8-bom'): Blob {
  if (encoding === 'utf-8-bom') {
    // Add BOM for proper Excel support with Indonesian characters
    const BOM = '\uFEFF';
    return new Blob([BOM + content], { type: 'text/csv;charset=utf-8' });
  }
  
  return new Blob([content], { type: 'text/csv;charset=utf-8' });
}

// Download CSV file
export function downloadCSV(options: CSVExportOptions): void {
  const content = generateCSVContent(options);
  const blob = createCSVBlob(content, options.encoding);
  
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = options.filename;
  
  // Trigger download
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up
  URL.revokeObjectURL(url);
}

// Predefined column configurations for common exports

export const attendanceCSVColumns: CSVColumn[] = [
  { key: 'date', header: 'Tanggal', formatter: formatDateForCSV },
  { key: 'studentName', header: 'Nama Siswa' },
  { key: 'studentId', header: 'NIS' },
  { key: 'className', header: 'Kelas' },
  { key: 'subjectName', header: 'Mata Pelajaran' },
  { key: 'status', header: 'Status Kehadiran' },
  { key: 'notes', header: 'Catatan' },
  { key: 'markedAt', header: 'Waktu Dicatat', formatter: formatDateTimeForCSV },
];

export const studentListCSVColumns: CSVColumn[] = [
  { key: 'studentId', header: 'NIS' },
  { key: 'name', header: 'Nama Lengkap' },
  { key: 'email', header: 'Email' },
  { key: 'className', header: 'Kelas' },
  { key: 'grade', header: 'Tingkat' },
  { key: 'isActive', header: 'Status', formatter: (value) => value ? 'Aktif' : 'Tidak Aktif' },
  { key: 'createdAt', header: 'Tanggal Daftar', formatter: formatDateForCSV },
];

export const attendanceSummaryCSVColumns: CSVColumn[] = [
  { key: 'studentName', header: 'Nama Siswa' },
  { key: 'studentId', header: 'NIS' },
  { key: 'className', header: 'Kelas' },
  { key: 'totalDays', header: 'Total Hari' },
  { key: 'presentDays', header: 'Hadir (H)' },
  { key: 'excusedDays', header: 'Izin (I)' },
  { key: 'sickDays', header: 'Sakit (S)' },
  { key: 'absentDays', header: 'Alpa (A)' },
  { key: 'attendanceRate', header: 'Persentase Kehadiran (%)', formatter: (value) => `${value.toFixed(1)}%` },
];

export const classroomCSVColumns: CSVColumn[] = [
  { key: 'name', header: 'Nama Kelas' },
  { key: 'grade', header: 'Tingkat' },
  { key: 'academicYear', header: 'Tahun Ajaran' },
  { key: 'homeroomTeacher', header: 'Wali Kelas' },
  { key: 'studentCount', header: 'Jumlah Siswa' },
  { key: 'isActive', header: 'Status', formatter: (value) => value ? 'Aktif' : 'Tidak Aktif' },
  { key: 'createdAt', header: 'Tanggal Dibuat', formatter: formatDateForCSV },
];

export const subjectCSVColumns: CSVColumn[] = [
  { key: 'name', header: 'Nama Mata Pelajaran' },
  { key: 'code', header: 'Kode' },
  { key: 'description', header: 'Deskripsi' },
  { key: 'isActive', header: 'Status', formatter: (value) => value ? 'Aktif' : 'Tidak Aktif' },
  { key: 'createdAt', header: 'Tanggal Dibuat', formatter: formatDateForCSV },
];

// Utility functions for specific export types

export function exportAttendanceData(data: any[], filename?: string) {
  const defaultFilename = `Absensi_${formatDateForCSV(new Date()).replace(/\//g, '-')}.csv`;
  
  downloadCSV({
    filename: filename || defaultFilename,
    columns: attendanceCSVColumns,
    data,
    includeTimestamp: true,
  });
}

export function exportStudentList(data: any[], filename?: string) {
  const defaultFilename = `Daftar_Siswa_${formatDateForCSV(new Date()).replace(/\//g, '-')}.csv`;
  
  downloadCSV({
    filename: filename || defaultFilename,
    columns: studentListCSVColumns,
    data,
    includeTimestamp: true,
  });
}

export function exportAttendanceSummary(data: any[], filename?: string) {
  const defaultFilename = `Ringkasan_Absensi_${formatDateForCSV(new Date()).replace(/\//g, '-')}.csv`;
  
  downloadCSV({
    filename: filename || defaultFilename,
    columns: attendanceSummaryCSVColumns,
    data,
    includeTimestamp: true,
  });
}

export function exportClassroomList(data: any[], filename?: string) {
  const defaultFilename = `Daftar_Kelas_${formatDateForCSV(new Date()).replace(/\//g, '-')}.csv`;
  
  downloadCSV({
    filename: filename || defaultFilename,
    columns: classroomCSVColumns,
    data,
    includeTimestamp: true,
  });
}

export function exportSubjectList(data: any[], filename?: string) {
  const defaultFilename = `Daftar_Mata_Pelajaran_${formatDateForCSV(new Date()).replace(/\//g, '-')}.csv`;
  
  downloadCSV({
    filename: filename || defaultFilename,
    columns: subjectCSVColumns,
    data,
    includeTimestamp: true,
  });
}

// Server-side CSV generation (for API routes)
export function generateCSVResponse(options: CSVExportOptions): Response {
  const content = generateCSVContent(options);
  const blob = createCSVBlob(content, options.encoding);
  
  return new Response(blob, {
    headers: {
      'Content-Type': 'text/csv;charset=utf-8',
      'Content-Disposition': `attachment; filename="${encodeURIComponent(options.filename)}"`,
      'Content-Length': blob.size.toString(),
    },
  });
}

// Bulk export utilities
export interface BulkExportOptions {
  exports: Array<{
    name: string;
    filename: string;
    columns: CSVColumn[];
    data: Record<string, any>[];
  }>;
  zipFilename: string;
}

// Note: For bulk export as ZIP, you would need to add a ZIP library like JSZip
// This is a placeholder for the interface
export async function exportBulkCSV(options: BulkExportOptions): Promise<void> {
  // Implementation would require JSZip or similar library
  // For now, we'll export each file individually
  options.exports.forEach(exportData => {
    downloadCSV({
      filename: exportData.filename,
      columns: exportData.columns,
      data: exportData.data,
      includeTimestamp: true,
    });
  });
}