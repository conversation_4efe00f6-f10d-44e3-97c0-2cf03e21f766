import { connectToDatabase, checkDatabaseHealth } from './db';
import { runMigrations, initializeDatabase } from './migrate';
import { seedDatabase } from './seed';

/**
 * Complete database setup utility
 * This function handles the full database initialization process
 */
export async function setupDatabase() {
  try {
    console.log('🚀 Starting database setup...');

    // Step 1: Test database connection
    await connectToDatabase();

    // Step 2: Run migrations
    await runMigrations();

    // Step 3: Seed initial data
    await seedDatabase();

    // Step 4: Final health check
    const isHealthy = await checkDatabaseHealth();
    if (!isHealthy) {
      throw new Error('Database health check failed after setup');
    }

    console.log('✅ Database setup completed successfully');
    return true;

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    throw error;
  }
}

/**
 * Database maintenance utilities
 */
export const DatabaseMaintenance = {
  /**
   * Check if database is ready for use
   */
  async isReady(): Promise<boolean> {
    try {
      return await checkDatabaseHealth();
    } catch {
      return false;
    }
  },

  /**
   * Get database status information
   */
  async getStatus() {
    try {
      const isHealthy = await checkDatabaseHealth();
      return {
        healthy: isHealthy,
        timestamp: new Date().toISOString(),
        status: isHealthy ? 'connected' : 'disconnected'
      };
    } catch (error) {
      return {
        healthy: false,
        timestamp: new Date().toISOString(),
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  /**
   * Perform database maintenance tasks
   */
  async performMaintenance() {
    try {
      console.log('🔧 Performing database maintenance...');
      
      // Check health
      const isHealthy = await checkDatabaseHealth();
      if (!isHealthy) {
        throw new Error('Database is not healthy');
      }

      // Additional maintenance tasks can be added here
      // - Cleanup old sessions
      // - Archive old attendance records
      // - Update statistics
      
      console.log('✅ Database maintenance completed');
      return true;
    } catch (error) {
      console.error('❌ Database maintenance failed:', error);
      return false;
    }
  }
};

/**
 * Error handling utilities for database operations
 */
export class DatabaseError extends Error {
  constructor(
    message: string,
    public code?: string,
    public originalError?: unknown
  ) {
    super(message);
    this.name = 'DatabaseError';
  }
}

/**
 * Wrap database operations with error handling
 */
export function withDatabaseErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      if (error instanceof Error) {
        throw new DatabaseError(
          `Database operation failed: ${error.message}`,
          'DB_OPERATION_ERROR',
          error
        );
      }
      throw new DatabaseError(
        'Unknown database error occurred',
        'DB_UNKNOWN_ERROR',
        error
      );
    }
  };
}

export default {
  setupDatabase,
  DatabaseMaintenance,
  DatabaseError,
  withDatabaseErrorHandling
};