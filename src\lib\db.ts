import { drizzle } from 'drizzle-orm/libsql';
import { createClient } from '@libsql/client';
import * as schema from '../db/schema';

// Create the database client
const client = createClient({
  url: process.env.DATABASE_URL!,
  authToken: process.env.DATABASE_AUTH_TOKEN!,
});

// Create the Drizzle database instance
export const db = drizzle(client, { schema });

// Database connection utility with error handling
export async function connectToDatabase() {
  try {
    // Test the connection
    await client.execute('SELECT 1');
    console.log('✅ Database connected successfully');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw new Error('Failed to connect to database');
  }
}

// Graceful database disconnection
export async function disconnectFromDatabase() {
  try {
    client.close();
    console.log('✅ Database disconnected successfully');
  } catch (error) {
    console.error('❌ Database disconnection failed:', error);
  }
}

// Database health check utility
export async function checkDatabaseHealth() {
  try {
    const result = await client.execute('SELECT 1 as health');
    return result.rows.length > 0;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
}

export default db;