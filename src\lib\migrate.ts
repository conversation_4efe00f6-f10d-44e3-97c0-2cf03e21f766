import { migrate } from 'drizzle-orm/libsql/migrator';
import { db } from './db';

/**
 * Run database migrations
 * This function applies all pending migrations to the database
 */
export async function runMigrations() {
  try {
    console.log('🔄 Running database migrations...');
    
    await migrate(db, {
      migrationsFolder: './src/db/migrations',
    });
    
    console.log('✅ Database migrations completed successfully');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw new Error(`Migration failed: ${error}`);
  }
}

/**
 * Initialize database with migrations
 * This function is typically called during application startup
 */
export async function initializeDatabase() {
  try {
    console.log('🚀 Initializing database...');
    
    // Run migrations
    await runMigrations();
    
    console.log('✅ Database initialization completed');
    return true;
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    return false;
  }
}

// Export for use in other parts of the application
export default { runMigrations, initializeDatabase };