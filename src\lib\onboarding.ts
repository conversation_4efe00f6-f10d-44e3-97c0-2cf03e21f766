import { db } from './db';
import { schools } from '@/db/schema';

/**
 * Check if the system has been onboarded (school setup completed)
 */
export async function isSystemOnboarded(): Promise<boolean> {
  try {
    const existingSchools = await db.select().from(schools).limit(1);
    return existingSchools.length > 0;
  } catch (error) {
    console.error('Error checking onboarding status:', error);
    // If there's an error, assume not onboarded to be safe
    return false;
  }
}

/**
 * Get school information if onboarded
 */
export async function getSchoolInfo() {
  try {
    const schools_result = await db.select().from(schools).limit(1);
    return schools_result.length > 0 ? schools_result[0] : null;
  } catch (error) {
    console.error('Error getting school info:', error);
    return null;
  }
}