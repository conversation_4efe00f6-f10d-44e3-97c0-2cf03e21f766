import React from 'react';
import { Document, Page, Text, View, StyleSheet, Font, Image } from '@react-pdf/renderer';

// Register fonts for Indonesian language support
Font.register({
  family: 'Roboto',
  fonts: [
    {
      src: 'https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2',
      fontWeight: 'normal',
    },
    {
      src: 'https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfBBc4.woff2',
      fontWeight: 'bold',
    },
  ],
});

// PDF Styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontFamily: 'Roboto',
  },
  header: {
    flexDirection: 'row',
    marginBottom: 20,
    paddingBottom: 10,
    borderBottomWidth: 2,
    borderBottomColor: '#2563eb',
  },
  logo: {
    width: 60,
    height: 60,
    marginRight: 15,
  },
  headerText: {
    flex: 1,
  },
  schoolName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  reportTitle: {
    fontSize: 14,
    color: '#4b5563',
    marginBottom: 2,
  },
  reportDate: {
    fontSize: 12,
    color: '#6b7280',
  },
  section: {
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
    paddingBottom: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 10,
  },
  infoItem: {
    width: '50%',
    marginBottom: 4,
  },
  infoLabel: {
    fontSize: 10,
    color: '#6b7280',
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 12,
    color: '#1f2937',
    fontWeight: 'bold',
  },
  table: {
    display: 'table',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  tableRow: {
    margin: 'auto',
    flexDirection: 'row',
  },
  tableHeader: {
    backgroundColor: '#f3f4f6',
  },
  tableCol: {
    width: '20%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  tableColWide: {
    width: '30%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  tableColNarrow: {
    width: '10%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  tableCell: {
    margin: 'auto',
    marginTop: 5,
    marginBottom: 5,
    fontSize: 10,
    textAlign: 'center',
  },
  tableCellLeft: {
    margin: 5,
    fontSize: 10,
    textAlign: 'left',
  },
  tableCellHeader: {
    margin: 'auto',
    marginTop: 5,
    marginBottom: 5,
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  summary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    padding: 15,
    backgroundColor: '#f9fafb',
    borderRadius: 5,
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 10,
    color: '#6b7280',
    marginBottom: 2,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    color: '#6b7280',
    fontSize: 10,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    paddingTop: 10,
  },
  signature: {
    marginTop: 30,
    alignItems: 'flex-end',
  },
  signatureText: {
    fontSize: 12,
    color: '#1f2937',
    marginBottom: 40,
  },
  signatureName: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#1f2937',
    borderBottomWidth: 1,
    borderBottomColor: '#1f2937',
    paddingBottom: 2,
  },
});

// Attendance Status Labels
const statusLabels = {
  H: 'Hadir',
  I: 'Izin',
  S: 'Sakit',
  A: 'Alpa',
};

const statusColors = {
  H: '#10b981',
  I: '#f59e0b',
  S: '#ef4444',
  A: '#6b7280',
};

// Interface definitions
interface AttendanceReportData {
  school: {
    name: string;
    logo?: string;
    signature?: string;
  };
  classroom: {
    name: string;
    grade: string;
  };
  subject: {
    name: string;
  };
  teacher: {
    name: string;
  };
  period: {
    startDate: string;
    endDate: string;
  };
  students: Array<{
    id: number;
    name: string;
    studentId: string;
    attendanceRecords: Array<{
      date: string;
      status: 'H' | 'I' | 'S' | 'A';
      notes?: string;
    }>;
    summary: {
      H: number;
      I: number;
      S: number;
      A: number;
      total: number;
      percentage: number;
    };
  }>;
  summary: {
    totalStudents: number;
    totalDays: number;
    overallAttendanceRate: number;
    statusBreakdown: {
      H: number;
      I: number;
      S: number;
      A: number;
    };
  };
}

// Attendance Report PDF Component
export const AttendanceReportPDF: React.FC<{ data: AttendanceReportData }> = ({ data }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const formatDateShort = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
    });
  };

  // Get unique dates for table headers
  const uniqueDates = Array.from(
    new Set(
      data.students.flatMap(student => 
        student.attendanceRecords.map(record => record.date)
      )
    )
  ).sort();

  return (
    <Document>
      <Page size="A4" style={styles.page} orientation="landscape">
        {/* Header */}
        <View style={styles.header}>
          {data.school.logo && (
            <Image style={styles.logo} src={data.school.logo} />
          )}
          <View style={styles.headerText}>
            <Text style={styles.schoolName}>{data.school.name}</Text>
            <Text style={styles.reportTitle}>
              Laporan Absensi - {data.classroom.name} ({data.classroom.grade})
            </Text>
            <Text style={styles.reportDate}>
              Mata Pelajaran: {data.subject.name} | Guru: {data.teacher.name}
            </Text>
            <Text style={styles.reportDate}>
              Periode: {formatDate(data.period.startDate)} - {formatDate(data.period.endDate)}
            </Text>
          </View>
        </View>

        {/* Report Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informasi Laporan</Text>
          <View style={styles.infoGrid}>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Total Siswa</Text>
              <Text style={styles.infoValue}>{data.summary.totalStudents} siswa</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Total Hari</Text>
              <Text style={styles.infoValue}>{data.summary.totalDays} hari</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Tingkat Kehadiran</Text>
              <Text style={styles.infoValue}>{data.summary.overallAttendanceRate.toFixed(1)}%</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Tanggal Cetak</Text>
              <Text style={styles.infoValue}>{formatDate(new Date().toISOString())}</Text>
            </View>
          </View>
        </View>

        {/* Attendance Table */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Kehadiran</Text>
          <View style={styles.table}>
            {/* Table Header */}
            <View style={[styles.tableRow, styles.tableHeader]}>
              <View style={styles.tableColNarrow}>
                <Text style={styles.tableCellHeader}>No</Text>
              </View>
              <View style={styles.tableColWide}>
                <Text style={styles.tableCellHeader}>Nama Siswa</Text>
              </View>
              <View style={styles.tableColNarrow}>
                <Text style={styles.tableCellHeader}>NIS</Text>
              </View>
              {uniqueDates.map((date, index) => (
                <View key={index} style={styles.tableColNarrow}>
                  <Text style={styles.tableCellHeader}>{formatDateShort(date)}</Text>
                </View>
              ))}
              <View style={styles.tableColNarrow}>
                <Text style={styles.tableCellHeader}>H</Text>
              </View>
              <View style={styles.tableColNarrow}>
                <Text style={styles.tableCellHeader}>I</Text>
              </View>
              <View style={styles.tableColNarrow}>
                <Text style={styles.tableCellHeader}>S</Text>
              </View>
              <View style={styles.tableColNarrow}>
                <Text style={styles.tableCellHeader}>A</Text>
              </View>
              <View style={styles.tableColNarrow}>
                <Text style={styles.tableCellHeader}>%</Text>
              </View>
            </View>

            {/* Table Rows */}
            {data.students.map((student, index) => (
              <View key={student.id} style={styles.tableRow}>
                <View style={styles.tableColNarrow}>
                  <Text style={styles.tableCell}>{index + 1}</Text>
                </View>
                <View style={styles.tableColWide}>
                  <Text style={styles.tableCellLeft}>{student.name}</Text>
                </View>
                <View style={styles.tableColNarrow}>
                  <Text style={styles.tableCell}>{student.studentId}</Text>
                </View>
                {uniqueDates.map((date, dateIndex) => {
                  const record = student.attendanceRecords.find(r => r.date === date);
                  return (
                    <View key={dateIndex} style={styles.tableColNarrow}>
                      <Text style={styles.tableCell}>
                        {record ? record.status : '-'}
                      </Text>
                    </View>
                  );
                })}
                <View style={styles.tableColNarrow}>
                  <Text style={styles.tableCell}>{student.summary.H}</Text>
                </View>
                <View style={styles.tableColNarrow}>
                  <Text style={styles.tableCell}>{student.summary.I}</Text>
                </View>
                <View style={styles.tableColNarrow}>
                  <Text style={styles.tableCell}>{student.summary.S}</Text>
                </View>
                <View style={styles.tableColNarrow}>
                  <Text style={styles.tableCell}>{student.summary.A}</Text>
                </View>
                <View style={styles.tableColNarrow}>
                  <Text style={styles.tableCell}>{student.summary.percentage.toFixed(1)}%</Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Summary */}
        <View style={styles.summary}>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Total Hadir</Text>
            <Text style={[styles.summaryValue, { color: statusColors.H }]}>
              {data.summary.statusBreakdown.H}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Total Izin</Text>
            <Text style={[styles.summaryValue, { color: statusColors.I }]}>
              {data.summary.statusBreakdown.I}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Total Sakit</Text>
            <Text style={[styles.summaryValue, { color: statusColors.S }]}>
              {data.summary.statusBreakdown.S}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Total Alpa</Text>
            <Text style={[styles.summaryValue, { color: statusColors.A }]}>
              {data.summary.statusBreakdown.A}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Tingkat Kehadiran</Text>
            <Text style={styles.summaryValue}>
              {data.summary.overallAttendanceRate.toFixed(1)}%
            </Text>
          </View>
        </View>

        {/* Signature */}
        <View style={styles.signature}>
          <Text style={styles.signatureText}>
            {data.school.name}, {formatDate(new Date().toISOString())}
          </Text>
          <Text style={styles.signatureText}>Guru Mata Pelajaran</Text>
          {data.school.signature && (
            <Image style={{ width: 80, height: 40, marginBottom: 10 }} src={data.school.signature} />
          )}
          <Text style={styles.signatureName}>{data.teacher.name}</Text>
        </View>

        {/* Footer */}
        <Text style={styles.footer}>
          Laporan ini dibuat secara otomatis oleh sistem GuruFlow pada {formatDate(new Date().toISOString())}
        </Text>
      </Page>
    </Document>
  );
};

// Student Report Card PDF
interface StudentReportData {
  school: {
    name: string;
    logo?: string;
  };
  student: {
    name: string;
    studentId: string;
    className: string;
    grade: string;
  };
  period: {
    semester: string;
    academicYear: string;
  };
  subjects: Array<{
    name: string;
    teacher: string;
    grades: Array<{
      type: string;
      score: number;
      maxScore: number;
      date: string;
    }>;
    finalGrade: number;
    letterGrade: string;
    attendanceRate: number;
  }>;
  attendance: {
    totalDays: number;
    present: number;
    excused: number;
    sick: number;
    absent: number;
    rate: number;
  };
  gamification: {
    totalXP: number;
    level: number;
    badges: Array<{
      name: string;
      description: string;
      earnedAt: string;
    }>;
  };
}

export const StudentReportPDF: React.FC<{ data: StudentReportData }> = ({ data }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          {data.school.logo && (
            <Image style={styles.logo} src={data.school.logo} />
          )}
          <View style={styles.headerText}>
            <Text style={styles.schoolName}>{data.school.name}</Text>
            <Text style={styles.reportTitle}>Rapor Siswa</Text>
            <Text style={styles.reportDate}>
              {data.period.semester} - {data.period.academicYear}
            </Text>
          </View>
        </View>

        {/* Student Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informasi Siswa</Text>
          <View style={styles.infoGrid}>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Nama Lengkap</Text>
              <Text style={styles.infoValue}>{data.student.name}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>NIS</Text>
              <Text style={styles.infoValue}>{data.student.studentId}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Kelas</Text>
              <Text style={styles.infoValue}>{data.student.className}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Tingkat</Text>
              <Text style={styles.infoValue}>{data.student.grade}</Text>
            </View>
          </View>
        </View>

        {/* Academic Performance */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Prestasi Akademik</Text>
          <View style={styles.table}>
            <View style={[styles.tableRow, styles.tableHeader]}>
              <View style={styles.tableColWide}>
                <Text style={styles.tableCellHeader}>Mata Pelajaran</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>Guru</Text>
              </View>
              <View style={styles.tableColNarrow}>
                <Text style={styles.tableCellHeader}>Nilai</Text>
              </View>
              <View style={styles.tableColNarrow}>
                <Text style={styles.tableCellHeader}>Huruf</Text>
              </View>
              <View style={styles.tableColNarrow}>
                <Text style={styles.tableCellHeader}>Kehadiran</Text>
              </View>
            </View>
            {data.subjects.map((subject, index) => (
              <View key={index} style={styles.tableRow}>
                <View style={styles.tableColWide}>
                  <Text style={styles.tableCellLeft}>{subject.name}</Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={styles.tableCellLeft}>{subject.teacher}</Text>
                </View>
                <View style={styles.tableColNarrow}>
                  <Text style={styles.tableCell}>{subject.finalGrade}</Text>
                </View>
                <View style={styles.tableColNarrow}>
                  <Text style={styles.tableCell}>{subject.letterGrade}</Text>
                </View>
                <View style={styles.tableColNarrow}>
                  <Text style={styles.tableCell}>{subject.attendanceRate.toFixed(1)}%</Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Attendance Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ringkasan Kehadiran</Text>
          <View style={styles.summary}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Total Hari</Text>
              <Text style={styles.summaryValue}>{data.attendance.totalDays}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Hadir</Text>
              <Text style={[styles.summaryValue, { color: statusColors.H }]}>
                {data.attendance.present}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Izin</Text>
              <Text style={[styles.summaryValue, { color: statusColors.I }]}>
                {data.attendance.excused}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Sakit</Text>
              <Text style={[styles.summaryValue, { color: statusColors.S }]}>
                {data.attendance.sick}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Alpa</Text>
              <Text style={[styles.summaryValue, { color: statusColors.A }]}>
                {data.attendance.absent}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Persentase</Text>
              <Text style={styles.summaryValue}>{data.attendance.rate.toFixed(1)}%</Text>
            </View>
          </View>
        </View>

        {/* Gamification */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Pencapaian & Penghargaan</Text>
          <View style={styles.infoGrid}>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Total XP</Text>
              <Text style={styles.infoValue}>{data.gamification.totalXP.toLocaleString('id-ID')} XP</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Level</Text>
              <Text style={styles.infoValue}>Level {data.gamification.level}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Total Badge</Text>
              <Text style={styles.infoValue}>{data.gamification.badges.length} badge</Text>
            </View>
          </View>
          
          {data.gamification.badges.length > 0 && (
            <View style={{ marginTop: 10 }}>
              <Text style={[styles.infoLabel, { marginBottom: 5 }]}>Badge yang Diperoleh:</Text>
              {data.gamification.badges.map((badge, index) => (
                <View key={index} style={{ marginBottom: 3 }}>
                  <Text style={[styles.infoValue, { fontSize: 10 }]}>
                    • {badge.name} - {badge.description}
                  </Text>
                  <Text style={[styles.infoLabel, { fontSize: 8 }]}>
                    Diperoleh: {formatDate(badge.earnedAt)}
                  </Text>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Footer */}
        <Text style={styles.footer}>
          Rapor ini dibuat secara otomatis oleh sistem GuruFlow pada {formatDate(new Date().toISOString())}
        </Text>
      </Page>
    </Document>
  );
};