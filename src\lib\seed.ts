import { db } from './db';
import { badges } from '../db/schema';

/**
 * Seed initial data into the database
 * This includes default badges and other system data
 */
export async function seedDatabase() {
  try {
    console.log('🌱 Seeding database with initial data...');

    // Seed default badges for gamification
    const defaultBadges = [
      {
        name: '<PERSON><PERSON>',
        description: 'Hadir 30 hari berturut-turut',
        icon: 'attendance-streak',
        criteria: JSON.stringify({
          type: 'attendance_streak',
          days: 30,
          status: 'H'
        }),
        xpReward: 100
      },
      {
        name: 'Siswa Teladan',
        description: 'Mendapat nilai A dalam 5 mata pelajaran',
        icon: 'excellent-student',
        criteria: JSON.stringify({
          type: 'grade_achievement',
          count: 5,
          minScore: 90
        }),
        xpReward: 200
      },
      {
        name: 'Pengumpul Tugas',
        description: 'Mengumpulkan 10 tugas tepat waktu',
        icon: 'assignment-master',
        criteria: JSON.stringify({
          type: 'assignment_completion',
          count: 10,
          onTime: true
        }),
        xpReward: 150
      },
      {
        name: '<PERSON><PERSON><PERSON> Kelas',
        description: '<PERSON><PERSON><PERSON> yang pertama mengumpulkan tugas 5 kali',
        icon: 'class-leader',
        criteria: JSON.stringify({
          type: 'first_submission',
          count: 5
        }),
        xpReward: 75
      },
      {
        name: 'Konsisten',
        description: 'Hadir setiap hari dalam satu bulan',
        icon: 'perfect-attendance',
        criteria: JSON.stringify({
          type: 'monthly_perfect_attendance',
          month: 1
        }),
        xpReward: 250
      }
    ];

    // Insert badges if they don't exist
    for (const badge of defaultBadges) {
      await db.insert(badges).values(badge).onConflictDoNothing();
    }

    console.log('✅ Database seeding completed successfully');
    return true;

  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    throw new Error(`Seeding failed: ${error}`);
  }
}

/**
 * Reset database (development only)
 * WARNING: This will delete all data
 */
export async function resetDatabase() {
  if (process.env.NODE_ENV === 'production') {
    throw new Error('Database reset is not allowed in production');
  }

  try {
    console.log('🔄 Resetting database...');
    
    // Note: In a real implementation, you would drop and recreate tables
    // This is a placeholder for the reset functionality
    console.log('⚠️  Database reset functionality needs to be implemented');
    
    return true;
  } catch (error) {
    console.error('❌ Database reset failed:', error);
    throw new Error(`Reset failed: ${error}`);
  }
}

export default { seedDatabase, resetDatabase };