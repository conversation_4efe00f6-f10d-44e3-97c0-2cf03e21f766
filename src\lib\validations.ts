import { z } from 'zod';
import { ATTENDANCE_CODES, SCHOOL_LEVELS, USER_ROLES, GRADE_TYPES } from './constants';

/**
 * Authentication validation schemas
 */
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, 'Email wajib diisi')
    .email('Format email tidak valid'),
  password: z
    .string()
    .min(1, 'Password wajib diisi')
    .min(8, 'Password minimal 8 karakter'),
});

export const registerSchema = z.object({
  name: z
    .string()
    .min(1, 'Nama wajib diisi')
    .min(2, 'Nama minimal 2 karakter')
    .max(100, 'Nama maksimal 100 karakter'),
  email: z
    .string()
    .min(1, 'Email wajib diisi')
    .email('Format email tidak valid'),
  password: z
    .string()
    .min(8, 'Password minimal 8 karakter')
    .regex(/[A-Z]/, 'Password harus mengandung huruf besar')
    .regex(/[a-z]/, 'Password harus mengandung huruf kecil')
    .regex(/[0-9]/, 'Password harus mengandung angka'),
  confirmPassword: z.string().min(1, 'Konfirmasi password wajib diisi'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Password tidak cocok',
  path: ['confirmPassword'],
});

/**
 * School onboarding validation schema
 */
export const schoolOnboardingSchema = z.object({
  schoolName: z
    .string()
    .min(1, 'Nama sekolah wajib diisi')
    .min(3, 'Nama sekolah minimal 3 karakter')
    .max(200, 'Nama sekolah maksimal 200 karakter'),
  schoolLevel: z
    .enum(Object.keys(SCHOOL_LEVELS) as [keyof typeof SCHOOL_LEVELS, ...Array<keyof typeof SCHOOL_LEVELS>])
    .refine((val) => val in SCHOOL_LEVELS, 'Jenjang sekolah tidak valid'),
  teacherName: z
    .string()
    .min(1, 'Nama guru wajib diisi')
    .min(2, 'Nama guru minimal 2 karakter')
    .max(100, 'Nama guru maksimal 100 karakter'),
  teacherEmail: z
    .string()
    .min(1, 'Email guru wajib diisi')
    .email('Format email tidak valid'),
  teacherPassword: z
    .string()
    .min(8, 'Password minimal 8 karakter')
    .regex(/[A-Z]/, 'Password harus mengandung huruf besar')
    .regex(/[a-z]/, 'Password harus mengandung huruf kecil')
    .regex(/[0-9]/, 'Password harus mengandung angka'),
});

/**
 * User management validation schemas
 */
export const userSchema = z.object({
  name: z
    .string()
    .min(1, 'Nama wajib diisi')
    .min(2, 'Nama minimal 2 karakter')
    .max(100, 'Nama maksimal 100 karakter'),
  email: z
    .string()
    .min(1, 'Email wajib diisi')
    .email('Format email tidak valid'),
  role: z
    .enum(Object.keys(USER_ROLES) as [keyof typeof USER_ROLES, ...Array<keyof typeof USER_ROLES>])
    .refine((val) => val in USER_ROLES, 'Role tidak valid'),
  isActive: z.boolean().default(true),
});

export const updateUserSchema = userSchema.partial().extend({
  id: z.number().positive('ID user tidak valid'),
});

/**
 * Classroom validation schemas
 */
export const classroomSchema = z.object({
  name: z
    .string()
    .min(1, 'Nama kelas wajib diisi')
    .min(2, 'Nama kelas minimal 2 karakter')
    .max(50, 'Nama kelas maksimal 50 karakter'),
  grade: z
    .string()
    .min(1, 'Tingkat kelas wajib diisi')
    .max(20, 'Tingkat kelas maksimal 20 karakter'),
  academicYear: z
    .string()
    .min(1, 'Tahun ajaran wajib diisi')
    .regex(/^\d{4}\/\d{4}$/, 'Format tahun ajaran tidak valid (contoh: 2024/2025)'),
  homeroomTeacherId: z.number().positive().optional(),
  isActive: z.boolean().default(true),
});

export const updateClassroomSchema = classroomSchema.partial().extend({
  id: z.number().positive('ID kelas tidak valid'),
});

/**
 * Subject validation schemas
 */
export const subjectSchema = z.object({
  name: z
    .string()
    .min(1, 'Nama mata pelajaran wajib diisi')
    .min(2, 'Nama mata pelajaran minimal 2 karakter')
    .max(100, 'Nama mata pelajaran maksimal 100 karakter'),
  code: z
    .string()
    .min(1, 'Kode mata pelajaran wajib diisi')
    .min(2, 'Kode mata pelajaran minimal 2 karakter')
    .max(10, 'Kode mata pelajaran maksimal 10 karakter')
    .regex(/^[A-Z0-9]+$/, 'Kode harus huruf besar dan angka saja'),
  description: z.string().max(500, 'Deskripsi maksimal 500 karakter').optional(),
});

export const updateSubjectSchema = subjectSchema.partial().extend({
  id: z.number().positive('ID mata pelajaran tidak valid'),
});

/**
 * Student validation schemas
 */
export const studentSchema = z.object({
  userId: z.number().positive('ID user tidak valid'),
  studentId: z
    .string()
    .min(1, 'NIS wajib diisi')
    .min(6, 'NIS minimal 6 digit')
    .max(10, 'NIS maksimal 10 digit')
    .regex(/^\d+$/, 'NIS harus berupa angka'),
  classroomId: z.number().positive().optional(),
  enrollmentDate: z
    .string()
    .min(1, 'Tanggal masuk wajib diisi')
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Format tanggal tidak valid (YYYY-MM-DD)'),
  isActive: z.boolean().default(true),
});

export const updateStudentSchema = studentSchema.partial().extend({
  id: z.number().positive('ID siswa tidak valid'),
});

/**
 * Attendance validation schemas
 */
export const attendanceRecordSchema = z.object({
  studentId: z.number().positive('ID siswa tidak valid'),
  classroomId: z.number().positive('ID kelas tidak valid'),
  date: z
    .string()
    .min(1, 'Tanggal wajib diisi')
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Format tanggal tidak valid (YYYY-MM-DD)'),
  status: z
    .enum(Object.keys(ATTENDANCE_CODES) as [keyof typeof ATTENDANCE_CODES, ...Array<keyof typeof ATTENDANCE_CODES>])
    .refine((val) => val in ATTENDANCE_CODES, 'Status absensi tidak valid'),
  notes: z.string().max(200, 'Catatan maksimal 200 karakter').optional(),
  recordedBy: z.number().positive('ID perekam tidak valid'),
});

export const bulkAttendanceSchema = z.object({
  classroomId: z.number().positive('ID kelas tidak valid'),
  date: z
    .string()
    .min(1, 'Tanggal wajib diisi')
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Format tanggal tidak valid (YYYY-MM-DD)'),
  records: z.array(z.object({
    studentId: z.number().positive('ID siswa tidak valid'),
    status: z
      .enum(Object.keys(ATTENDANCE_CODES) as [keyof typeof ATTENDANCE_CODES, ...Array<keyof typeof ATTENDANCE_CODES>])
      .refine((val) => val in ATTENDANCE_CODES, 'Status absensi tidak valid'),
    notes: z.string().max(200, 'Catatan maksimal 200 karakter').optional(),
  })).min(1, 'Minimal satu record absensi diperlukan'),
  recordedBy: z.number().positive('ID perekam tidak valid'),
});

/**
 * Assignment validation schemas
 */
export const assignmentSchema = z.object({
  title: z
    .string()
    .min(1, 'Judul tugas wajib diisi')
    .min(3, 'Judul tugas minimal 3 karakter')
    .max(200, 'Judul tugas maksimal 200 karakter'),
  description: z.string().max(2000, 'Deskripsi maksimal 2000 karakter').optional(),
  classroomSubjectId: z.number().positive('ID kelas-mata pelajaran tidak valid'),
  dueDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/, 'Format tanggal tidak valid (YYYY-MM-DD HH:MM:SS)')
    .optional(),
  maxScore: z
    .number()
    .min(1, 'Nilai maksimal minimal 1')
    .max(1000, 'Nilai maksimal maksimal 1000')
    .default(100),
  isActive: z.boolean().default(true),
  createdBy: z.number().positive('ID pembuat tidak valid'),
});

export const updateAssignmentSchema = assignmentSchema.partial().extend({
  id: z.number().positive('ID tugas tidak valid'),
});

/**
 * Submission validation schemas
 */
export const submissionSchema = z.object({
  assignmentId: z.number().positive('ID tugas tidak valid'),
  studentId: z.number().positive('ID siswa tidak valid'),
  content: z.string().max(5000, 'Konten maksimal 5000 karakter').optional(),
  fileUrl: z.string().url('URL file tidak valid').optional(),
});

export const updateSubmissionSchema = submissionSchema.partial().extend({
  id: z.number().positive('ID pengumpulan tidak valid'),
});

/**
 * Grade validation schemas
 */
export const gradeSchema = z.object({
  submissionId: z.number().positive().optional(),
  studentId: z.number().positive('ID siswa tidak valid'),
  classroomSubjectId: z.number().positive('ID kelas-mata pelajaran tidak valid'),
  score: z
    .number()
    .min(0, 'Nilai tidak boleh negatif')
    .max(1000, 'Nilai maksimal 1000'),
  maxScore: z
    .number()
    .min(1, 'Nilai maksimal minimal 1')
    .max(1000, 'Nilai maksimal maksimal 1000'),
  gradeType: z
    .enum(Object.keys(GRADE_TYPES) as [keyof typeof GRADE_TYPES, ...Array<keyof typeof GRADE_TYPES>])
    .refine((val) => val in GRADE_TYPES, 'Tipe nilai tidak valid'),
  gradedBy: z.number().positive('ID penilai tidak valid'),
});

export const updateGradeSchema = gradeSchema.partial().extend({
  id: z.number().positive('ID nilai tidak valid'),
});

/**
 * File upload validation schema
 */
export const fileUploadSchema = z.object({
  file: z
    .instanceof(File)
    .refine((file) => file.size <= 10 * 1024 * 1024, 'Ukuran file maksimal 10MB')
    .refine(
      (file) => [
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      ].includes(file.type),
      'Tipe file tidak didukung'
    ),
});

/**
 * Search and filter validation schemas
 */
export const searchSchema = z.object({
  query: z.string().max(100, 'Query pencarian maksimal 100 karakter').optional(),
  page: z.number().min(1, 'Halaman minimal 1').default(1),
  limit: z.number().min(1, 'Limit minimal 1').max(100, 'Limit maksimal 100').default(20),
  sortBy: z.string().max(50, 'Sort by maksimal 50 karakter').optional(),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

export const dateRangeSchema = z.object({
  startDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Format tanggal mulai tidak valid (YYYY-MM-DD)'),
  endDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Format tanggal akhir tidak valid (YYYY-MM-DD)'),
}).refine((data) => new Date(data.startDate) <= new Date(data.endDate), {
  message: 'Tanggal mulai harus sebelum tanggal akhir',
  path: ['endDate'],
});

/**
 * Type exports for use in components
 */
export type LoginInput = z.infer<typeof loginSchema>;
export type RegisterInput = z.infer<typeof registerSchema>;
export type SchoolOnboardingInput = z.infer<typeof schoolOnboardingSchema>;
export type UserInput = z.infer<typeof userSchema>;
export type ClassroomInput = z.infer<typeof classroomSchema>;
export type SubjectInput = z.infer<typeof subjectSchema>;
export type StudentInput = z.infer<typeof studentSchema>;
export type AttendanceRecordInput = z.infer<typeof attendanceRecordSchema>;
export type BulkAttendanceInput = z.infer<typeof bulkAttendanceSchema>;
export type AssignmentInput = z.infer<typeof assignmentSchema>;
export type SubmissionInput = z.infer<typeof submissionSchema>;
export type GradeInput = z.infer<typeof gradeSchema>;
export type SearchInput = z.infer<typeof searchSchema>;
export type DateRangeInput = z.infer<typeof dateRangeSchema>;